.checkbox-item {
  margin-left: 20px;
}

/* Advanced Hierarchy Styles */
.hierarchy-controls {
  background: var(--surface);
  border: 1px solid var(--surface-border);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin-bottom: 2rem;

  .control-group {
    margin-bottom: 1rem;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .control-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;

    h5 {
      margin: 0;
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--text-primary);
    }

    .selection-count {
      font-size: 0.9rem;
      color: var(--text-secondary);
      background: var(--surface-variant);
      padding: 0.25rem 0.75rem;
      border-radius: 1rem;
      font-weight: 500;
    }
  }

  .button-group {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
  }

  .control-btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;

    &.primary {
      background: var(--primary-color);
      color: white;

      &:hover {
        background: var(--primary-color-dark);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }
    }

    &.secondary {
      background: var(--surface-variant);
      color: var(--text-primary);
      border: 1px solid var(--surface-border);

      &:hover {
        background: var(--surface-hover);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    }

    &:active {
      transform: translateY(0);
    }
  }

  .selected-summary {
    margin-top: 1rem;
    padding: 1rem;
    background: var(--surface-variant);
    border-radius: var(--border-radius);
    border-left: 3px solid var(--primary-color);

    h6 {
      margin: 0 0 0.5rem 0;
      font-size: 0.875rem;
      font-weight: 600;
      color: var(--text-primary);
    }

    .selected-list {
      font-size: 0.8rem;
      line-height: 1.4;
      color: var(--text-secondary);

      .selected-item {
        font-weight: 500;
      }
    }
  }
}

.advanced-hierarchy-tree {
  border: 1px solid var(--surface-border);
  border-radius: var(--border-radius);
  background: var(--surface);
  overflow: hidden;

  .checkbox-node {
    border-bottom: 1px solid var(--surface-border);

    &:last-child {
      border-bottom: none;
    }

    &.level-0 {
      .node-header {
        background: linear-gradient(
          135deg,
          var(--surface-variant) 0%,
          var(--surface) 100%
        );
        border-left: 4px solid var(--primary-color);
      }
    }

    &.level-1 {
      .node-header {
        background: var(--surface-variant);
        border-left: 3px solid var(--secondary-color);
      }

      .checkbox-with-controls {
        padding-left: 2rem;
      }
    }

    &.level-2 {
      .node-header {
        background: var(--surface);
        border-left: 2px solid var(--accent-color);
      }

      .checkbox-with-controls {
        padding-left: 4rem;
      }
    }

    .node-header {
      padding: 0.75rem 1rem;
      transition: background-color 0.2s ease;

      &:hover {
        background: var(--surface-hover);
      }
    }

    .node-leaf {
      padding: 0.5rem 1rem 0.5rem 6rem;
      background: var(--surface);

      &:hover {
        background: var(--surface-hover);
      }
    }

    .checkbox-with-controls {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 1rem;

      .level-controls {
        display: flex;
        gap: 0.5rem;
        flex-shrink: 0;
      }
    }

    .mini-btn {
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
      border: none;
      border-radius: 0.25rem;
      cursor: pointer;
      font-weight: 500;
      transition: all 0.2s ease;
      min-width: 0;
      white-space: nowrap;

      &.select {
        background: rgba(var(--rgb-success), 0.1);
        color: var(--success-color);
        border: 1px solid rgba(var(--rgb-success), 0.2);

        &:hover {
          background: rgba(var(--rgb-success), 0.2);
          transform: translateY(-1px);
          box-shadow: 0 1px 4px rgba(var(--rgb-success), 0.3);
        }
      }

      &.deselect {
        background: rgba(var(--rgb-error), 0.1);
        color: var(--error-color);
        border: 1px solid rgba(var(--rgb-error), 0.2);

        &:hover {
          background: rgba(var(--rgb-error), 0.2);
          transform: translateY(-1px);
          box-shadow: 0 1px 4px rgba(var(--rgb-error), 0.3);
        }
      }

      &:active {
        transform: translateY(0);
      }
    }

    .children-container {
      border-top: 1px solid var(--surface-border);
    }
  }
}

// /* Preview and code example */
.code-example {
  margin-top: 1.5rem;
  // border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  overflow: hidden;

  .example-preview {
    padding: 2rem;
    // background-color: var(--surface);
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    align-items: center;
    width: 100%;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: flex-start;
    }
  }

  .code-block {
    padding: 1.5rem;
    // background-color: var(--surface);
    // border-top: 1px solid var(--border-color);
    position: relative;
    overflow-x: auto;

    pre {
      margin: 0;
      overflow-x: auto;
      white-space: pre;
      font-family: "Fira Code", monospace;
      font-size: 0.9rem;
      line-height: 1.5;
      color: var(--text-color-primary);
    }

    .copy-button {
      position: absolute;
      top: 1rem;
      right: 1rem;
      background: transparent;
      border: none;
      cursor: pointer;
      color: var(--text-color-secondary);

      &:hover {
        color: var(--primary-color);
      }
    }
  }
}

:host {
  display: block;
  width: 100%;
}

/* Prevent horizontal scroll */
html,
body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
}

* {
  box-sizing: border-box;
}

/* Main layout */
.documentation {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
  overflow-x: hidden;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

/* Navigation Links */
.nav-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  text-decoration: none;
  color: #374151;
  transition: all 0.2s ease;
  font-weight: 500;

  &:hover {
    background: #f9fafb;
    border-color: #d1d5db;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }

  .nav-icon {
    font-size: 1.25rem;
    width: 24px;
    text-align: center;
  }

  .nav-text {
    font-size: 0.9rem;
  }
}

/* Header */
.doc-header {
  h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
  }

  .description {
    font-size: 1.1rem;
    line-height: 1.6;
  }
}

/* Sections */
.doc-sections {
  margin-top: 4rem;
}

.doc-section {
  margin-bottom: 1rem;

  h2 {
    font-size: 1.8rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
  }

  p {
    font-size: 0.95rem;
    line-height: 1.5;
  }
}

/* Section header with toggle */
.section-header {
  display: flex;
  flex-direction: column;
  position: relative;
  cursor: pointer;
  padding: 1rem;
  background-color: var(--surface);
  border-radius: var(--border-radius);

  h2 {
    margin-bottom: 0.5rem;
  }

  .description-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .code-toggle {
    font-size: 0.75rem;
    color: var(--icons-action);
    cursor: pointer;
    display: flex;
    align-items: center;
    font-weight: var(--font-font-weight-medium);
    font-family: var(--font-font-family-heading);

    &:hover {
      text-decoration: underline;
    }

    span {
      margin-right: 0.5rem;
    }

    awe-icons {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      line-height: 0;
      padding: 0;
      margin: 0;
      vertical-align: middle;
      flex-shrink: 0;

      svg {
        width: 60%;
        height: 80%;
        display: block;
      }
    }
  }
}

/* Code example styles */
.code-example {
  margin-top: 1.5rem;

  .example-preview {
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    border: 1px solid var(--surface-border);
  }

  .code-block {
    position: relative;
    border-radius: 0.5rem;
    margin-top: 1rem;
    border: 1px solid var(--surface-border);
    background-color: var(--surface-ground);

    pre {
      margin: 0;
      padding: 1rem;
      border-radius: 0.25rem;
      overflow-x: auto;
    }

    .copy-button {
      position: absolute;
      top: 0.5rem;
      right: 0.5rem;
      padding: 0.5rem;
      background: transparent;
      border: none;
      cursor: pointer;
      color: var(--text-color-secondary);

      &:hover {
        color: var(--primary-color);
      }
    }
  }
}

/* API table styles */
.api-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;

  th,
  td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--surface-border);
  }

  th {
    background-color: var(--surface);
    font-weight: 600;
    color: var(--text-color-primary);
  }

  td {
    code {
      background-color: var(--surface);
      padding: 0.2rem 0.4rem;
      border-radius: var(--border-radius-sm);
      font-family: monospace;
    }
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .documentation {
    padding: 1rem;
  }
}

.ava-checkbox {
  margin-bottom: 10px;
}

.horizontal-checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}
