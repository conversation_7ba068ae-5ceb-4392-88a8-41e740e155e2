import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CheckboxComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-sizes-demo',
  standalone: true,
  imports: [CommonModule, CheckboxComponent],
  template: `
    <div class="demo-section center-demo">
      <div class="sizes-grid">
        <div class="size-group">
          <div class="checkbox-examples">
            <ava-checkbox
              label="Small"
              size="small"
              variant="default"
              [(isChecked)]="smallStates.default"
              (isCheckedChange)="onCheckboxChange($event)"
            >
            </ava-checkbox>
            <ava-checkbox
              label="Medium"
              size="medium"
              variant="default"
              [(isChecked)]="mediumStates.default"
              (isCheckedChange)="onCheckboxChange($event)"
            >
            </ava-checkbox>
            <ava-checkbox
              label="Large"
              size="large"
              variant="default"
              [(isChecked)]="largeStates.default"
              (isCheckedChange)="onCheckboxChange($event)"
            >
            </ava-checkbox>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .center-demo {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 60vh;
      }
      .demo-section {
        margin-bottom: 2rem;
        padding: 2rem;
        margin-top: 0;
        max-width: 900px;
        margin-left: auto;
        margin-right: auto;
      }
      .description {
        color: #666;
        margin-bottom: 2rem;
        text-align: center;
      }
      .sizes-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
      }
      .size-group {
        padding: 1.5rem;
        margin-top: 0;
      }
      .size-group h4 {
        margin: 0 0 0.5rem 0;
        color: #1f2937;
        font-size: 1.1rem;
      }
      .size-description {
        color: #6b7280;
        font-size: 0.9rem;
        margin-bottom: 1rem;
        line-height: 1.4;
      }
      .checkbox-examples {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        align-items: left;
      }
    `,
  ],
})
export class SizesDemoComponent {
  smallStates = {
    default: false,
    withBg: true,
    animated: false,
  };

  mediumStates = {
    default: true,
    withBg: false,
    animated: true,
  };

  largeStates = {
    default: false,
    withBg: true,
    animated: false,
  };

  onCheckboxChange(checked: boolean) {
    console.log('Checkbox changed:', checked);
  }
}
