import { Component, ElementRef, HostListener, ViewChild, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { PromptBarComponent } from '../../../../../play-comp-library/src/lib/components/prompt-bar/prompt-bar.component';
import { FileAttachPillComponent, FileAttachOption } from '../../../../../play-comp-library/src/lib/components/file-attach-pill/file-attach-pill.component';
import { IconPillComponent, IconOption } from '../../../../../play-comp-library/src/lib/components/icon-pill/icon-pill.component';
import { IconsComponent } from '../../../../../play-comp-library/src/lib/components/icons/icons.component';

type IconStatus = 'default' | 'active' | 'disable';

// Interface for selected files
interface SelectedFile {
  id: string;
  name: string;
  url: string;
  type: string;
}

@Component({
  selector: 'awe-app-promptbar',
  standalone: true,
  imports: [CommonModule, FormsModule, PromptBarComponent, FileAttachPillComponent, IconPillComponent, IconsComponent],
  templateUrl: './app-promptbar.component.html',
  styleUrls: ['./app-promptbar.component.scss'],
   encapsulation: ViewEncapsulation.None,
})
export class AppPromptbarComponent { 
  currentTheme = 'light' 

  @ViewChild('codeBlock') codeBlock!: ElementRef;

  animatedTexts = [
    '"Generate Design"',
    '"Image to App"',
    '"Design Analysis"',
    '"Review Accessibility"',
  ];

  leftIcons: { name: string; status: IconStatus }[] = [
    { name: 'awe_enhanced_alternate', status: 'default' },
    { name: 'awe_figma', status: 'disable' },
  ];

  rightIcons: { name: string; status: IconStatus }[] = [
    { name: 'awe_enhance', status: 'active' },
    { name: 'awe_enhanced_send', status: 'active' },
  ];  // Add property for two-way binding demo
  promptText = '';
  showDesignSystemContainer = false;
  originalText = ''; // Store original text before enhancement
  isTextEnhanced = false; // Track if text has been enhanced

  // File handling properties
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;
  selectedFiles: SelectedFile[] = [];
  showPreview = false;
  previewFile: SelectedFile | null = null;
  fileError = '';
  readonly acceptedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
  readonly maxAllowedFiles = 1; // Maximum number of files allowed
  isFileAttachDisabled = false; // Track if file attachment is disabled

  // File attachment options
  fileOptions: FileAttachOption[] = [
    { name: 'From Computer', icon: 'awe_upload', value: 'computer' },
    { name: 'From Cloud', icon: 'awe_cloud_upload', value: 'cloud' },
    { name: 'From URL', icon: 'awe_link', value: 'url' }
  ];

  // Tech selection options
  techOptions: IconOption[] = [
    { name: 'React', icon: '/assets/icons/awe_react.svg', value: 'react', isLocalSvg: true },
    { name: 'Angular', icon: '/assets/icons/awe_angular.svg', value: 'angular', isLocalSvg: true },
    { name: 'Vue', icon: '/assets/icons/awe_vue.svg', value: 'vue', isLocalSvg: true }
  ];
  selectedTech: IconOption = this.techOptions[0];

  // Design library options
  designOptions: IconOption[] = [
    { name: 'Tailwind', icon: '/assets/icons/awe_tailwind.svg', value: 'tailwind', isLocalSvg: true },
    { name: 'Material UI', icon: '/assets/icons/awe_material.svg', value: 'material', isLocalSvg: true },
    { name: 'Bootstrap', icon: '/assets/icons/awe_bootstrap.svg', value: 'bootstrap', isLocalSvg: true },
    { name: 'Custom', icon: '/assets/icons/awe_custom.svg', value: 'custom', isLocalSvg: true }
  ];
  selectedDesign: IconOption = this.designOptions[0];

  
  handleIconClick(event: { name: string; side: string; index: number }): void {
    const normalizedIconName = event.name.toLowerCase();

    switch (normalizedIconName) {
      case 'awe_enhanced_alternate':
        // File handling is now managed by the prompt-bar component
        break;
      case 'awe_figma':
        this.handleFigma();
        break;
      case 'awe_enhance':
        this.handleEnhanceText();
        break;
      case 'awe_enhanced_send':
        this.handleEnhancedSend();
        break;
      default:
        console.warn('Unknown icon clicked:', event.name);
    }
  }

  onTextValueChange(text: string) {
    console.log('Text changed:', text);
  }

  customAction() {
    // Custom action implementation
  }
  handleEnhancedAlternate(): void {
    // Check if file attachment is disabled
    if (this.isFileAttachDisabled) {
      this.fileError = `Only ${this.maxAllowedFiles} image can be uploaded at a time`;
      return;
    }

    // Reset any previous errors
    this.fileError = '';
    
    // Use the file input element
    if (this.fileInput && this.fileInput.nativeElement) {
      this.fileInput.nativeElement.click();
    } else {
      console.error('File input element not found');
    }
  }
  onFileSelected(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (!input.files || input.files.length === 0) {
      return;
    }

    // Reset previous error
    this.fileError = '';

    // Check if max files already reached
    if (this.isMaxFilesReached()) {
      this.fileError = `Only ${this.maxAllowedFiles} image can be uploaded at a time`;
      input.value = ''; // Reset input
      return;
    }

    const file = input.files[0]; // We only handle one file at a time
    
    if (this.validateFile(file)) {
      try {
        const url = URL.createObjectURL(file);
        const newFile: SelectedFile = {
          id: Math.random().toString(36).substring(2, 11),
          name: file.name,
          url: url,
          type: file.type
        };

        this.selectedFiles = [newFile]; // Replace existing files with new one
        this.updateFileAttachPillStatus();
        
        // Hide design system container if it was shown
        this.showDesignSystemContainer = false;

        // Emit the selected file
        this.onFilesSelected([file]);
      } catch (error) {
        console.error('Error creating file URL:', error);
        this.fileError = 'Error uploading file. Please try again.';
      }
    }

    // Always reset the input so the same file can be selected again
    input.value = '';
  }
  validateFile(file: File): boolean {
    // Reset previous error
    this.fileError = '';

    // Check if it's actually a file and not a folder
    if (!file || !file.type) {
      this.fileError = 'Invalid file selected';
      return false;
    }

    // Check if it's an image file
    if (!this.acceptedImageTypes.includes(file.type)) {
      this.fileError = 'Only image files (JPEG, PNG, GIF, WEBP, SVG) are allowed';
      return false;
    }

    // Check file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      this.fileError = 'File size must be less than 5MB';
      return false;
    }

    // Additional check for empty files
    if (file.size === 0) {
      this.fileError = 'Cannot upload empty file';
      return false;
    }

    return true;
  }

  // Add this property to your component class
  activeDevice: 'laptop' | 'mobile' = 'laptop'; // Default to laptop view

  // Add this method to your component class
  setActiveDevice(device: 'laptop' | 'mobile'): void {
    this.activeDevice = device;
    // You can add additional logic here to handle the view change
    // console.log(`Switched to ${device} view`);
  }


  // Check if maximum number of files has been reached
  isMaxFilesReached(): boolean {
    return this.selectedFiles.length >= this.maxAllowedFiles;
  }

  // Update the file attach pill status based on file count
  updateFileAttachPillStatus(): void {
    // Check if max files are reached
    const isMaxReached = this.isMaxFilesReached();

    // Disable the awe_enhanced_alternate icon if max files reached
    const uploadIconIndex = this.leftIcons.findIndex(icon => icon.name === 'awe_enhanced_alternate');
    if (uploadIconIndex !== -1) {
      this.leftIcons[uploadIconIndex].status = isMaxReached ? 'disable' : 'default';
    }

    // Update the file attach disabled state
    this.isFileAttachDisabled = isMaxReached;
  }

  handleFigma(): void {
    // console.log('Figma functionality triggered');
  }
  handleEnhanceText(): void {
    if(this.promptText) {
      // Store the original text before enhancement
      if (!this.isTextEnhanced) {
        this.originalText = this.promptText;
      }
      
      // Update the text with enhanced version
      this.promptText = "Lorem ipsum dolor sit amet consectetur adipisicing elit. Ipsum, atque voluptates, eius, porro consectetur rerum debitis quasi fugit dicta dolorum nihil asperiores? Ducimus quaerat explicabo cum perferendis tenetur excepturi. Beatae?Lorem ipsum dolor sit amet consectetur adipisicing elit. Id aliquid, est nisi esse distinctio consectetur quam deleniti asperiores tempore incidunt quo fugiat voluptate praesentium nobis deserunt nostrum aliquam laboriosam illo.lore Lorem ipsum dolor sit, amet consectetur adipisicing elit.";
      
      // Mark text as enhanced
      this.isTextEnhanced = true;
 
      // Add a small delay to ensure the DOM has updated with the new text
      setTimeout(() => {
        this.adjustTextareaHeight();
      }, 0);
    }
  }
 
  // Add this method to your AppComponent class (the component containing handleEnhanceText)
adjustTextareaHeight(): void {
  const textAreas = document.querySelectorAll('.prompt-text');
  textAreas.forEach((textArea) => {
    const htmlTextArea = textArea as HTMLTextAreaElement;
    if (htmlTextArea) {
      htmlTextArea.style.height = 'auto';
      htmlTextArea.style.height = `${htmlTextArea.scrollHeight}px`;
    }
  });
}
  handleEnhancedSend(): void {
    // If there's no image attached, show the design system container
    if (this.selectedFiles.length === 0 && this.promptText.trim() !== '') {
      this.showDesignSystemContainer = true;
    } else {
      // Handle normal send operation when image is attached
      console.log('Normal send with image');
      // Add your send implementation here
    }
  }

  handleEnterPressed(): void {
    console.log('Enter key pressed');
  }

  onFilesSelected(files: File[]): void {
    console.log('Files selected:', files);
  }

  onFileRemoved(fileId: string): void {
    console.log('File removed:', fileId);
    // Remove the file from our local array
    this.removeFile(fileId);
  }

  onFilePreviewClosed(): void {
    console.log('File preview closed');
    this.closePreview();
  }

  // Remove a file from the selected files array
  removeFile(fileId: string): void {
    this.selectedFiles = this.selectedFiles.filter(file => file.id !== fileId);
    // Update the file attach pill status after removing a file
    this.updateFileAttachPillStatus();
  }

  // Show file preview
  showFilePreview(file: SelectedFile): void {
    this.previewFile = file;
    this.showPreview = true;
  }

  // Close file preview
  closePreview(): void {
    this.showPreview = false;
    this.previewFile = null;
  }

  // Truncate file name for display
  truncateFileName(filename: string): string {
    const maxLength = 13;
    const extension = filename.slice(filename.lastIndexOf('.'));
    const nameWithoutExt = filename.slice(0, filename.lastIndexOf('.'));

    if (filename.length <= maxLength) {
      return filename;
    }

    const truncatedName = nameWithoutExt.slice(0, maxLength - extension.length - 1);
    return `${truncatedName}...${extension}`;
  }

  // File attach pill event handler
  onFileOptionSelected(option: FileAttachOption): void {
    console.log('Selected file option:', option);

    // Check if file attachment is disabled
    if (this.isFileAttachDisabled) {
      this.fileError = `Only ${this.maxAllowedFiles} image can be uploaded at a time`;
      return;
    }

    // Simulate file attachment based on the selected option
    switch (option.value) {
      case 'computer':
        this.handleEnhancedAlternate();
        break;
      case 'cloud':
        console.log('Opening cloud dialog...');
        break;
      case 'url':
        const url = prompt('Enter the URL of the file:');
        if (url) {
          console.log('File URL:', url);
          // Create a mock file for URL
          const mockFile: SelectedFile = {
            id: Math.random().toString(36).substring(2, 11),
            name: url.split('/').pop() || 'file.jpg',
            url: url,
            type: 'image/jpeg' // Assume it's an image
          };

          this.selectedFiles = [...this.selectedFiles, mockFile];
          this.updateFileAttachPillStatus();
        }
        break;
    }
  }

  // Tech pill event handler
  onTechSelected(option: IconOption): void {
    console.log('Selected tech:', option);
    this.selectedTech = option;
  }

  // Design library pill event handler
  onDesignSelected(option: IconOption): void {
    console.log('Selected design library:', option);
    this.selectedDesign = option;

    // Special handling for custom option
    if (option.value === 'custom') {
      const customTheme = prompt('Enter your custom theme name:');
      if (customTheme) {
        console.log('Custom theme:', customTheme);
      }
    }
  }
  handleDesignSystemSelection(type: 'our' | 'create'): void {
    if (type === 'our') {
      console.log('Our Design System selected');
      // Add your implementation for Our Design System here
    } else {
      console.log('Create Design System selected');
      // Add your implementation for Create Design System here
    }
    
    // Hide the container after selection
    this.showDesignSystemContainer = false;
    
    // Proceed with sending the prompt
    console.log('Sending prompt with design system:', type);
    // Add your send implementation here
  }
  handleUndo(): void {
    if (this.isTextEnhanced && this.originalText) {
      this.promptText = this.originalText;
      this.isTextEnhanced = false;
      this.originalText = '';
      
      // Adjust textarea height after text change
      setTimeout(() => {
        this.adjustTextareaHeight();
      }, 0);
    }
  }
// documentation:
@HostListener('document:click', ['$event'])
  clickOutside(event: MouseEvent) {
    if (
      this.codeBlock &&
      !this.codeBlock.nativeElement.contains(event.target)
    ) {
      this.sections.forEach((section) => (section.showCode = false));
    }
  }
  
  sections = [
  {
    title: 'Basic Usage',
    description:
      'Demonstrates the basic functionality of the prompt bar with text input and icons.',
    showCode: false,
  },
  {
    title: 'Animations',
    description: 'Shows how to use animated texts in the prompt bar.',
    showCode: false,
  },
  {
    title: 'Custom Content',
    description: 'Illustrates how to add custom content to the prompt bar.',
    showCode: false,
  },
  {
    title: 'Chat Bot Variant',
    description: 'Demonstrates the usage of the chat-bot variant of the prompt bar.',
    showCode: false,
  },
  {
    title: 'Prompt with design System',
    description: 'Demonstrates the usage of the prompt bar with design system.',
    showCode: false,
  },
  {
    title: 'Prompt without design System',
    description: 'Demonstrates the usage of the prompt bar without design system.',
    showCode: false,
  },
];

  apiProps = [
    {
      name: 'theme',
      type: "'light' | 'dark'",
      default: "'light'",
      description: 'The theme of the prompt bar.',
    },
    {
      name: 'defaultText',
      type: 'string',
      default: "''",
      description: 'The default text for the prompt bar.',
    },
    {
      name: 'leftIcons',
      type: '{ name: string, status: "active" | "default" | "disable" }[]',
      default: '[]',
      description: 'The icons displayed on the left side of the prompt bar.',
    },
    {
      name: 'rightIcons',
      type: '{ name: string, status: "active" | "default" | "disable" }[]',
      default: '[]',
      description: 'The icons displayed on the right side of the prompt bar.',
    },
    {
      name: 'animatedTexts',
      type: 'string[]',
      default: '[]',
      description: 'The texts to be animated in the prompt bar.',
    },
    {
      name: 'staticText',
      type: 'string',
      default: "''",
      description: 'The static text displayed in the prompt bar.',
    },
    {
      name: 'textValue',
      type: 'string',
      default: "''",
      description:
        'The current text value in the prompt bar (supports two-way binding).',
    },
    {
      name: 'variant',
      type: "'default' | 'chat-bot'",
      default: "'default'",
      description: 'The variant of the prompt bar.',
    },
    {
      name: 'currentTheme', 
      type: "'light' | 'dark'",
      default: "'light'",
      description: 'Current theme applied to the component and its children.',
    },
    {
      name: 'isFileAttachDisabled',
      type: 'boolean',
      default: 'false',
      description: 'Whether file attachment is disabled due to reaching maximum files.',
    },
    {
      name: 'maxAllowedFiles',
      type: 'number',
      default: '1',
      description: 'Maximum number of files that can be attached.',
    },
    {
      name: 'acceptedImageTypes',
      type: 'string[]',
      default: "['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml']",
      description: 'Array of accepted MIME types for file attachment.',
    }
  ];

  events = [
    {
      name: 'iconClicked',
      type: 'EventEmitter<{ name: string, side: string, index: number }>',
      description: 'Emitted when an icon is clicked.',
    },
    {
      name: 'textValueChange',
      type: 'EventEmitter<string>',
      description: 'Emitted when the text value changes.',
    },
    {
      name: 'enterPressed',
      type: 'EventEmitter<void>',
      description: 'Emitted when the Enter key is pressed.',
    },
    {
      name: 'filesSelected',
      type: 'EventEmitter<File[]>',
      description: 'Emitted when files are selected through the file input.',
    },
    {
      name: 'fileRemoved',
      type: 'EventEmitter<string>',
      description: 'Emitted when a file is removed with the file ID.',
    },
    {
      name: 'filePreviewClosed',
      type: 'EventEmitter<void>',
      description: 'Emitted when the file preview is closed.',
    },
    {
      name: 'optionSelected',
      type: 'EventEmitter<FileAttachOption>',
      description: 'Emitted when an option is selected from file attach pill.',
    },
    {
      name: 'selectionChange',
      type: 'EventEmitter<IconOption>',
      description: 'Emitted when a selection changes in icon pills.',
    }
  ];

  toggleSection(index: number): void {
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation(); // Prevent the click event from bubbling up to the section header
    this.sections[index].showCode = !this.sections[index].showCode;
  }
  
  copyCode(sectionTitle: string): void {
    const code = this.getExampleCode(sectionTitle);
    navigator.clipboard.writeText(code);
  }

  getExampleCode(sectionTitle: string): string {
  const examples: Record<string, string> = {
    'basic usage': `
import { Component } from '@angular/core';
import { PromptBarComponent } from '@awe/play-comp-library';
import { IconsComponent } from '@awe/play-comp-library';

@Component({
selector: 'app-basic-promptbar',
standalone: true,
imports: [PromptBarComponent, IconsComponent],
template: \`
  <awe-prompt-bar
    [theme]="'light'"
    [(textValue)]="promptText"
    defaultText="'Start creating with AI: \\"Generate Design\\"'"
    [leftIcons]="leftIcons"
    [rightIcons]="rightIcons"
    (iconClicked)="handleIconClick($event)"
    (textValueChange)="onTextValueChange($event)"
    (enterPressed)="handleEnterPressed()"
    (filesSelected)="onFilesSelected($event)"
    (fileRemoved)="onFileRemoved($event)"
    (filePreviewClosed)="onFilePreviewClosed()">
  </awe-prompt-bar>
  <awe-prompt-bar
    [theme]="'dark'"
    [(textValue)]="promptText"
    defaultText="'Start creating with AI: \\"Generate Design\\"'"
    [leftIcons]="leftIcons"
    [rightIcons]="rightIcons"
    (iconClicked)="handleIconClick($event)"
    (textValueChange)="onTextValueChange($event)"
    (enterPressed)="handleEnterPressed()"
    (filesSelected)="onFilesSelected($event)"
    (fileRemoved)="onFileRemoved($event)"
    (filePreviewClosed)="onFilePreviewClosed()">
  </awe-prompt-bar>
\`
})
export class BasicPromptBarComponent {
promptText = '';

type IconStatus = "default" | "active" | "disable";

leftIcons: { name: string; status: IconStatus }[] = [
  { name: 'awe_enhanced_alternate', status: "default" },
  { name: 'awe_figma', status: "disable" }
];

rightIcons: { name: string; status: IconStatus }[] = [
  { name: 'awe_enhance', status: "active" },
  { name: 'awe_enhanced_send', status: "active" }
];

onTextValueChange(text: string) {
  console.log('Text changed:', text);
}

handleIconClick(event: { name: string; side: string; index: number }): void {
  const normalizedIconName = event.name.toLowerCase();

  switch (normalizedIconName) {
    case "awe_enhanced_alternate":
      this.handleEnhancedAlternate();
      break;
    case "awe_figma":
      this.handleFigma();
      break;
    case "awe_enhance":
      this.handleEnhanceText();
      break;
    case "awe_enhanced_send":
      this.handleEnhancedSend();
      break;
    default:
      console.warn("Unknown icon clicked:", event.name);
  }
}

handleEnhancedAlternate(): void {
  // File handling logic is now managed by the prompt-bar component
  console.log('File upload triggered');
}

handleFigma(): void {
  console.log('Figma functionality triggered');
}

handleEnhanceText(): void {
  console.log("Enhance Text functionality triggered");
  if(this.promptText) {
    this.promptText = "Lorem ipsum dolor sit amet consectetur adipisicing elit...";
    // Adjust textarea height if needed
    setTimeout(() => this.adjustTextareaHeight(), 0);
  }
}

handleEnhancedSend(): void {
  console.log('Enhanced Send functionality triggered');
}

handleEnterPressed(): void {
  console.log('Enter key pressed');
}

onFilesSelected(files: File[]): void {
  console.log('Files selected:', files);
}

onFileRemoved(fileId: string): void {
  console.log('File removed:', fileId);
}

onFilePreviewClosed(): void {
  console.log('File preview closed');
}

adjustTextareaHeight(): void {
  const textAreas = document.querySelectorAll('.prompt-text');
  textAreas.forEach((textArea) => {
    const htmlTextArea = textArea as HTMLTextAreaElement;
    if (htmlTextArea) {
      htmlTextArea.style.height = 'auto';
      htmlTextArea.style.height = \`\${htmlTextArea.scrollHeight}px\`;
    }
  });
}
}`,

    'animations': `
import { Component } from '@angular/core';
import { PromptBarComponent } from '@awe/play-comp-library';
import { IconsComponent } from '@awe/play-comp-library';

@Component({
selector: 'app-animated-promptbar',
standalone: true,
imports: [PromptBarComponent, IconsComponent],
template: \`
  <awe-prompt-bar
    [theme]="'light'"
    [(textValue)]="promptText"
    [staticText]="'Start creating with AI:'"
    [animatedTexts]="animatedTexts"
    [leftIcons]="leftIcons"
    [rightIcons]="rightIcons"
    (iconClicked)="handleIconClick($event)"
    (textValueChange)="onTextValueChange($event)"
    (enterPressed)="handleEnterPressed()"
    (filesSelected)="onFilesSelected($event)"
    (fileRemoved)="onFileRemoved($event)"
    (filePreviewClosed)="onFilePreviewClosed()">
  </awe-prompt-bar>
  <awe-prompt-bar
    [theme]="'dark'"
    [(textValue)]="promptText"
    [staticText]="'Start creating with AI:'"
    [animatedTexts]="animatedTexts"
    [leftIcons]="leftIcons"
    [rightIcons]="rightIcons"
    (iconClicked)="handleIconClick($event)"
    (textValueChange)="onTextValueChange($event)"
    (enterPressed)="handleEnterPressed()"
    (filesSelected)="onFilesSelected($event)"
    (fileRemoved)="onFileRemoved($event)"
    (filePreviewClosed)="onFilePreviewClosed()">
  </awe-prompt-bar>
\`
})
export class AnimatedPromptBarComponent {
promptText = '';

animatedTexts = [
  '"Generate Design"',
  '"Image to App"',
  '"Design Analysis"',
  '"Review Accessibility"'
];

type IconStatus = "default" | "active" | "disable";

leftIcons: { name: string; status: IconStatus }[] = [
  { name: 'awe_enhanced_alternate', status: "default" },
  { name: 'awe_figma', status: "disable" }
];

rightIcons: { name: string; status: IconStatus }[] = [
  { name: 'awe_enhance', status: "active" },
  { name: 'awe_enhanced_send', status: "active" }
];

onTextValueChange(text: string) {
  console.log('Text changed:', text);
}

handleIconClick(event: { name: string; side: string; index: number }): void {
  // ... same as basic usage example
}

handleEnterPressed(): void {
  console.log('Enter key pressed');
}

onFilesSelected(files: File[]): void {
  console.log('Files selected:', files);
}

onFileRemoved(fileId: string): void {
  console.log('File removed:', fileId);
}

onFilePreviewClosed(): void {
  console.log('File preview closed');
}
}`,

    'custom content': `
import { Component, ViewChild, ElementRef } from '@angular/core';
import { PromptBarComponent } from '@awe/play-comp-library';
import { FileAttachPillComponent, FileAttachOption } from '@awe/play-comp-library';
import { IconPillComponent, IconOption } from '@awe/play-comp-library';
import { IconsComponent } from '@awe/play-comp-library';

// Interface for selected files
interface SelectedFile {
id: string;
name: string;
url: string;
type: string;
}

@Component({
selector: 'app-custom-promptbar',
standalone: true,
imports: [PromptBarComponent, FileAttachPillComponent, IconPillComponent, IconsComponent],
template: \`
  <awe-prompt-bar
    [theme]="'light'"
    defaultText="'Start creating with AI: \\"Generate Design\\"'"
    (enterPressed)="handleEnterPressed()"
    [(textValue)]="promptText">
    <div class="custom-content">
      <!-- Selected Files Display -->
      <div class="selected-files" *ngIf="selectedFiles.length > 0">
        <div class="file-item" *ngFor="let file of selectedFiles">
          <div class="file-preview" (click)="showFilePreview(file)">
            <img [src]="file.url" [alt]="file.name">
            <span class="file-name">{{ truncateFileName(file.name) }}</span>
          </div>
          <awe-icons
            iconName="awe_close"
            (click)="removeFile(file.id)"
            role="button"
            tabindex="0"
            iconColor="blue"
            [attr.aria-label]="'Remove ' + file.name"
          ></awe-icons>
        </div>
      </div>

      <div class="tools-container">
        <div class="pills-container">
          <awe-file-attach-pill
            [currentTheme]="'light'"
            [options]="fileOptions"
            (optionSelected)="onFileOptionSelected($event)"
            [class.disabled]="isFileAttachDisabled"
          ></awe-file-attach-pill>
          <awe-icon-pill
            [options]="techOptions"
            [selectedOption]="selectedTech"
            (selectionChange)="onTechSelected($event)"
          ></awe-icon-pill>
          <awe-icon-pill
            [options]="designOptions"
            [selectedOption]="selectedDesign"
            (selectionChange)="onDesignSelected($event)"
          ></awe-icon-pill>
        </div>
        <div class="enhance-icons">
          <awe-icons
            iconName="awe_enhance"
            (click)="handleEnhanceText()"
            role="button"
            tabindex="0"
            [attr.aria-label]="'Enhance'"
          ></awe-icons>
          <awe-icons
            iconName="awe_enhanced_send"
            (click)="handleEnhancedSend()"
            role="button"
            tabindex="0"
            [attr.aria-label]="'Enhanced Send'"
          ></awe-icons>
        </div>
      </div>
    </div>
  </awe-prompt-bar>
\`
})
export class CustomPromptBarComponent {
@ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

promptText = '';
selectedFiles: SelectedFile[] = [];
isFileAttachDisabled = false;
maxAllowedFiles = 1;
acceptedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];

// File attachment options
fileOptions: FileAttachOption[] = [
  { name: 'From Computer', icon: 'awe_upload', value: 'computer' },
  { name: 'From Cloud', icon: 'awe_cloud_upload', value: 'cloud' },
  { name: 'From URL', icon: 'awe_link', value: 'url' }
];

// Tech selection options
techOptions: IconOption[] = [
  { name: 'Angular', icon: '/assets/icons/awe_angular.svg', value: 'angular', isLocalSvg: true },
  { name: 'React', icon: '/assets/icons/awe_react.svg', value: 'react', isLocalSvg: true },
  { name: 'Vue', icon: '/assets/icons/awe_vue.svg', value: 'vue', isLocalSvg: true }
];
selectedTech: IconOption = this.techOptions[0];

// Design library options
designOptions: IconOption[] = [
  { name: 'Material UI', icon: '/assets/icons/awe_material.svg', value: 'material', isLocalSvg: true },
  { name: 'Tailwind', icon: '/assets/icons/awe_tailwind.svg', value: 'tailwind', isLocalSvg: true },
  { name: 'Bootstrap', icon: '/assets/icons/awe_bootstrap.svg', value: 'bootstrap', isLocalSvg: true },
  { name: 'Custom', icon: '/assets/icons/awe_custom.svg', value: 'custom', isLocalSvg: true }
];
selectedDesign: IconOption = this.designOptions[0];

handleEnterPressed(): void {
  console.log('Enter key pressed');
}

handleEnhanceText(): void {
  if(this.promptText) {
    this.promptText = "Lorem ipsum dolor sit amet consectetur adipisicing elit...";
    // Adjust textarea height
    setTimeout(() => this.adjustTextareaHeight(), 0);
  }
}

handleEnhancedSend(): void {
  console.log('Enhanced Send functionality triggered');
}

// File handling methods
onFileOptionSelected(option: FileAttachOption): void {
  console.log('Selected file option:', option);

  if (this.isFileAttachDisabled) {
    console.error(\`Only \${this.maxAllowedFiles} image can be uploaded at a time\`);
    return;
  }

  switch (option.value) {
    case 'computer':
      this.fileInput.nativeElement.click();
      break;
    case 'cloud':
      console.log('Opening cloud dialog...');
      break;
    case 'url':
      const url = prompt('Enter the URL of the file:');
      if (url) {
        console.log('File URL:', url);
        this.addFileFromUrl(url);
      }
      break;
  }
}

addFileFromUrl(url: string): void {
  const mockFile: SelectedFile = {
    id: Math.random().toString(36).substring(2, 11),
    name: url.split('/').pop() || 'file.jpg',
    url: url,
    type: 'image/jpeg'
  };

  this.selectedFiles = [...this.selectedFiles, mockFile];
  this.updateFileAttachStatus();
}

removeFile(fileId: string): void {
  this.selectedFiles = this.selectedFiles.filter(file => file.id !== fileId);
  this.updateFileAttachStatus();
}

showFilePreview(file: SelectedFile): void {
  console.log('Preview file:', file);
  // Implementation for preview overlay
}

truncateFileName(filename: string): string {
  const maxLength = 13;
  const extension = filename.slice(filename.lastIndexOf('.'));
  const nameWithoutExt = filename.slice(0, filename.lastIndexOf('.'));

  if (filename.length <= maxLength) return filename;

  const truncatedName = nameWithoutExt.slice(0, maxLength - extension.length - 1);
  return \`\${truncatedName}...\${extension}\`;
}

updateFileAttachStatus(): void {
  this.isFileAttachDisabled = this.selectedFiles.length >= this.maxAllowedFiles;
}

adjustTextareaHeight(): void {
  const textAreas = document.querySelectorAll('.prompt-text');
  textAreas.forEach((textArea) => {
    const htmlTextArea = textArea as HTMLTextAreaElement;
    if (htmlTextArea) {
      htmlTextArea.style.height = 'auto';
      htmlTextArea.style.height = \`\${htmlTextArea.scrollHeight}px\`;
    }
  });
}

onTechSelected(option: IconOption): void {
  console.log('Selected tech:', option);
  this.selectedTech = option;
}

onDesignSelected(option: IconOption): void {
  console.log('Selected design library:', option);
  this.selectedDesign = option;

  if (option.value === 'custom') {
    const customTheme = prompt('Enter your custom theme name:');
    if (customTheme) {
      console.log('Custom theme:', customTheme);
    }
  }
}
}`,

    'chat bot variant': `
import { Component } from '@angular/core';
import { PromptBarComponent } from '@awe/play-comp-library';
import { IconsComponent } from '@awe/play-comp-library';

@Component({
selector: 'app-chat-bot-promptbar',
standalone: true,
imports: [PromptBarComponent, IconsComponent],
template: \`
  <awe-prompt-bar
    [theme]="'light'"
    [variant]="'chat-bot'"
    [(textValue)]="promptText"
    defaultText="'Start creating with AI: \\"Generate Design\\"'"
    [leftIcons]="leftIcons"
    [rightIcons]="rightIcons"
    (iconClicked)="handleIconClick($event)"
    (textValueChange)="onTextValueChange($event)"
    (enterPressed)="handleEnterPressed()"
    (filesSelected)="onFilesSelected($event)"
    (fileRemoved)="onFileRemoved($event)"
    (filePreviewClosed)="onFilePreviewClosed()">
  </awe-prompt-bar>
  <awe-prompt-bar
    [theme]="'dark'"
    [variant]="'chat-bot'"
    [(textValue)]="promptText"
    defaultText="'Start creating with AI: \\"Generate Design\\"'"
    [leftIcons]="leftIcons"
    [rightIcons]="rightIcons"
    (iconClicked)="handleIconClick($event)"
    (textValueChange)="onTextValueChange($event)"
    (enterPressed)="handleEnterPressed()"
    (filesSelected)="onFilesSelected($event)"
    (fileRemoved)="onFileRemoved($event)"
    (filePreviewClosed)="onFilePreviewClosed()">
  </awe-prompt-bar>
\`
})
export class ChatBotPromptBarComponent {
promptText = '';

type IconStatus = "default" | "active" | "disable";

leftIcons: { name: string; status: IconStatus }[] = [
  { name: 'awe_enhanced_alternate', status: "default" },
  { name: 'awe_figma', status: "disable" }
];

rightIcons: { name: string; status: IconStatus }[] = [
  { name: 'awe_enhance', status: "active" },
  { name: 'awe_enhanced_send', status: "active" }
];

handleIconClick(event: { name: string; side: string; index: number }): void {
  const normalizedIconName = event.name.toLowerCase();

  switch (normalizedIconName) {
    case "awe_enhanced_alternate":
      console.log('File upload triggered');
      break;
    case "awe_figma":
      console.log('Figma functionality triggered');
      break;
    case "awe_enhance":
      this.handleEnhanceText();
      break;
    case "awe_enhanced_send":
      console.log('Enhanced Send functionality triggered');
      break;
    default:
      console.warn("Unknown icon clicked:", event.name);
  }
}

onTextValueChange(text: string) {
  console.log('Text changed:', text);
}

handleEnhanceText(): void {
  if(this.promptText) {
    this.promptText = "Lorem ipsum dolor sit amet consectetur adipisicing elit...";
    setTimeout(() => this.adjustTextareaHeight(), 0);
  }
}

adjustTextareaHeight(): void {
  const textAreas = document.querySelectorAll('.prompt-text');
  textAreas.forEach((textArea) => {
    const htmlTextArea = textArea as HTMLTextAreaElement;
    if (htmlTextArea) {
      htmlTextArea.style.height = 'auto';
      htmlTextArea.style.height = \`\${htmlTextArea.scrollHeight}px\`;
    }
  });
}

handleEnterPressed(): void {
  console.log('Enter key pressed');
}

onFilesSelected(files: File[]): void {
  console.log('Files selected:', files);
}

onFileRemoved(fileId: string): void {
  console.log('File removed:', fileId);
}

onFilePreviewClosed(): void {
  console.log('File preview closed');
}
}`,

    'prompt with design system': `
import { Component, ViewChild, ElementRef } from '@angular/core';
import { PromptBarComponent } from '@awe/play-comp-library';
import { FileAttachPillComponent, FileAttachOption } from '@awe/play-comp-library';
import { IconPillComponent, IconOption } from '@awe/play-comp-library';
import { IconsComponent } from '@awe/play-comp-library';

// Interface for selected files
interface SelectedFile {
  id: string;
  name: string;
  url: string;
  type: string;
}

@Component({
  selector: 'app-prompt-with-design',
  standalone: true,
  imports: [PromptBarComponent, FileAttachPillComponent, IconPillComponent, IconsComponent],
  template: \`
    <awe-prompt-bar
      [theme]="'light'"
      defaultText="'Start creating with AI: \\"Generate Design\\"'"
      (enterPressed)="handleEnterPressed()"
      [(textValue)]="promptText">
      <div class="custom-content">
        <!-- Selected Files Display -->
        <div class="selected-files" *ngIf="selectedFiles.length > 0">
          <div class="file-item" *ngFor="let file of selectedFiles">
            <div class="file-preview" (click)="showFilePreview(file)">
              <img [src]="file.url" [alt]="file.name">
              <span class="file-name">{{ truncateFileName(file.name) }}</span>
            </div>
            <awe-icons
              iconName="awe_close"
              (click)="removeFile(file.id)"
              role="button"
              tabindex="0"
              iconColor="blue"
              [attr.aria-label]="'Remove ' + file.name"
            ></awe-icons>
          </div>
        </div>

        <!-- Design System Selection Container - Show only when triggered -->
        <div class="design-system-container" *ngIf="showDesignSystemContainer">
          <div class="design-system-option" (click)="handleDesignSystemSelection('our')">
            <awe-icons
              iconName="awe_design_system"
              iconSize="48"
              [attr.aria-label]="'Our Design System'"
            ></awe-icons>
            <span class="option-title">Our Design System</span>
          </div>
          <div class="design-system-option" (click)="handleDesignSystemSelection('create')">
            <awe-icons
              iconName="awe_add"
              iconSize="48"
              [attr.aria-label]="'Create Design System'"
            ></awe-icons>
            <span class="option-title">Create Design System</span>
          </div>
        </div>

        <div class="tools-container">
          <div class="pills-container">
            <awe-file-attach-pill
              [currentTheme]="'light'"
              [options]="fileOptions"
              (optionSelected)="onFileOptionSelected($event)"
              [class.disabled]="isFileAttachDisabled"
            ></awe-file-attach-pill>
            <awe-icon-pill
              [options]="designOptions"
              [selectedOption]="selectedDesign"
              (selectionChange)="onDesignSelected($event)"
            ></awe-icon-pill>

            <!-- New container for device icons -->
            <div class="device-icons-container">
              <awe-icons
                iconName="awe_laptop"
                [color]="activeDevice === 'laptop' ? '#3D415C' : '#A3A7C2'"
                (click)="setActiveDevice('laptop')"
                role="button"
                tabindex="0"
                [attr.aria-label]="'Laptop view'"
              ></awe-icons>
              <awe-icons
                iconName="awe_mobile"
                [color]="activeDevice === 'mobile' ? '#3D415C' : '#A3A7C2'"
                (click)="setActiveDevice('mobile')"
                role="button"
                tabindex="0"
                [attr.aria-label]="'Mobile view'"
              ></awe-icons>
            </div>
          </div>

          <div class="enhance-icons">
            <!-- Undo button - only shows when text is enhanced -->
            <awe-icons
              *ngIf="isTextEnhanced"
              iconName="awe_undo"
              (click)="handleUndo()"
              role="button"
              tabindex="0"
              [attr.aria-label]="'Undo Enhancement'"
            ></awe-icons>
            <awe-icons
              iconName="awe_enhance"
              (click)="handleEnhanceText()"
              role="button"
              tabindex="0"
              [attr.aria-label]="'Enhance'"
            ></awe-icons>
            <awe-icons
              iconName="awe_enhanced_send"
              (click)="handleEnhancedSend()"
              role="button"
              tabindex="0"
              [attr.aria-label]="'Enhanced Send'"
            ></awe-icons>
          </div>
        </div>
      </div>
    </awe-prompt-bar>
  \`
})
export class PromptWithDesignComponent {
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  promptText = '';
  selectedFiles: SelectedFile[] = [];
  isFileAttachDisabled = false;
  maxAllowedFiles = 1;
  acceptedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
  showDesignSystemContainer = false;
  originalText = ''; // Store original text before enhancement
  isTextEnhanced = false; // Track if text has been enhanced

  // File attachment options
  fileOptions: FileAttachOption[] = [
    { name: 'From Computer', icon: 'awe_upload', value: 'computer' },
    { name: 'From Cloud', icon: 'awe_cloud_upload', value: 'cloud' },
    { name: 'From URL', icon: 'awe_link', value: 'url' }
  ];

  // Design library options
  designOptions: IconOption[] = [
    { name: 'Tailwind', icon: '/assets/icons/awe_tailwind.svg', value: 'tailwind', isLocalSvg: true },
    { name: 'Material UI', icon: '/assets/icons/awe_material.svg', value: 'material', isLocalSvg: true },
    { name: 'Bootstrap', icon: '/assets/icons/awe_bootstrap.svg', value: 'bootstrap', isLocalSvg: true },
    { name: 'Custom', icon: '/assets/icons/awe_custom.svg', value: 'custom', isLocalSvg: true }
  ];
  selectedDesign: IconOption = this.designOptions[0];

  // Add property for device view
  activeDevice: 'laptop' | 'mobile' = 'laptop'; // Default to laptop view

  handleEnterPressed(): void {
    console.log('Enter key pressed');
  }

  handleEnhanceText(): void {
    if(this.promptText) {
      // Store the original text before enhancement
      if (!this.isTextEnhanced) {
        this.originalText = this.promptText;
      }

      // Update the text with enhanced version
      this.promptText = "Lorem ipsum dolor sit amet consectetur adipisicing elit...";

      // Mark text as enhanced
      this.isTextEnhanced = true;

      // Add a small delay to ensure the DOM has updated with the new text
      setTimeout(() => {
        this.adjustTextareaHeight();
      }, 0);
    }
  }

  handleEnhancedSend(): void {
    // If there's no image attached, show the design system container
    if (this.selectedFiles.length === 0 && this.promptText.trim() !== '') {
      this.showDesignSystemContainer = true;
    } else {
      // Handle normal send operation when image is attached
      console.log('Normal send with image');
      // Add your send implementation here
    }
  }

  handleDesignSystemSelection(type: 'our' | 'create'): void {
    if (type === 'our') {
      console.log('Our Design System selected');
      // Add your implementation for Our Design System here
    } else {
      console.log('Create Design System selected');
      // Add your implementation for Create Design System here
    }

    // Hide the container after selection
    this.showDesignSystemContainer = false;

    // Proceed with sending the prompt
    console.log('Sending prompt with design system:', type);
    // Add your send implementation here
  }

  handleUndo(): void {
    if (this.isTextEnhanced && this.originalText) {
      this.promptText = this.originalText;
      this.isTextEnhanced = false;
      this.originalText = '';

      // Adjust textarea height after text change
      setTimeout(() => {
        this.adjustTextareaHeight();
      }, 0);
    }
  }

  // Device view methods
  setActiveDevice(device: 'laptop' | 'mobile'): void {
    this.activeDevice = device;
    console.log(\`Switched to \${device} view\`);
  }

  // File handling methods
  onFileOptionSelected(option: FileAttachOption): void {
    console.log('Selected file option:', option);

    if (this.isFileAttachDisabled) {
      console.error(\`Only \${this.maxAllowedFiles} image can be uploaded at a time\`);
      return;
    }

    switch (option.value) {
      case 'computer':
        this.fileInput.nativeElement.click();
        break;
      case 'cloud':
        console.log('Opening cloud dialog...');
        break;
      case 'url':
        const url = prompt('Enter the URL of the file:');
        if (url) {
          console.log('File URL:', url);
          this.addFileFromUrl(url);
        }
        break;
    }
  }

  addFileFromUrl(url: string): void {
    const mockFile: SelectedFile = {
      id: Math.random().toString(36).substring(2, 11),
      name: url.split('/').pop() || 'file.jpg',
      url: url,
      type: 'image/jpeg'
    };

    this.selectedFiles = [...this.selectedFiles, mockFile];
    this.updateFileAttachStatus();
  }

  removeFile(fileId: string): void {
    this.selectedFiles = this.selectedFiles.filter(file => file.id !== fileId);
    this.updateFileAttachStatus();
  }

  showFilePreview(file: SelectedFile): void {
    console.log('Preview file:', file);
    // Implementation for preview overlay
  }

  truncateFileName(filename: string): string {
    const maxLength = 13;
    const extension = filename.slice(filename.lastIndexOf('.'));
    const nameWithoutExt = filename.slice(0, filename.lastIndexOf('.'));

    if (filename.length <= maxLength) return filename;

    const truncatedName = nameWithoutExt.slice(0, maxLength - extension.length - 1);
    return \`\${truncatedName}...\${extension}\`;
  }

  updateFileAttachStatus(): void {
    this.isFileAttachDisabled = this.selectedFiles.length >= this.maxAllowedFiles;
  }

  adjustTextareaHeight(): void {
    const textAreas = document.querySelectorAll('.prompt-text');
    textAreas.forEach((textArea) => {
      const htmlTextArea = textArea as HTMLTextAreaElement;
      if (htmlTextArea) {
        htmlTextArea.style.height = 'auto';
        htmlTextArea.style.height = \`\${htmlTextArea.scrollHeight}px\`;
      }
    });
  }

  onDesignSelected(option: IconOption): void {
    console.log('Selected design library:', option);
    this.selectedDesign = option;

    if (option.value === 'custom') {
      const customTheme = prompt('Enter your custom theme name:');
      if (customTheme) {
        console.log('Custom theme:', customTheme);
      }
    }
  }
}`,

    'prompt without design system': `
import { Component, ViewChild, ElementRef } from '@angular/core';
import { PromptBarComponent } from '@awe/play-comp-library';
import { FileAttachPillComponent, FileAttachOption } from '@awe/play-comp-library';
import { IconPillComponent, IconOption } from '@awe/play-comp-library';
import { IconsComponent } from '@awe/play-comp-library';

// Interface for selected files
interface SelectedFile {
  id: string;
  name: string;
  url: string;
  type: string;
}

@Component({
  selector: 'app-prompt-without-design',
  standalone: true,
  imports: [PromptBarComponent, FileAttachPillComponent, IconPillComponent, IconsComponent],
  template: \`
    <awe-prompt-bar
      [theme]="'light'"
      defaultText="'Start creating with AI: \\"Generate Design\\"'"
      (enterPressed)="handleEnterPressed()"
      [(textValue)]="promptText">
      <div class="custom-content">
        <!-- Selected Files Display -->
        <div class="selected-files" *ngIf="selectedFiles.length > 0">
          <div class="file-item" *ngFor="let file of selectedFiles">
            <div class="file-preview" (click)="showFilePreview(file)">
              <img [src]="file.url" [alt]="file.name">
              <span class="file-name">{{ truncateFileName(file.name) }}</span>
            </div>
            <awe-icons
              iconName="awe_close"
              (click)="removeFile(file.id)"
              role="button"
              tabindex="0"
              iconColor="blue"
              [attr.aria-label]="'Remove ' + file.name"
            ></awe-icons>
          </div>
        </div>

        <div class="tools-container">
          <div class="pills-container">
            <awe-file-attach-pill
              [currentTheme]="'light'"
              [options]="fileOptions"
              (optionSelected)="onFileOptionSelected($event)"
              [class.disabled]="isFileAttachDisabled"
            ></awe-file-attach-pill>
            <awe-icon-pill
              [options]="techOptions"
              [selectedOption]="selectedTech"
              (selectionChange)="onTechSelected($event)"
            ></awe-icon-pill>
            <awe-icon-pill
              [options]="designOptions"
              [selectedOption]="selectedDesign"
              (selectionChange)="onDesignSelected($event)"
            ></awe-icon-pill>

            <!-- New container for device icons -->
            <div class="device-icons-container">
              <awe-icons
                iconName="awe_laptop"
                [color]="activeDevice === 'laptop' ? '#3D415C' : '#A3A7C2'"
                (click)="setActiveDevice('laptop')"
                role="button"
                tabindex="0"
                [attr.aria-label]="'Laptop view'"
              ></awe-icons>
              <awe-icons
                iconName="awe_mobile"
                [color]="activeDevice === 'mobile' ? '#3D415C' : '#A3A7C2'"
                (click)="setActiveDevice('mobile')"
                role="button"
                tabindex="0"
                [attr.aria-label]="'Mobile view'"
              ></awe-icons>
            </div>
          </div>

          <div class="enhance-icons">
            <awe-icons
              *ngIf="isTextEnhanced"
              iconName="awe_undo"
              (click)="handleUndo()"
              role="button"
              tabindex="0"
              [attr.aria-label]="'Undo Enhancement'"
            ></awe-icons>
            <awe-icons
              iconName="awe_enhance"
              (click)="handleEnhanceText()"
              role="button"
              tabindex="0"
              [attr.aria-label]="'Enhance'"
            ></awe-icons>
            <awe-icons
              iconName="awe_enhanced_send"
              (click)="handleEnhancedSend()"
              role="button"
              tabindex="0"
              [attr.aria-label]="'Enhanced Send'"
            ></awe-icons>
          </div>
        </div>
      </div>
    </awe-prompt-bar>
  \`
})
export class PromptWithoutDesignComponent {
  @ViewChild('fileInput') fileInput!: ElementRef<HTMLInputElement>;

  promptText = '';
  selectedFiles: SelectedFile[] = [];
  isFileAttachDisabled = false;
  maxAllowedFiles = 1;
  acceptedImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];
  originalText = ''; // Store original text before enhancement
  isTextEnhanced = false; // Track if text has been enhanced

  // File attachment options
  fileOptions: FileAttachOption[] = [
    { name: 'From Computer', icon: 'awe_upload', value: 'computer' },
    { name: 'From Cloud', icon: 'awe_cloud_upload', value: 'cloud' },
    { name: 'From URL', icon: 'awe_link', value: 'url' }
  ];

  // Tech selection options
  techOptions: IconOption[] = [
    { name: 'React', icon: '/assets/icons/awe_react.svg', value: 'react', isLocalSvg: true },
    { name: 'Angular', icon: '/assets/icons/awe_angular.svg', value: 'angular', isLocalSvg: true },
    { name: 'Vue', icon: '/assets/icons/awe_vue.svg', value: 'vue', isLocalSvg: true }
  ];
  selectedTech: IconOption = this.techOptions[0];

  // Design library options
  designOptions: IconOption[] = [
    { name: 'Tailwind', icon: '/assets/icons/awe_tailwind.svg', value: 'tailwind', isLocalSvg: true },
    { name: 'Material UI', icon: '/assets/icons/awe_material.svg', value: 'material', isLocalSvg: true },
    { name: 'Bootstrap', icon: '/assets/icons/awe_bootstrap.svg', value: 'bootstrap', isLocalSvg: true },
    { name: 'Custom', icon: '/assets/icons/awe_custom.svg', value: 'custom', isLocalSvg: true }
  ];
  selectedDesign: IconOption = this.designOptions[0];

  // Add property for device view
  activeDevice: 'laptop' | 'mobile' = 'laptop'; // Default to laptop view

  handleEnterPressed(): void {
    console.log('Enter key pressed');
  }

  handleEnhanceText(): void {
    if(this.promptText) {
      // Store the original text before enhancement
      if (!this.isTextEnhanced) {
        this.originalText = this.promptText;
      }

      // Update the text with enhanced version
      this.promptText = "Lorem ipsum dolor sit amet consectetur adipisicing elit...";

      // Mark text as enhanced
      this.isTextEnhanced = true;

      // Add a small delay to ensure the DOM has updated with the new text
      setTimeout(() => {
        this.adjustTextareaHeight();
      }, 0);
    }
  }

  handleEnhancedSend(): void {
    console.log('Enhanced Send functionality triggered');
    // Add your send implementation here
  }

  handleUndo(): void {
    if (this.isTextEnhanced && this.originalText) {
      this.promptText = this.originalText;
      this.isTextEnhanced = false;
      this.originalText = '';

      // Adjust textarea height after text change
      setTimeout(() => {
        this.adjustTextareaHeight();
      }, 0);
    }
  }

  // Device view methods
  setActiveDevice(device: 'laptop' | 'mobile'): void {
    this.activeDevice = device;
    console.log(\`Switched to \${device} view\`);
  }

  // File handling methods
  onFileOptionSelected(option: FileAttachOption): void {
    console.log('Selected file option:', option);

    if (this.isFileAttachDisabled) {
      console.error(\`Only \${this.maxAllowedFiles} image can be uploaded at a time\`);
      return;
    }

    switch (option.value) {
      case 'computer':
        this.fileInput.nativeElement.click();
        break;
      case 'cloud':
        console.log('Opening cloud dialog...');
        break;
      case 'url':
        const url = prompt('Enter the URL of the file:');
        if (url) {
          console.log('File URL:', url);
          this.addFileFromUrl(url);
        }
        break;
    }
  }

  addFileFromUrl(url: string): void {
    const mockFile: SelectedFile = {
      id: Math.random().toString(36).substring(2, 11),
      name: url.split('/').pop() || 'file.jpg',
      url: url,
      type: 'image/jpeg'
    };

    this.selectedFiles = [...this.selectedFiles, mockFile];
    this.updateFileAttachStatus();
  }

  removeFile(fileId: string): void {
    this.selectedFiles = this.selectedFiles.filter(file => file.id !== fileId);
    this.updateFileAttachStatus();
  }

  showFilePreview(file: SelectedFile): void {
    console.log('Preview file:', file);
    // Implementation for preview overlay
  }

  truncateFileName(filename: string): string {
    const maxLength = 13;
    const extension = filename.slice(filename.lastIndexOf('.'));
    const nameWithoutExt = filename.slice(0, filename.lastIndexOf('.'));

    if (filename.length <= maxLength) return filename;

    const truncatedName = nameWithoutExt.slice(0, maxLength - extension.length - 1);
    return \`\${truncatedName}...\${extension}\`;
  }

  updateFileAttachStatus(): void {
    this.isFileAttachDisabled = this.selectedFiles.length >= this.maxAllowedFiles;
  }

  adjustTextareaHeight(): void {
    const textAreas = document.querySelectorAll('.prompt-text');
    textAreas.forEach((textArea) => {
      const htmlTextArea = textArea as HTMLTextAreaElement;
      if (htmlTextArea) {
        htmlTextArea.style.height = 'auto';
        htmlTextArea.style.height = \`\${htmlTextArea.scrollHeight}px\`;
      }
    });
  }

  onTechSelected(option: IconOption): void {
    console.log('Selected tech:', option);
    this.selectedTech = option;
  }

  onDesignSelected(option: IconOption): void {
    console.log('Selected design library:', option);
    this.selectedDesign = option;

    if (option.value === 'custom') {
      const customTheme = prompt('Enter your custom theme name:');
      if (customTheme) {
        console.log('Custom theme:', customTheme);
      }
    }
  }
}`
  };

  return examples[sectionTitle.toLowerCase()] || '';
}

}
