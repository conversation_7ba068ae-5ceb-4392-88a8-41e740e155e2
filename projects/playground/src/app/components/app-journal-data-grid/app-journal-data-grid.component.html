<div class="demo-page">
  <!-- Journal Data Grid with Header Controls in Parent -->
  <div class="journal-grid-demo">
        <!-- Header Controls (Parent Component) -->
        <div class="journal-data-grid-header">
          <div class="header-left">
            <ava-textbox
              #searchInput
              placeholder="Search for Journal Entries"
              [disabled]="loading"
              icon="search"
              iconPosition="start"
              (input)="onSearchChange($event)"
              class="search-input"
            >
            </ava-textbox>
          </div>



          <div class="header-right">
            <ava-button
              label="Create Journal Entry"
              variant="primary"
              size="medium"
              iconName="plus"
              iconPosition="left"
              [disabled]="loading"
              (userClick)="onCreateClick()"
              class="create-btn"
            >
            </ava-button>
          </div>
        </div>

        <!-- Journal Data Grid Component (Child Component) -->
        <ava-data-composite-component
          #journalDataGridRef
          [data]="journalData"
          [columns]="columns"
          [filteredData]="filteredData"
          [disabled]="loading"
          emptyMessage="No journal entries found"
          (selectionChange)="onSelectionChange($event)"
          (journalIdClick)="onJournalIdClick($event)"
          (dataGridEvent)="onDataGridEvent($event)"
        >
        </ava-data-composite-component>
      </div>

  <!-- Create Journal Entry Modal -->
  <div *ngIf="showCreateModal" class="modal-overlay" (click)="onCloseModal()">
    <div class="modal-container" (click)="$event.stopPropagation()">
      <div class="modal-header">
        <h2>Create New Journal Entry</h2>
        <button class="close-button" (click)="onCloseModal()" aria-label="Close">
          <span>&times;</span>
        </button>
      </div>

      <form [formGroup]="journalForm" (ngSubmit)="onSubmitForm()" class="modal-form">
        <div class="modal-body">
          <!-- Journal Description -->
          <div class="form-group">
            <label for="journalDescription">Journal Description *</label>
            <ava-textarea
              id="journalDescription"
              formControlName="journalDescription"
              placeholder="Enter journal description"
              [variant]="isFieldInvalid('journalDescription') ? 'error' : 'default'">
            </ava-textarea>
            <div *ngIf="isFieldInvalid('journalDescription')" class="error-message">
              {{ getFieldError('journalDescription') }}
            </div>
          </div>

          <!-- Journal Status -->
          <div class="form-group">
            <label for="journalStatus">Journal Status *</label>
            <select
              id="journalStatus"
              formControlName="journalStatus"
              class="form-select"
              [class.error]="isFieldInvalid('journalStatus')">
              <option value="" disabled>Select status</option>
              <option
                *ngFor="let option of statusOptions"
                [value]="option.value">
                {{ option.label }}
              </option>
            </select>
            <div *ngIf="isFieldInvalid('journalStatus')" class="error-message">
              {{ getFieldError('journalStatus') }}
            </div>
          </div>

          <!-- Source Transaction -->
          <div class="form-group">
            <label for="sourceTransaction">Source Transaction *</label>
            <select
              id="sourceTransaction"
              formControlName="sourceTransaction"
              class="form-select"
              [class.error]="isFieldInvalid('sourceTransaction')">
              <option value="" disabled>Select source</option>
              <option
                *ngFor="let option of sourceOptions"
                [value]="option.value">
                {{ option.label }}
              </option>
            </select>
            <div *ngIf="isFieldInvalid('sourceTransaction')" class="error-message">
              {{ getFieldError('sourceTransaction') }}
            </div>
          </div>

          <!-- Dr/Cr Totals -->
          <div class="form-group">
            <label for="drCrTotals">Dr/Cr Totals *</label>
            <ava-textbox
              id="drCrTotals"
              formControlName="drCrTotals"
              placeholder="Enter amount (e.g., $1000.00)"
              [variant]="isFieldInvalid('drCrTotals') ? 'error' : 'default'">
            </ava-textbox>
            <div *ngIf="isFieldInvalid('drCrTotals')" class="error-message">
              {{ getFieldError('drCrTotals') }}
            </div>
          </div>

          <!-- Date -->
          <div class="form-group">
            <label for="date">Date *</label>
            <ava-textbox
              id="date"
              formControlName="date"
              type="date"
              placeholder="Select date"
              [variant]="isFieldInvalid('date') ? 'error' : 'default'">
            </ava-textbox>
            <div *ngIf="isFieldInvalid('date')" class="error-message">
              {{ getFieldError('date') }}
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <ava-button
            type="button"
            label="Cancel"
            variant="secondary"
            (userClick)="onCloseModal()">
          </ava-button>
          <ava-button
            type="submit"
            label="Create Entry"
            variant="primary"
            iconName="plus"
            iconPosition="left">
          </ava-button>
        </div>
      </form>
    </div>
  </div>
</div>
