/* Journal Data Grid Playground Styles - Full Page Layout */

.demo-page {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0.5rem;
  background: #f8f9fa;
  box-sizing: border-box;
  overflow: hidden;
}

.journal-grid-demo {
  height:100%;
  width: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;

  /* Header Controls Styling */
  .journal-data-grid-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    gap: 1rem;
    flex-shrink: 0;

    .header-left {
      flex: 1;
      max-width: 400px;

      .search-input {
        width: 100%;
      }
    }

    .header-right {
      display: flex;
      align-items: center;
    }
  }

  /* Data Grid Container - Make it fill remaining space */
  .data-grid-container {
    flex: 1;
    overflow: auto;
    min-height: 0;
  }

  /* Hide sort and filter icons using CSS */
  ::ng-deep {
    .ava-data-table {
      .sort-icon,
      .filter {
        display: none !important;
      }

      th {
        cursor: default !important;
      }

      /* Make table headers non-clickable */
      .cell-wrapper {
        pointer-events: none;
      }

      /* Re-enable pointer events for content inside headers */
      .grid-column-container {
        pointer-events: auto;
      }
    }
  }
}

/* Modal Styles - Fixed for proper scrolling */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  z-index: 1000;
  padding: 2rem 1rem;
  overflow-y: auto;
}

.modal-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 600px;
  width: 100%;
  max-height: none;
  margin: auto 0;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
  border-radius: 12px 12px 0 0;

  h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #212529;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #6c757d;
    padding: 0.25rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background: #e9ecef;
      color: #212529;
    }

    span {
      display: block;
      line-height: 1;
    }
  }
}

.modal-body {
  padding: 2rem;
  overflow-y: auto;
  flex: 1;
  max-height: calc(80vh - 200px);
}

.modal-footer {
  padding: 1.5rem 2rem;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  border-radius: 0 0 12px 12px;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  flex-shrink: 0;
}

/* Responsive Header */
@media (max-width: 768px) {
  .journal-data-grid-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;

    .header-left {
      max-width: 100%;
    }

    .header-right {
      justify-content: center;
    }
  }
}

/* Form styles for modal */
.form-group {
  margin-bottom: 1.5rem;

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #212529;
    font-size: 0.875rem;
  }

  .error-message {
    margin-top: 0.5rem;
    font-size: 0.75rem;
    color: #dc3545;
  }
}

.modal-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--color-border-subtle);
  background: var(--color-background-secondary);

  h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--color-text-primary);
  }

  .close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--color-text-secondary);
    padding: 0.25rem;
    border-radius: 4px;
    transition: background-color 0.2s ease;

    &:hover {
      background: var(--color-background-disabled);
      color: var(--color-text-primary);
    }

    span {
      display: block;
      line-height: 1;
    }
  }
}

.modal-body {
  padding: 2rem;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem 2rem;
  border-top: 1px solid var(--color-border-subtle);
  background: var(--color-background-secondary);
}

/* Modal form styles */
.modal-form {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Responsive modal */
@media (max-width: 768px) {
  .modal-overlay {
    padding: 0.5rem;
  }

  .modal-container {
    max-width: 100%;
    max-height: 95vh;
  }

  .modal-header,
  .modal-body,
  .modal-footer {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .modal-body {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .modal-footer {
    flex-direction: column;

    ava-button {
      width: 100%;
    }
  }
}
