import { Component, ChangeDetectorRef, OnInit, ViewChild, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import {
  AvaTextboxComponent,
  ButtonComponent,
  AvaTextareaComponent
} from 'play-comp-library';
import { DataCompositeComponent, JournalColumn, JournalDataGridEvent } from '../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'app-journal-data-grid',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    DataCompositeComponent,
    AvaTextboxComponent,
    ButtonComponent,
    AvaTextareaComponent
  ],
  templateUrl: './app-journal-data-grid.component.html',
  styleUrl: './app-journal-data-grid.component.scss'
})
export class AppJournalDataGridComponent implements OnInit {
  @ViewChild('journalDataGridRef') journalDataGridRef!: DataCompositeComponent;

  // Form properties
  showCreateModal = false;
  journalForm: FormGroup;

  // Journal status options
  statusOptions = [
    { value: 'Draft', label: 'Draft' },
    { value: 'Template', label: 'Template' },
    { value: 'Posted', label: 'Posted' },
    { value: 'Rejected', label: 'Rejected' },
    { value: 'Ready to Approve', label: 'Ready to Approve' }
  ];

  // Source transaction options
  sourceOptions = [
    { value: 'Manual', label: 'Manual' },
    { value: 'Automatic', label: 'Automatic' },
    { value: 'Import', label: 'Import' }
  ];

  constructor(private fb: FormBuilder, private cdr: ChangeDetectorRef) {
    this.journalForm = this.fb.group({
      journalDescription: ['', [Validators.required, Validators.minLength(3)]],
      journalStatus: ['Draft', Validators.required],
      sourceTransaction: ['Manual', Validators.required],
      drCrTotals: ['', [Validators.required, Validators.pattern(/^\$?\d+(\.\d{2})?$/)]],
      date: ['', Validators.required]
    });
  }

  ngOnInit() {
    // Initialize filtered data
    this.calculateFilteredData();
  }

  // Sample journal data matching the image provided
  journalData = [
    {
      journalId: '1',
      date: 'mm/dd/yyyy',
      journalDescription: 'This is a template for mortgage payments',
      journalStatus: 'Template',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true,
      iconName: 'file-text'
    },
    {
      journalId: '2',
      date: 'mm/dd/yyyy',
      journalDescription: 'This is a template for mortgage payments',
      journalStatus: 'Posted',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true,
      iconName: 'file-text'
    },
    {
      journalId: '3',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Rejected',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true,
      iconName: 'file-text'
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Ready to Approve',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true,
      iconName: 'file-text'
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Template',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true,
      iconName: 'file-text'
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Ready to Approve',
      sourceTransaction: 'Manual',
      drCrTotals: 'N/A',
      documents: true,
      iconName: 'file-text'
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Rejected',
      sourceTransaction: 'Manual',
      drCrTotals: '$204893',
      documents: true,
      iconName: 'file-text'
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Draft',
      sourceTransaction: 'Manual',
      drCrTotals: '$204893',
      documents: true,
      iconName: 'file-text'
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Draft',
      sourceTransaction: 'Manual',
      drCrTotals: '$204893',
      documents: true,
      iconName: 'file-text'
    },
    {
      journalId: '388236',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Posted',
      sourceTransaction: 'Manual',
      drCrTotals: '$204893',
      documents: true,
      iconName: 'file-text'
    },
    {
      journalId: '3882361',
      date: 'mm/dd/yyyy',
      journalDescription: 'Description',
      journalStatus: 'Posted',
      sourceTransaction: 'Manual',
      drCrTotals: '$204893',
      documents: true,
      iconName: 'file-text'
    }
  ];

  // Column configuration matching the image
  columns: JournalColumn[] = [
    {
      id: 'journalId',
      label: 'Journal ID',
    },
    {
      id: 'date',
      label: 'Date',
    },
    {
      id: 'journalDescription',
      label: 'Journal Description',
    },
    {
      id: 'journalStatus',
      label: 'Journal Status',
    },
    {
      id: 'sourceTransaction',
      label: 'Source Transaction',
    },
    {
      id: 'drCrTotals',
      label: 'Dr/Cr Totals',
    },
    {
      id: 'documents',
      label: 'Documents',
    }
  ];

  // Component state
  loading = false;
  selectedRows: Record<string, unknown>[] = [];
  searchTerm = '';
  filteredData: Record<string, unknown>[] = [];

  private calculateFilteredData(): void {
    let filtered = [...this.journalData];

    // Apply search filter
    if (this.searchTerm && this.searchTerm.trim()) {
      const searchLower = this.searchTerm.toLowerCase().trim();
      filtered = filtered.filter((row) =>
        Object.values(row).some((value) =>
          String(value).toLowerCase().includes(searchLower)
        )
      );
    }

    // Create a new array reference to trigger OnPush change detection
    this.filteredData = [...filtered];
    console.log('Filtered data updated:', this.filteredData.length, 'items');
    console.log('Journal data length:', this.journalData.length);
  }

  // Event handlers
  onSearchChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.searchTerm = target?.value || '';
    console.log('Search changed:', this.searchTerm);
    this.calculateFilteredData();
    this.cdr.markForCheck();
    this.cdr.detectChanges();
  }

  private updateFilteredData() {
    this.calculateFilteredData();

    // Force change detection to update the child component
    this.cdr.markForCheck();
    this.cdr.detectChanges();

    // Additional debugging
    console.log('updateFilteredData called');
    console.log('- journalData length:', this.journalData.length);
    console.log('- filteredData length:', this.filteredData.length);
    console.log('- searchTerm:', this.searchTerm);
    console.log('Current filteredData:', this.filteredData);
  }



  onCreateClick() {
    console.log('Create Journal Entry button clicked');
    this.showCreateModal = true;
    this.resetForm();
    // Prevent background scrolling
    document.body.style.overflow = 'hidden';
  }

  onCloseModal() {
    this.showCreateModal = false;
    this.resetForm();
    // Restore background scrolling
    document.body.style.overflow = 'auto';
  }

  onSubmitForm() {
    console.log('Form submission started');
    console.log('Form valid:', this.journalForm.valid);
    console.log('Form value:', this.journalForm.value);

    if (this.journalForm.valid) {
      const formValue = this.journalForm.value;

      // Generate new journal ID
      const newId = this.generateJournalId();

      // Create new journal entry
      const newEntry = {
        journalId: newId,
        date: formValue.date,
        journalDescription: formValue.journalDescription,
        journalStatus: formValue.journalStatus,
        sourceTransaction: formValue.sourceTransaction,
        drCrTotals: formValue.drCrTotals,
        documents: true, // Default to true for new entries to show icon
        iconName: 'file-text' // Default icon name
      };

      console.log('🚀 === FORM SUBMISSION START ===');
      console.log('📝 New entry to add:', newEntry);
      console.log('📊 Before adding - Journal data length:', this.journalData.length);
      console.log('📊 Before adding - Filtered data length:', this.filteredData.length);

      // Add to journal data at the beginning - create completely new array
      this.journalData = [newEntry, ...this.journalData];

      console.log('✅ After adding - Journal data length:', this.journalData.length);
      console.log('📊 Journal data after adding:', this.journalData);

      // Force update of filtered data FIRST
      console.log('🔄 Calling updateFilteredData...');
      this.updateFilteredData();

      console.log('📊 After updateFilteredData - Filtered data length:', this.filteredData.length);
      console.log('📊 Filtered data after update:', this.filteredData);

      // Force immediate change detection
      this.cdr.markForCheck();
      this.cdr.detectChanges();

      // Manually trigger child component update
      if (this.journalDataGridRef) {
        console.log('🔧 Manually triggering child component update');
        this.journalDataGridRef.filteredData = [...this.filteredData];
        this.journalDataGridRef.data = [...this.journalData];
        this.journalDataGridRef.refreshDataGrid();
      }

      // Additional force change detection with timeout
      setTimeout(() => {
        console.log('⏰ Timeout change detection triggered');
        this.cdr.markForCheck();
        this.cdr.detectChanges();
      }, 0);

      // Close modal and reset form
      this.showCreateModal = false;
      this.resetForm();

      // Restore background scrolling
      document.body.style.overflow = 'auto';

      console.log('🏁 === FORM SUBMISSION END ===');
      console.log('📊 Final journal data length:', this.journalData.length);
      console.log('📊 Final filtered data length:', this.filteredData.length);
    } else {
      console.log('Form is invalid');
      console.log('Form errors:', this.journalForm.errors);
      Object.keys(this.journalForm.controls).forEach(key => {
        const control = this.journalForm.get(key);
        if (control && control.errors) {
          console.log(`${key} errors:`, control.errors);
        }
      });

      // Mark all fields as touched to show validation errors
      Object.keys(this.journalForm.controls).forEach(key => {
        this.journalForm.get(key)?.markAsTouched();
      });
    }
  }

  private resetForm() {
    this.journalForm.reset({
      journalDescription: '',
      journalStatus: 'Draft',
      sourceTransaction: 'Manual',
      drCrTotals: '',
      date: ''
    });
    // Ensure background scrolling is restored
    document.body.style.overflow = 'auto';
  }

  private generateJournalId(): string {
    // Generate a random 6-digit number
    const randomNum = Math.floor(100000 + Math.random() * 900000);
    return randomNum.toString();
  }

  // Form validation helpers
  isFieldInvalid(fieldName: string): boolean {
    const field = this.journalForm.get(fieldName);
    return !!(field && field.invalid && field.touched);
  }

  getFieldError(fieldName: string): string {
    const field = this.journalForm.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors['required']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} is required`;
      }
      if (field.errors['minlength']) {
        return `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
      if (field.errors['pattern']) {
        return 'Please enter a valid amount (e.g., $1000.00)';
      }
    }
    return '';
  }

  onSelectionChange(selectedRows: Record<string, unknown>[]) {
    this.selectedRows = selectedRows;
    console.log('Selection changed:', selectedRows);
  }

  onJournalIdClick(row: Record<string, unknown>) {
    console.log('Journal ID clicked:', row);
    // Implement navigation to journal detail
  }

  onDataGridEvent(event: JournalDataGridEvent) {
    console.log('Data grid event:', event);
    // Handle all data grid events
  }

  // Demo methods for playground
  toggleLoading() {
    this.loading = !this.loading;
  }

  clearSelection() {
    this.selectedRows = [];
  }

  addSampleData() {
    const newEntry = {
      journalId: this.generateJournalId(),
      date: new Date().toLocaleDateString(),
      journalDescription: 'Sample journal entry added',
      journalStatus: 'Draft',
      sourceTransaction: 'Manual',
      drCrTotals: '$150000',
      documents: true,
      iconName: 'file-text'
    };

    console.log('Adding sample data:', newEntry);
    this.journalData = [newEntry, ...this.journalData];
    this.updateFilteredData();

    // Force change detection
    setTimeout(() => {
      this.cdr.markForCheck();
      this.cdr.detectChanges();
    }, 0);
  }

  // Usage example for documentation
  usageExample = `<!-- Parent component handles header controls -->
<div class="journal-data-grid-header">
  <div class="header-left">
    <ava-textbox
      placeholder="Search for Journal Entries"
      icon="search"
      iconPosition="start"
      (textboxChange)="onSearchChange($event)"
    ></ava-textbox>
  </div>

  <div class="header-right">
    <ava-button label="Create Journal Entry" variant="primary"
                iconName="plus" (userClick)="onCreateClick()">
    </ava-button>
  </div>
</div>

<!-- Child component handles data display -->
<ava-journal-data-grid
  [data]="journalData"
  [columns]="columns"
  [filteredData]="filteredData"
  (selectionChange)="onSelectionChange($event)"
  (journalIdClick)="onJournalIdClick($event)"
>
</ava-journal-data-grid>`;
}
