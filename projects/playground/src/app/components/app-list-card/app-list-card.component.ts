import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ListCardComponent } from '../../../../../play-comp-library/src/lib/composite-components/list-card/list-card.component';

@Component({
  selector: 'ava-list-card-demo',
  standalone: true,
  imports: [CommonModule, ListCardComponent],
  templateUrl: './app-list-card.component.html',
  styleUrl: './app-list-card.component.scss',
})
export class AppListCardComponent {
  // Note: This component is currently a placeholder with no inputs or outputs defined
}
