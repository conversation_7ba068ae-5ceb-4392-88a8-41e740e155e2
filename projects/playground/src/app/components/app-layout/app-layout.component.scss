/* Landing Page Styles */
.landing-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}


/* Main Content */
.landing-main {
  padding: 3rem 0;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
  }

  /* Welcome Section */
  .welcome-section {
    text-align: center;
    margin-bottom: 4rem;

    .welcome-content {
      h2 {
        font-size: 3rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 1rem;
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      p {
        font-size: 1.25rem;
        color: #64748b;
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
      }
    }
  }

  /* Components Section */
  .components-section {
    h3 {
      font-size: 2rem;
      font-weight: 600;
      color: #1e293b;
      text-align: center;
      margin-bottom: 3rem;
    }

    .component-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;

      .component-category {
        background: rgba(255, 255, 255, 0.8);
        border-radius: 1rem;
        padding: 2rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);

        h4 {
          font-size: 1.25rem;
          font-weight: 600;
          color: #1e293b;
          margin-bottom: 1.5rem;
          padding-bottom: 0.5rem;
          border-bottom: 2px solid #e2e8f0;
        }

        .component-links {
          display: grid;
          gap: 0.75rem;

          .component-link {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 0.5rem;
            text-decoration: none;
            color: #374151;
            transition: all 0.2s;
            border: 1px solid transparent;

            &:hover {
              background: rgba(99, 102, 241, 0.1);
              border-color: #6366f1;
              color: #6366f1;
              transform: translateX(4px);
            }

            .component-icon {
              font-size: 1.25rem;
              margin-right: 0.75rem;
              width: 1.5rem;
              text-align: center;
            }

            .component-name {
              font-weight: 500;
            }
          }
        }
      }
    }
  }
}

/* Footer */
.landing-footer {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 2rem 0;
  margin-top: 4rem;

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;

    p {
      color: #64748b;
      margin: 0;
    }
  }
}

/* Dark Theme */
html[data-theme="dark"] {
  .landing-page {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  }

  .landing-header {
    background: rgba(30, 41, 59, 0.9);
    border-bottom-color: rgba(255, 255, 255, 0.1);

    .header-content {
      .logo-section {

        h1,
        p {
          color: #f8fafc;
        }
      }

      .theme-controls button {
        background: #374151;
        border-color: #4b5563;
        color: #f8fafc;

        &:hover {
          background: #4b5563;
        }

        &.active {
          background: #6366f1;
        }
      }
    }
  }

  .landing-main {
    .welcome-section .welcome-content {

      h2,
      p {
        color: #f8fafc;
      }
    }

    .components-section {
      h3 {
        color: #f8fafc;
      }

      .component-category {
        background: rgba(30, 41, 59, 0.8);
        border-color: rgba(255, 255, 255, 0.1);

        h4 {
          color: #f8fafc;
          border-bottom-color: #374151;
        }

        .component-link {
          background: rgba(55, 65, 81, 0.5);
          color: #f8fafc;

          &:hover {
            background: rgba(99, 102, 241, 0.2);
            color: #93c5fd;
          }
        }
      }
    }
  }

  .landing-footer {
    background: rgba(30, 41, 59, 0.9);
    border-top-color: rgba(255, 255, 255, 0.1);

    p {
      color: #cbd5e1;
    }
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .landing-header .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;

    .logo-section h1 {
      font-size: 1.5rem;
    }
  }

  .landing-main {
    padding: 2rem 0;

    .welcome-section .welcome-content h2 {
      font-size: 2rem;
    }

    .components-section {
      .component-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }

      .component-category {
        padding: 1.5rem;
      }
    }
  }
}