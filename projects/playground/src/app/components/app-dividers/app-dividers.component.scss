.documentation {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
  line-height: 1.6;
  color: #333;

  // Header Styles
  .doc-header {
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid #e9ecef;

    h1 {
      font-size: 2.5rem;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 1rem;
    }

    .description {
      font-size: 1.125rem;
      color: #6c757d;
      max-width: 800px;
      margin: 0 auto;
    }
  }

  // Section Styles
  .doc-section {
    margin-bottom: 3rem;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;

    h2 {
      font-size: 1.75rem;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 1rem;
    }

    h3 {
      font-size: 1.375rem;
      font-weight: 600;
      color: #495057;
      margin-bottom: 0.75rem;
    }
  }

  // Section Header
  .section-header {
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;

    .description-container {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: 1rem;

      p {
        margin: 0;
        flex: 1;
        color: #6c757d;
      }
    }
  }

  // Code Toggle Button
  .code-toggle {
    background: #007bff;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    outline: none;
    white-space: nowrap;

    &:hover,
    &:focus {
      background: #0056b3;
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
    }

    &:active {
      transform: translateY(0);
    }
  }

  // Code Example Container
  .code-example {
    border-top: 1px solid #dee2e6;

    &.expanded {
      .example-preview {
        border-bottom: 1px solid #dee2e6;
      }
    }
  }

  // Example Preview
  .example-preview {
    padding: 2rem;

    // Basic Usage Demo
    .basic-usage-demo {
      .content-section {
        max-width: 600px;

        h3 {
          color: #495057;
          margin-bottom: 0.75rem;
        }

        p {
          color: #6c757d;
          margin-bottom: 1.5rem;
        }
      }
    }

    // Variants Demo
    .variants-demo {
      .variant-example {
        margin-bottom: 2rem;
        padding: 1.5rem;
        background: #f8f9fa;
        border-radius: 8px;

        h4 {
          color: #495057;
          margin-bottom: 0.5rem;
          font-size: 1.125rem;
        }

        p {
          color: #6c757d;
          margin-bottom: 1rem;
          font-size: 0.9rem;
        }
      }
    }

    // Orientation Demo
    .orientation-demo {

      .horizontal-section,
      .vertical-section {
        margin-bottom: 2.5rem;

        h4 {
          color: #495057;
          margin-bottom: 1rem;
          padding-bottom: 0.5rem;
          border-bottom: 2px solid #e9ecef;
        }
      }

      .article-content {
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;

        p {
          margin-bottom: 1rem;
          color: #6c757d;
        }
      }

      .layout-demo {
        display: flex;
        gap: 1.5rem;
        background: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
        min-height: 200px;

        .sidebar-content,
        .main-content,
        .panel-content {
          flex: 1;
          padding: 1rem;
          background: white;
          border-radius: 6px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

          h5 {
            color: #495057;
            margin-bottom: 0.75rem;
            font-size: 1rem;
          }

          ul {
            margin: 0;
            padding-left: 1.25rem;

            li {
              color: #6c757d;
              margin-bottom: 0.25rem;
            }
          }

          p {
            color: #6c757d;
            margin: 0;
          }
        }
      }
    }

    // Color Demo
    .color-demo {
      .color-section {
        margin-bottom: 2rem;

        h4 {
          color: #495057;
          margin-bottom: 1rem;
          padding-bottom: 0.5rem;
          border-bottom: 2px solid #e9ecef;
        }
      }

      .color-examples,
      .gradient-examples {
        display: grid;
        gap: 1rem;
      }

      .color-item,
      .gradient-item {
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 8px;

        .color-label {
          display: block;
          font-weight: 500;
          color: #495057;
          margin-bottom: 0.75rem;
          font-size: 0.9rem;
        }
      }
    }

    // Practical Demo
    .practical-demo {

      .card-example,
      .form-example,
      .navigation-example {
        margin-bottom: 2.5rem;

        h4 {
          color: #495057;
          margin-bottom: 1rem;
          padding-bottom: 0.5rem;
          border-bottom: 2px solid #e9ecef;
        }
      }

      // Demo Card
      .demo-card {
        max-width: 400px;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .card-header,
        .card-body,
        .card-footer {
          padding: 1.25rem;
        }

        .card-header {
          background: #f8f9fa;
          border-bottom: 1px solid #dee2e6;

          h5 {
            margin: 0;
            color: #495057;
            font-size: 1.125rem;
          }
        }

        .card-body {
          p {
            margin-bottom: 0.75rem;
            color: #6c757d;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        .card-footer {
          background: #f8f9fa;
          display: flex;
          gap: 0.75rem;
        }
      }

      // Demo Form
      .demo-form {
        max-width: 500px;
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .form-section {
          margin-bottom: 1rem;

          h5 {
            color: #495057;
            margin-bottom: 1rem;
            font-size: 1rem;
          }
        }

        .form-input {
          width: 100%;
          padding: 0.75rem;
          border: 1px solid #ced4da;
          border-radius: 4px;
          margin-bottom: 0.75rem;
          font-size: 0.9rem;
          transition: border-color 0.15s ease-in-out;

          &:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
          }
        }

        .form-actions {
          display: flex;
          gap: 0.75rem;
        }
      }

      // Demo Navigation
      .demo-nav {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .nav-section {
          display: flex;
          gap: 1.5rem;
          padding: 0.75rem 0;

          .nav-link {
            color: #007bff;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.15s ease-in-out;

            &:hover {
              color: #0056b3;
              text-decoration: underline;
            }
          }
        }
      }
    }

    // Responsive Demo
    .responsive-demo {
      .responsive-article {
        background: white;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        .article-header {
          margin-bottom: 1rem;

          h3 {
            color: #495057;
            margin-bottom: 0.5rem;
          }

          .meta {
            color: #6c757d;
            font-size: 0.875rem;
            margin: 0;
          }
        }

        .responsive-content {
          display: flex;
          gap: 1.5rem;
          margin: 1.5rem 0;

          .content-main {
            flex: 2;

            h4 {
              color: #495057;
              margin-bottom: 1rem;
            }

            p {
              color: #6c757d;
              margin-bottom: 1rem;
            }
          }

          .content-sidebar {
            flex: 1;
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;

            h5 {
              color: #495057;
              margin-bottom: 0.75rem;
            }

            ul {
              margin: 0;
              padding-left: 1.25rem;

              li {
                margin-bottom: 0.5rem;

                a {
                  color: #007bff;
                  text-decoration: none;

                  &:hover {
                    text-decoration: underline;
                  }
                }
              }
            }
          }
        }

        .article-footer {
          margin-top: 1rem;

          p {
            color: #6c757d;
            font-size: 0.875rem;
            margin: 0;
          }
        }
      }

      .responsive-cards {
        h4 {
          color: #495057;
          margin-bottom: 1rem;
          padding-bottom: 0.5rem;
          border-bottom: 2px solid #e9ecef;
        }

        .card-grid {
          display: flex;
          gap: 1rem;

          .grid-card {
            flex: 1;
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 1.25rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

            h5 {
              color: #495057;
              margin-bottom: 0.75rem;
            }

            p {
              color: #6c757d;
              margin: 0;
              font-size: 0.9rem;
            }
          }
        }
      }
    }
  }

  // Code Display
  .code-display {
    background: #f8f9fa;

    .code-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem 1.5rem;
      background: #2c3e50;
      color: white;
      font-weight: 500;

      .copy-btn {
        background: #007bff;
        color: white;
        border: none;
        padding: 0.375rem 0.75rem;
        border-radius: 4px;
        font-size: 0.875rem;
        cursor: pointer;
        transition: background-color 0.15s ease-in-out;

        &:hover {
          background: #0056b3;
        }
      }
    }

    pre {
      margin: 0;
      padding: 1.5rem;
      background: #282c34;
      color: #abb2bf;
      font-family: "Fira Code", "Source Code Pro", Consolas, monospace;
      font-size: 0.875rem;
      line-height: 1.5;
      overflow-x: auto;

      code {
        white-space: pre;
      }
    }
  }

  // Buttons
  .btn-primary,
  .btn-secondary {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    font-size: 0.875rem;
  }

  .btn-primary {
    background: #007bff;
    color: white;

    &:hover {
      background: #0056b3;
    }
  }

  .btn-secondary {
    background: #6c757d;
    color: white;

    &:hover {
      background: #545b62;
    }
  }

  // API Documentation
  .api-section {
    .api-subsection {
      margin-bottom: 2rem;

      h3 {
        color: #495057;
        margin-bottom: 1rem;
        padding-bottom: 0.5rem;
        border-bottom: 2px solid #e9ecef;
      }
    }

    .api-table {
      overflow-x: auto;

      table {
        width: 100%;
        border-collapse: collapse;
        background: white;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        th,
        td {
          padding: 0.75rem 1rem;
          text-align: left;
          border-bottom: 1px solid #dee2e6;
        }

        th {
          background: #f8f9fa;
          font-weight: 600;
          color: #495057;
        }

        td {
          color: #6c757d;

          code {
            background: #f1f3f4;
            padding: 0.125rem 0.375rem;
            border-radius: 3px;
            font-family: "Fira Code", "Source Code Pro", Consolas, monospace;
            font-size: 0.875rem;
            color: #e83e8c;
          }
        }

        tr:hover {
          background: #f8f9fa;
        }
      }
    }
  }

  // Guidelines Section
  .guidelines-section {
    .guidelines-content {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;

      .guideline-item {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

        h4 {
          color: #495057;
          margin-bottom: 1rem;
          font-size: 1.125rem;
        }

        ul {
          margin: 0;
          padding-left: 1.25rem;

          li {
            color: #6c757d;
            margin-bottom: 0.5rem;
            line-height: 1.5;

            strong {
              color: #495057;
            }
          }
        }
      }
    }
  }

  // Navigation Links
  .nav-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    padding: 2rem;

    .nav-link {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 1rem 1.5rem;
      background: white;
      border: 2px solid #e9ecef;
      border-radius: 8px;
      text-decoration: none;
      color: #495057;
      transition: all 0.2s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

      &:hover {
        background: #f8f9fa;
        border-color: #007bff;
        color: #007bff;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
        text-decoration: none;
      }

      .nav-icon {
        font-size: 1.25rem;
        flex-shrink: 0;
      }

      .nav-text {
        font-weight: 500;
        font-size: 0.95rem;
      }
    }
  }

  // Code Block
  .code-block {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;

    pre {
      margin: 0;
      background: #282c34;
      color: #abb2bf;
      padding: 1rem;
      border-radius: 4px;
      font-family: "Fira Code", "Source Code Pro", Consolas, monospace;
      font-size: 0.875rem;
      overflow-x: auto;
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .documentation {
    padding: 1rem;

    .doc-header {
      h1 {
        font-size: 2rem;
      }

      .description {
        font-size: 1rem;
      }
    }

    .section-header {
      padding: 1rem;

      .description-container {
        flex-direction: column;
        gap: 1rem;
      }
    }

    .example-preview {
      padding: 1rem;

      .orientation-demo {
        .layout-demo {
          flex-direction: column;
          min-height: auto;
        }

        .divider-vertical-responsive {
          display: none;
        }
      }

      .responsive-demo {
        .responsive-content {
          flex-direction: column;
        }

        .card-grid {
          flex-direction: column;
        }

        .divider-responsive {
          display: none;
        }
      }
    }

    .guidelines-content {
      grid-template-columns: 1fr;
    }

    .api-table {
      font-size: 0.8rem;
    }
  }
}

@media (max-width: 480px) {
  .documentation {
    .doc-header {
      h1 {
        font-size: 1.75rem;
      }
    }

    .section-header {
      .description-container {
        .code-toggle {
          width: 100%;
          text-align: center;
        }
      }
    }
  }
}