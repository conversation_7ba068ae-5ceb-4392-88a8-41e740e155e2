.demo-container {
  padding: 2rem;
  max-width: 700px;
  .variant-description {
    border-left: 4px solid #007bff;
    padding: 1rem 1.5rem;
    margin: 0 2rem 1rem 2rem;
    border-radius: 0 8px 8px 0;

    p {
      margin-bottom: 0.5rem;
      color: #495057;

      &:last-child {
        margin-bottom: 0;
      }

      strong {
        color: #2c3e50;
        font-weight: 600;
      }
    }
  }

  .variant-example {
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    margin: 0 auto;

    h3 {
      color: #495057;
      margin-bottom: 0.75rem;
      font-size: 1.25rem;
    }

    p {
      color: #6c757d;
      margin-bottom: 1.5rem;
      line-height: 1.6;
    }
  }

  .comparison-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;

    .comparison-item {
      h4 {
        // background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        // color: white;
        margin: 0;
        padding: 1rem;
        text-align: center;
        font-size: 1.125rem;
        font-weight: 600;
      }

      .comparison-content {
        padding: 1.5rem;

        p {
          color: #6c757d;
          margin-bottom: 1rem;
          text-align: center;
          font-size: 0.9rem;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }
}

// Responsive Design for Variants
@media (max-width: 768px) {
  .demo-container {
    .variant-description {
      margin: 0 1rem 1rem 1rem;
      padding: 1rem;
    }

    .comparison-grid {
      grid-template-columns: 1fr;
      padding: 1rem;
    }
  }
}

@media (max-width: 480px) {
  .demo-container {
    .variant-description {
      border-left: none;
      border-top: 4px solid #007bff;
      border-radius: 8px;
    }

    .comparison-grid {
      .comparison-item {
        .comparison-content {
          padding: 1rem;
        }
      }
    }
  }
}
