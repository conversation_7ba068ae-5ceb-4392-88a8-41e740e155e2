<div class="demo-container">
  <div class="demo-section">
    <div class="example-container">
      <div class="comparison-grid">
        <div class="comparison-item">
          <h4 style="color: var(--color-text-primary)">Solid</h4>
          <div class="comparison-content">
            <p>Strong separation</p>
            <ava-dividers
              variant="solid"
              orientation="horizontal"
              color="#BBBEC5"
            ></ava-dividers>
            <p>Clear boundaries</p>
          </div>
        </div>

        <div class="comparison-item">
          <h4 style="color: var(--color-text-primary)">Dashed</h4>
          <div class="comparison-content">
            <p>Subtle separation</p>
            <ava-dividers
              variant="dashed"
              orientation="horizontal"
              color="#BBBEC5"
            ></ava-dividers>
            <p>Gentle boundaries</p>
          </div>
        </div>

        <div class="comparison-item">
          <h4 style="color: var(--color-text-primary)">Dotted</h4>
          <div class="comparison-content">
            <p>Delicate separation</p>
            <ava-dividers
              variant="dotted"
              orientation="horizontal"
              color="#BBBEC5"
            ></ava-dividers>
            <p>Minimal boundaries</p>
          </div>
        </div>

        <div class="comparison-item">
          <h4 style="color: var(--color-text-primary)">Gradient</h4>
          <div class="comparison-content">
            <p>Modern separation</p>
            <ava-dividers
              variant="gradient"
              orientation="horizontal"
            ></ava-dividers>
            <p>Smooth boundaries</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
