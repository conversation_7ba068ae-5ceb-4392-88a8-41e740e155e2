// Basic Usage Demo Styles


.demo-container {
  max-width: 840px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  line-height: 1.6;
  color: #333;

  .demo-header {
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;

    h1 {
      font-size: 2.5rem;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 1rem;
    }

    .description {
      font-size: 1.125rem;
      color: #6c757d;
      max-width: 800px;
      margin: 0 auto;
    }
  }

  .demo-section {
    margin-bottom: 3rem;
    border-radius: 12px;
    overflow: hidden;

    h2 {
      font-size: 1.75rem;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 1.5rem;
      padding: 1.5rem 2rem 0;
    }

    .example-container {
      padding: 2rem;

      .content-section {
        max-width: 600px;
        margin: 0 auto;
        padding: 2rem;
        border-radius: 8px;

        h3 {
          color: #495057;
          margin-bottom: 0.75rem;
          font-size: 1.25rem;
        }

        p {
          color: #6c757d;
          margin-bottom: 1.5rem;
          line-height: 1.6;
        }
      }
    }

    .code-example {
      .code-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 1.5rem;
        color: white;
        font-weight: 500;

        .copy-btn {
          background: #007bff;
          color: white;
          border: none;
          padding: 0.375rem 0.75rem;
          border-radius: 4px;
          font-size: 0.875rem;
          cursor: pointer;
          transition: background-color 0.15s ease-in-out;

          &:hover {
            background: #0056b3;
          }
        }
      }

      pre {
        margin: 0;
        padding: 1.5rem;
        color: #abb2bf;
        font-family: "Fira Code", "Source Code Pro", Consolas, monospace;
        font-size: 0.875rem;
        line-height: 1.5;
        overflow-x: auto;

        code {
          white-space: pre;
        }
      }
    }

    .use-cases {
      padding: 2rem;
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;

      .use-case {
        padding: 1.5rem;
        border-radius: 8px;

        h4 {
          color: #495057;
          margin-bottom: 0.75rem;
          font-size: 1.125rem;
        }

        p {
          color: #6c757d;
          margin: 0;
          line-height: 1.5;
        }
      }
    }
  }
}

// Responsive Design
@media (max-width: 768px) {
  .demo-container {
    padding: 1rem;

    .demo-header {
      h1 {
        font-size: 2rem;
      }

      .description {
        font-size: 1rem;
      }
    }

    .demo-section {
      .example-container {
        padding: 1rem;

        .content-section {
          padding: 1.5rem;
        }
      }

      .use-cases {
        grid-template-columns: 1fr;
        padding: 1rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .demo-container {
    .demo-header {
      h1 {
        font-size: 1.75rem;
      }
    }

    .demo-section {
      h2 {
        font-size: 1.5rem;
        padding: 1rem 1rem 0;
      }
    }
  }
}