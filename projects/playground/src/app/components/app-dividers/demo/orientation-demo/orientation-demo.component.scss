.demo-container {
  padding: 2rem;
  .article-content {
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    max-width: 600px;
    margin: 0 auto;

    p {
      margin-bottom: 1rem;
      color: #6c757d;
    }
  }

  .layout-demo {
    display: flex;
    gap: 1.5rem;
    background: #f8f9fa;
    padding: 1.5rem;
    border-radius: 8px;
    min-height: 200px;

    .sidebar-content,
    .main-content,
    .panel-content {
      flex: 1;
      padding: 1rem;
      background: white;
      border-radius: 6px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

      h5 {
        color: #495057;
        margin-bottom: 0.75rem;
        font-size: 1rem;
      }

      ul {
        margin: 0;
        padding-left: 1.25rem;

        li {
          color: #6c757d;
          margin-bottom: 0.25rem;
        }
      }

      p {
        color: #6c757d;
        margin: 0;
      }
    }
  }
}

@media (max-width: 768px) {
  .demo-container {
    .layout-demo {
      flex-direction: column;
      min-height: auto;
    }
  }
}
