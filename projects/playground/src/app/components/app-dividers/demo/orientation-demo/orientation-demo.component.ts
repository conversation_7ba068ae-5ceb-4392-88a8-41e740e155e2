import { Component, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DividersComponent } from '../../../../../../../play-comp-library/src/lib/components/dividers/dividers.component';

@Component({
  selector: 'ava-orientation-demo',
  standalone: true,
  imports: [CommonModule, DividersComponent],
  templateUrl: './orientation-demo.component.html',
  styleUrls: ['./orientation-demo.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class OrientationDemoComponent {
  copyCode(codeType: string): void {
    const codeExamples: Record<string, string> = {
      horizontal: `<ava-dividers variant="solid" orientation="horizontal" color="#007bff"></ava-dividers>`,
      vertical: `<ava-dividers variant="solid" orientation="vertical" color="#dc3545"></ava-dividers>`,
      layout: `
<div class="layout-demo">
  <div class="sidebar">
    <h4>Sidebar Content</h4>
  </div>
  
  <ava-dividers variant="solid" orientation="vertical"></ava-dividers>
  
  <div class="main-content">
    <h4>Main Content</h4>
  </div>
</div>`,
    };

    const code = codeExamples[codeType] || '';
    navigator.clipboard.writeText(code);
  }
}
