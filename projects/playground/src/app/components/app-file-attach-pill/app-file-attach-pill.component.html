<div class="documentation">
  <!-- Header -->
  <header class="doc-header">
    <h1>File Attach Pill Component</h1>
    <p class="description">
      A versatile icon component that expands on hover and shows file attachment options on click.
      Supports both ava-icons and custom icons with theme support.
    </p>
  </header>

  <!-- Installation -->
  <section class="doc-section">
    <h2>Installation</h2>
    <div class="code-block">
      <pre><code>import {{ '{' }} FileAttachPillComponent, FileAttachOption {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
    </div>
  </section>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section *ngFor="let section of sections; let i = index" class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            
            <!-- Basic Usage -->
            <ng-container *ngSwitchCase="'Basic Usage'">
              <div class="demo-item">
                <h3>Basic File Attachment</h3>
                <div class="demo-content">
                  <ava-file-attach-pill
                    [options]="fileOptions"
                    (optionSelected)="onOptionSelected($event)">
                  </ava-file-attach-pill>
                </div>
                <p class="demo-caption" *ngIf="selectedOption">Last selected: {{ selectedOption.name }}</p>
              </div>
            </ng-container>

            <!-- Mixed Icon Types -->
            <ng-container *ngSwitchCase="'Mixed Icon Types'">
              <div class="demo-item">
                <h3>Mixed Icon Types</h3>
                <div class="demo-content">
                  <ava-file-attach-pill
                    [options]="mixedOptions"
                    mainText="Mixed Icons"
                    (optionSelected)="onOptionSelected($event)">
                  </ava-file-attach-pill>
                </div>
                <p class="demo-caption">Combines ava-icons and custom image icons</p>
              </div>
            </ng-container>

            <!-- Custom Main Icon -->
            <ng-container *ngSwitchCase="'Custom Main Icon'">
              <div class="demo-item">
                <h3>Technology Selector</h3>
                <div class="demo-content">
                  <ava-file-attach-pill
                    [options]="techOptions"
                    mainText="Angular"
                    [mainIcon]="'assets/awe_angular.svg'"
                    [useCustomMainIcon]="true"
                    (optionSelected)="onOptionSelected($event)">
                  </ava-file-attach-pill>
                </div>
                <p class="demo-caption">Custom main icon with framework options</p>
              </div>

              <div class="demo-item">
                <h3>Design Framework Selector</h3>
                <div class="demo-content">
                  <ava-file-attach-pill
                    [options]="designOptions"
                    mainText="Choose Design "
                    [mainIcon]="'assets/awe_material.svg'"
                    [useCustomMainIcon]="true"
                    (optionSelected)="onOptionSelected($event)">
                  </ava-file-attach-pill>
                </div>
                <p class="demo-caption">Design framework selection with custom icons</p>
              </div>
            </ng-container>

           

            <!-- Different Icon Sizes -->
            <ng-container *ngSwitchCase="'Different Icon Sizes'">
              <div class="demo-item">
                <h3>Large Icons (24px)</h3>
                <div class="demo-content">
                  <ava-file-attach-pill
                    [options]="fileOptions"
                    [iconSize]="24"
                    mainText="Large Icons"
                    (optionSelected)="onOptionSelected($event)">
                  </ava-file-attach-pill>
                </div>
              </div>

              <div class="demo-item">
                <h3>Default Icons (20px)</h3>
                <div class="demo-content">
                  <ava-file-attach-pill
                    [options]="fileOptions"
                    mainText="Default Icons"
                    (optionSelected)="onOptionSelected($event)">
                  </ava-file-attach-pill>
                </div>
              </div>

              <div class="demo-item">
                <h3>Small Icons (16px)</h3>
                <div class="demo-content">
                  <ava-file-attach-pill
                    [options]="fileOptions"
                    [iconSize]="16"
                    mainText="Small Icons"
                    (optionSelected)="onOptionSelected($event)">
                  </ava-file-attach-pill>
                </div>
              </div>
            </ng-container>

          </ng-container>
        </div>

        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            Copy Code
          </button>
        </div>
      </div>
    </section>
  </div>

  <!-- FileAttachOption Interface -->
  <section class="doc-section">
    <h2>FileAttachOption Interface</h2>
    <div class="api-block">
      <table class="api-table">
        <thead>
          <tr>
            <th>Property</th>
            <th>Type</th>
            <th>Required</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td><code>name</code></td>
            <td><code>string</code></td>
            <td>Yes</td>
            <td>Display name for the option</td>
          </tr>
          <tr>
            <td><code>icon</code></td>
            <td><code>string</code></td>
            <td>Yes</td>
            <td>Icon name (ava-icon) or URL/path (custom icon)</td>
          </tr>
          <tr>
            <td><code>value</code></td>
            <td><code>string</code></td>
            <td>Yes</td>
            <td>Unique identifier for the option</td>
          </tr>
          <tr>
            <td><code>useCustomIcon</code></td>
            <td><code>boolean</code></td>
            <td>No</td>
            <td>Whether to treat icon as custom URL/path instead of ava-icon</td>
          </tr>
        </tbody>
      </table>
    </div>
  </section>

  <!-- API Reference -->
  <section class="doc-section api-reference">
    <h2>API Reference</h2>
    <div class="api-block">
      <h3>Inputs</h3>
      <table class="api-table">
        <thead>
          <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Default</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let prop of apiProps">
            <td><code>{{ prop.name }}</code></td>
            <td><code>{{ prop.type }}</code></td>
            <td><code>{{ prop.default }}</code></td>
            <td>{{ prop.description }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </section>

  <!-- Events -->
  <section class="doc-section">
    <h2>Events</h2>
    <div class="api-block">
      <table class="api-table">
        <thead>
          <tr>
            <th>Name</th>
            <th>Type</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let event of events">
            <td><code>{{ event.name }}</code></td>
            <td><code>{{ event.type }}</code></td>
            <td>{{ event.description }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </section>

</div>