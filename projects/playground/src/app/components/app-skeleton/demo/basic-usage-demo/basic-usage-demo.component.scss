.basic-usage-demo {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;

  .demo-header {
    text-align: center;
    margin-bottom: 2rem;

    h2 {
      color: #333;
      margin-bottom: 0.5rem;
    }

    p {
      color: #666;
      font-size: 1.1rem;
    }
  }

  .demo-content {
    margin-bottom: 3rem;

    .skeleton-examples {
      display: flex;
      flex-direction: column;
      gap: 1.5rem;
      padding: 2rem;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;

      .skeleton-item {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;

        .description {
          margin: 0;
          font-size: 0.9rem;
          color: #666;
          font-style: italic;
        }
      }
    }
  }

  .demo-info {
    h3 {
      color: #333;
      margin-bottom: 1rem;
      border-bottom: 2px solid #007bff;
      padding-bottom: 0.5rem;
    }

    p {
      color: #555;
      line-height: 1.6;
      margin-bottom: 1.5rem;
    }

    pre {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 1rem;
      overflow-x: auto;
      margin: 1rem 0;

      code {
        color: #333;
        font-family: "Courier New", monospace;
        font-size: 0.9rem;
      }
    }
  }
}
