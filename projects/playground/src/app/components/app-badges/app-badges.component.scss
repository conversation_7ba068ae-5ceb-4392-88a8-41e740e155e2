:host {
  display: block;
  width: 100%;
}

/* Prevent horizontal scroll */
html,
body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
}

* {
  box-sizing: border-box;
}

/* Main layout */
.demo-container {
  max-width: 1200px;
  padding: 2rem;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
  overflow-x: hidden;
  margin: 0 auto;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

/* Header */
.demo-header {
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid var(--surface-border, #e2e8f0);

  h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--text-primary, #1e293b);
    margin-bottom: 1rem;
  }

  p {
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--text-secondary, #64748b);
    max-width: 800px;
  }
}

/* Navigation */
.demo-navigation {
  margin-bottom: 3rem;
  padding: 2rem;
  background: var(--surface, #ffffff);
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--surface-border, #e2e8f0);

  h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-primary, #1e293b);
    margin-bottom: 1.5rem;
  }

  .nav-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
    }
  }

  .nav-link {
    display: inline-block;
    padding: 1rem 1.5rem;
    background: var(--surface-ground, #f8fafc);
    color: var(--text-primary, #1e293b);
    text-decoration: none;
    border-radius: 8px;
    border: 1px solid var(--surface-border, #e2e8f0);
    font-weight: 500;
    transition: all 0.2s ease;
    text-align: center;

    &:hover {
      background: var(--primary-color, #3b82f6);
      color: white;
      transform: translateY(-1px);
      box-shadow: 0 4px 6px rgba(59, 130, 246, 0.2);
    }

    &:active {
      transform: translateY(0);
    }

    &.active {
      background: var(--primary-color, #3b82f6);
      color: white;
    }
  }
}

/* Demo content */
.demo-content {
  min-height: 400px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .demo-container {
    padding: 1rem;
  }

  .demo-header {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;

    h1 {
      font-size: 2rem;
    }
  }

  .demo-navigation {
    margin-bottom: 2rem;
    padding: 1.5rem;
  }
}
