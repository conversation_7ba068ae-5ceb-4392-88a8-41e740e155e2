import { Component, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BadgesComponent } from '../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-badges-variants',
  standalone: true,
  imports: [CommonModule, BadgesComponent],
  templateUrl: './badges-variants.component.html',
  styleUrls: ['./badges-variants.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class BadgesVariantsComponent {
  // Component logic can be added here if needed
}
