.demo-section {
  padding: 2rem;
  max-width: 800px;
  margin: 0 auto;

  .demo-header {
    text-align: center;
    margin-bottom: 3rem;

    h2 {
      font-size: 2.5rem;
      color: #333;
      margin-bottom: 1rem;
    }

    p {
      font-size: 1.1rem;
      color: #666;
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;
    }
  }

  .variant-section {
    display: flex;
    gap: 6rem;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
  }

  .usage-examples {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 2rem;

    .example-item {
      background: white;
      padding: 1.5rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      border-left: 4px solid #007bff;

      h4 {
        font-size: 1.1rem;
        color: #333;
        margin-bottom: 0.5rem;
        font-weight: 600;
      }

      p {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 1rem;
        line-height: 1.5;
      }

      .badge-examples {
        display: flex;
        gap: 0.75rem;
        align-items: center;
        flex-wrap: wrap;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .demo-section {
    padding: 1rem;

    .demo-header {
      h2 {
        font-size: 2rem;
      }

      p {
        font-size: 1rem;
      }
    }

    .variant-section {
      padding: 1rem;
      margin-bottom: 2rem;

      h3 {
        font-size: 1.5rem;
      }

      .badge-group {
        .badge-row {
          padding: 1rem;

          .badge-examples {
            gap: 0.5rem;
            justify-content: center;

            ava-badges {
              margin: 0.25rem;
            }
          }
        }
      }
    }

    .usage-examples {
      grid-template-columns: 1fr;
      gap: 1rem;

      .example-item {
        padding: 1rem;
      }
    }
  }
}
