<div class="demo-section">
  <div class="demo-header">
    <h2>Smart Count Badges</h2>
    <p>
      Explore intelligent count formatting and various count scenarios with
      different badge states and sizes.
    </p>
  </div>

  <!-- ================== COUNT FORMATTING ================== -->
  <div class="count-section">
    <h3>Smart Count Formatting</h3>
    <p>
      Badge counts automatically format large numbers for better readability and
      user experience.
    </p>

    <div class="formatting-examples">
      <div class="example-group">
        <h4>Small Numbers</h4>
        <p>Numbers under 100 display as-is for precise counting.</p>
        <div class="badge-examples">
          <ava-badges
            state="high-priority"
            size="medium"
            [count]="1"
          ></ava-badges>
          <ava-badges
            state="medium-priority"
            size="medium"
            [count]="5"
          ></ava-badges>
          <ava-badges
            state="low-priority"
            size="medium"
            [count]="12"
          ></ava-badges>
          <ava-badges state="neutral" size="medium" [count]="25"></ava-badges>
          <ava-badges
            state="information"
            size="medium"
            [count]="99"
          ></ava-badges>
        </div>
      </div>

      <div class="example-group">
        <h4>Medium Numbers</h4>
        <p>Numbers 100-999 display in full for detailed counting.</p>
        <div class="badge-examples">
          <ava-badges
            state="high-priority"
            size="medium"
            [count]="100"
          ></ava-badges>
          <ava-badges
            state="medium-priority"
            size="medium"
            [count]="250"
          ></ava-badges>
          <ava-badges
            state="low-priority"
            size="medium"
            [count]="500"
          ></ava-badges>
          <ava-badges state="neutral" size="medium" [count]="750"></ava-badges>
          <ava-badges
            state="information"
            size="medium"
            [count]="999"
          ></ava-badges>
        </div>
      </div>

      <div class="example-group">
        <h4>Large Numbers</h4>
        <p>
          Numbers 1000+ use smart formatting (e.g., 1K, 1.5K, 2M) for better
          readability.
        </p>
        <div class="badge-examples">
          <ava-badges
            state="high-priority"
            size="medium"
            [count]="1000"
          ></ava-badges>
          <ava-badges
            state="medium-priority"
            size="medium"
            [count]="1500"
          ></ava-badges>
          <ava-badges
            state="low-priority"
            size="medium"
            [count]="2500"
          ></ava-badges>
          <ava-badges
            state="neutral"
            size="medium"
            [count]="10000"
          ></ava-badges>
          <ava-badges
            state="information"
            size="medium"
            [count]="1000000"
          ></ava-badges>
        </div>
      </div>
    </div>
  </div>

  <!-- ================== COUNT BY STATE ================== -->
  <div class="count-section">
    <h3>Count Examples by State</h3>
    <p>
      Different count values across various badge states to show visual
      hierarchy and priority.
    </p>

    <div class="state-counts">
      <div class="state-group high-priority">
        <h4>High Priority Counts</h4>
        <p>
          Critical notifications and urgent alerts with various count values.
        </p>
        <div class="badge-examples">
          <ava-badges
            state="high-priority"
            size="large"
            [count]="3"
          ></ava-badges>
          <ava-badges
            state="high-priority"
            size="medium"
            [count]="7"
          ></ava-badges>
          <ava-badges
            state="high-priority"
            size="small"
            [count]="15"
          ></ava-badges>
          <ava-badges
            state="high-priority"
            size="xsmall"
            [count]="99"
          ></ava-badges>
        </div>
      </div>

      <div class="state-group medium-priority">
        <h4>Medium Priority Counts</h4>
        <p>Warnings and pending actions that need attention.</p>
        <div class="badge-examples">
          <ava-badges
            state="medium-priority"
            size="large"
            [count]="5"
          ></ava-badges>
          <ava-badges
            state="medium-priority"
            size="medium"
            [count]="12"
          ></ava-badges>
          <ava-badges
            state="medium-priority"
            size="small"
            [count]="25"
          ></ava-badges>
          <ava-badges
            state="medium-priority"
            size="xsmall"
            [count]="150"
          ></ava-badges>
        </div>
      </div>

      <div class="state-group low-priority">
        <h4>Low Priority Counts</h4>
        <p>Success states and completed actions with positive counts.</p>
        <div class="badge-examples">
          <ava-badges
            state="low-priority"
            size="large"
            [count]="8"
          ></ava-badges>
          <ava-badges
            state="low-priority"
            size="medium"
            [count]="18"
          ></ava-badges>
          <ava-badges
            state="low-priority"
            size="small"
            [count]="42"
          ></ava-badges>
          <ava-badges
            state="low-priority"
            size="xsmall"
            [count]="250"
          ></ava-badges>
        </div>
      </div>

      <div class="state-group neutral">
        <h4>Neutral Counts</h4>
        <p>General information and default states with neutral counts.</p>
        <div class="badge-examples">
          <ava-badges state="neutral" size="large" [count]="6"></ava-badges>
          <ava-badges state="neutral" size="medium" [count]="14"></ava-badges>
          <ava-badges state="neutral" size="small" [count]="33"></ava-badges>
          <ava-badges state="neutral" size="xsmall" [count]="175"></ava-badges>
        </div>
      </div>

      <div class="state-group information">
        <h4>Information Counts</h4>
        <p>
          Helpful information and contextual updates with informational counts.
        </p>
        <div class="badge-examples">
          <ava-badges state="information" size="large" [count]="9"></ava-badges>
          <ava-badges
            state="information"
            size="medium"
            [count]="21"
          ></ava-badges>
          <ava-badges
            state="information"
            size="small"
            [count]="47"
          ></ava-badges>
          <ava-badges
            state="information"
            size="xsmall"
            [count]="300"
          ></ava-badges>
        </div>
      </div>
    </div>
  </div>

  <!-- ================== COUNT BY SIZE ================== -->
  <div class="count-section">
    <h3>Count Display by Size</h3>
    <p>
      How count values are displayed across different badge sizes for optimal
      readability.
    </p>

    <div class="size-counts">
      <div class="size-group">
        <h4>XSmall Size</h4>
        <p>
          Compact display for tight spaces, shows essential count information.
        </p>
        <div class="badge-examples">
          <ava-badges
            state="high-priority"
            size="xsmall"
            [count]="5"
          ></ava-badges>
          <ava-badges
            state="medium-priority"
            size="xsmall"
            [count]="25"
          ></ava-badges>
          <ava-badges
            state="low-priority"
            size="xsmall"
            [count]="100"
          ></ava-badges>
          <ava-badges state="neutral" size="xsmall" [count]="1000"></ava-badges>
        </div>
      </div>

      <div class="size-group">
        <h4>Small Size</h4>
        <p>Standard small size with good readability for most use cases.</p>
        <div class="badge-examples">
          <ava-badges
            state="high-priority"
            size="small"
            [count]="5"
          ></ava-badges>
          <ava-badges
            state="medium-priority"
            size="small"
            [count]="25"
          ></ava-badges>
          <ava-badges
            state="low-priority"
            size="small"
            [count]="100"
          ></ava-badges>
          <ava-badges state="neutral" size="small" [count]="1000"></ava-badges>
        </div>
      </div>

      <div class="size-group">
        <h4>Medium Size</h4>
        <p>Default size with optimal balance of visibility and space usage.</p>
        <div class="badge-examples">
          <ava-badges
            state="high-priority"
            size="medium"
            [count]="5"
          ></ava-badges>
          <ava-badges
            state="medium-priority"
            size="medium"
            [count]="25"
          ></ava-badges>
          <ava-badges
            state="low-priority"
            size="medium"
            [count]="100"
          ></ava-badges>
          <ava-badges state="neutral" size="medium" [count]="1000"></ava-badges>
        </div>
      </div>

      <div class="size-group">
        <h4>Large Size</h4>
        <p>Prominent display for important counts that need high visibility.</p>
        <div class="badge-examples">
          <ava-badges
            state="high-priority"
            size="large"
            [count]="5"
          ></ava-badges>
          <ava-badges
            state="medium-priority"
            size="large"
            [count]="25"
          ></ava-badges>
          <ava-badges
            state="low-priority"
            size="large"
            [count]="100"
          ></ava-badges>
          <ava-badges state="neutral" size="large" [count]="1000"></ava-badges>
        </div>
      </div>
    </div>
  </div>

  <!-- ================== REAL-WORLD SCENARIOS ================== -->
  <div class="count-section">
    <h3>Real-World Count Scenarios</h3>
    <p>
      Common use cases and count patterns you'll encounter in real applications.
    </p>

    <div class="scenario-examples">
      <div class="scenario-item">
        <h4>Notification Center</h4>
        <p>Unread messages, alerts, and system notifications.</p>
        <div class="badge-examples">
          <ava-badges
            state="high-priority"
            size="medium"
            [count]="3"
          ></ava-badges>
          <ava-badges
            state="medium-priority"
            size="medium"
            [count]="12"
          ></ava-badges>
          <ava-badges
            state="information"
            size="medium"
            [count]="47"
          ></ava-badges>
        </div>
      </div>

      <div class="scenario-item">
        <h4>E-commerce Cart</h4>
        <p>Shopping cart items, wishlist counts, and order notifications.</p>
        <div class="badge-examples">
          <ava-badges
            state="low-priority"
            size="medium"
            [count]="2"
          ></ava-badges>
          <ava-badges state="neutral" size="medium" [count]="15"></ava-badges>
          <ava-badges
            state="information"
            size="medium"
            [count]="99"
          ></ava-badges>
        </div>
      </div>

      <div class="scenario-item">
        <h4>Social Media</h4>
        <p>Likes, comments, shares, and friend requests.</p>
        <div class="badge-examples">
          <ava-badges
            state="high-priority"
            size="medium"
            [count]="5"
          ></ava-badges>
          <ava-badges
            state="medium-priority"
            size="medium"
            [count]="25"
          ></ava-badges>
          <ava-badges
            state="low-priority"
            size="medium"
            [count]="1500"
          ></ava-badges>
        </div>
      </div>

      <div class="scenario-item">
        <h4>Dashboard Metrics</h4>
        <p>System status, performance indicators, and data counts.</p>
        <div class="badge-examples">
          <ava-badges
            state="high-priority"
            size="medium"
            [count]="1"
          ></ava-badges>
          <ava-badges
            state="medium-priority"
            size="medium"
            [count]="8"
          ></ava-badges>
          <ava-badges
            state="low-priority"
            size="medium"
            [count]="42"
          ></ava-badges>
        </div>
      </div>
    </div>
  </div>

  <!-- ================== COUNT LIMITS ================== -->
  <div class="count-section">
    <h3>Count Limits and Edge Cases</h3>
    <p>Understanding how badges handle extreme count values and edge cases.</p>

    <div class="limit-examples">
      <div class="limit-group">
        <h4>Zero and Single Digits</h4>
        <p>Special handling for zero and single-digit counts.</p>
        <div class="badge-examples">
          <ava-badges state="neutral" size="medium" [count]="0"></ava-badges>
          <ava-badges
            state="information"
            size="medium"
            [count]="1"
          ></ava-badges>
          <ava-badges
            state="low-priority"
            size="medium"
            [count]="2"
          ></ava-badges>
          <ava-badges
            state="medium-priority"
            size="medium"
            [count]="9"
          ></ava-badges>
        </div>
      </div>

      <div class="limit-group">
        <h4>Large Number Formatting</h4>
        <p>Smart formatting for very large numbers to maintain readability.</p>
        <div class="badge-examples">
          <ava-badges state="neutral" size="medium" [count]="9999"></ava-badges>
          <ava-badges
            state="information"
            size="medium"
            [count]="15000"
          ></ava-badges>
          <ava-badges
            state="low-priority"
            size="medium"
            [count]="100000"
          ></ava-badges>
          <ava-badges
            state="high-priority"
            size="medium"
            [count]="1000000"
          ></ava-badges>
        </div>
      </div>
    </div>
  </div>
</div>
