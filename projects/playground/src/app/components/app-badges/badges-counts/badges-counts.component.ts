import { Component, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BadgesComponent } from '../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-badges-counts',
  standalone: true,
  imports: [CommonModule, BadgesComponent],
  templateUrl: './badges-counts.component.html',
  styleUrls: ['./badges-counts.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class BadgesCountsComponent {
  // Component logic can be added here if needed
}
