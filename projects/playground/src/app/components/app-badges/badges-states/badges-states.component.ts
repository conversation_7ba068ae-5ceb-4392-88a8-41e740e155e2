import { Component, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BadgesComponent } from '../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-badges-states',
  standalone: true,
  imports: [CommonModule, BadgesComponent],
  templateUrl: './badges-states.component.html',
  styleUrls: ['./badges-states.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class BadgesStatesComponent {
  // Component logic can be added here if needed
}
