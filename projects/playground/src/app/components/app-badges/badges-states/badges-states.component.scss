.demo-section {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;

  .demo-header {
    text-align: center;
    margin-bottom: 3rem;

    h2 {
      font-size: 2.5rem;
      color: #333;
      margin-bottom: 1rem;
    }

    p {
      font-size: 1.1rem;
      color: #666;
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;
    }
  }

  // State Overview Section
  .state-overview {
    margin-bottom: 4rem;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;

    h3 {
      font-size: 1.8rem;
      color: #333;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid #007bff;
    }

    > p {
      font-size: 1rem;
      color: #666;
      margin-bottom: 2rem;
      line-height: 1.6;
    }

    .state-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;

      .state-item {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        border-left: 4px solid;

        &.high-priority {
          border-left-color: #dc3545;
        }

        &.medium-priority {
          border-left-color: #ffc107;
        }

        &.low-priority {
          border-left-color: #28a745;
        }

        &.neutral {
          border-left-color: #6c757d;
        }

        &.information {
          border-left-color: #17a2b8;
        }

        h4 {
          font-size: 1.2rem;
          color: #333;
          margin-bottom: 0.75rem;
          font-weight: 600;
        }

        p {
          font-size: 0.9rem;
          color: #666;
          margin-bottom: 1rem;
          line-height: 1.5;
        }

        .state-examples {
          display: flex;
          gap: 0.75rem;
          align-items: center;
          flex-wrap: wrap;
        }
      }
    }
  }

  // Size Comparison Section
  .size-comparison {
    margin-bottom: 4rem;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;

    h3 {
      font-size: 1.8rem;
      color: #333;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid #007bff;
    }

    > p {
      font-size: 1rem;
      color: #666;
      margin-bottom: 2rem;
      line-height: 1.6;
    }

    .comparison-table {
      background: white;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .table-header {
        display: grid;
        grid-template-columns: 1fr repeat(4, 1fr);
        background: #007bff;
        color: white;

        .header-cell {
          padding: 1rem;
          text-align: center;
          font-weight: 600;
          border-right: 1px solid rgba(255, 255, 255, 0.2);

          &:first-child {
            text-align: left;
          }
        }
      }

      .table-row {
        display: grid;
        grid-template-columns: 1fr repeat(4, 1fr);
        border-bottom: 1px solid #e9ecef;

        &:nth-child(even) {
          background: #f8f9fa;
        }

        .state-label {
          padding: 1rem;
          font-weight: 600;
          color: #333;
          display: flex;
          align-items: center;
        }

        .badge-cell {
          padding: 1rem;
          display: flex;
          align-items: center;
          justify-content: center;
          border-right: 1px solid #e9ecef;
        }
      }
    }
  }

  // Variant Comparison Section
  .variant-comparison {
    margin-bottom: 4rem;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;

    h3 {
      font-size: 1.8rem;
      color: #333;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid #007bff;
    }

    > p {
      font-size: 1rem;
      color: #666;
      margin-bottom: 2rem;
      line-height: 1.6;
    }

    .variant-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;

      .variant-section {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        h4 {
          font-size: 1.1rem;
          color: #333;
          margin-bottom: 1rem;
          font-weight: 600;
          text-align: center;
        }

        .variant-examples {
          display: flex;
          gap: 1rem;
          align-items: center;
          justify-content: center;
          flex-wrap: wrap;
        }
      }
    }
  }

  // Usage Guidelines Section
  .usage-guidelines {
    margin-bottom: 4rem;
    padding: 2rem;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;

    h3 {
      font-size: 1.8rem;
      color: #333;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid #007bff;
    }

    > p {
      font-size: 1rem;
      color: #666;
      margin-bottom: 2rem;
      line-height: 1.6;
    }

    .guidelines-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;

      .guideline-item {
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

        h4 {
          font-size: 1.1rem;
          color: #333;
          margin-bottom: 1rem;
          font-weight: 600;
        }

        ul {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;
            line-height: 1.4;

            &:before {
              content: "•";
              color: #007bff;
              font-weight: bold;
              position: absolute;
              left: 0;
            }
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .demo-section {
    padding: 1rem;

    .demo-header {
      h2 {
        font-size: 2rem;
      }

      p {
        font-size: 1rem;
      }
    }

    .state-overview,
    .size-comparison,
    .variant-comparison,
    .usage-guidelines {
      padding: 1rem;
      margin-bottom: 2rem;

      h3 {
        font-size: 1.5rem;
      }

      .state-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .comparison-table {
        .table-header,
        .table-row {
          grid-template-columns: 1fr;
          gap: 0.5rem;
        }

        .table-header {
          .header-cell {
            border-right: none;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
          }
        }

        .table-row {
          .badge-cell {
            border-right: none;
            border-bottom: 1px solid #e9ecef;
          }
        }
      }

      .variant-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .guidelines-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
      }
    }
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .demo-section {
    .state-overview,
    .size-comparison,
    .variant-comparison,
    .usage-guidelines {
      background: #2d3748;
      border-color: #4a5568;

      h3 {
        color: #e2e8f0;
        border-bottom-color: #63b3ed;
      }

      > p {
        color: #a0aec0;
      }
    }

    .state-grid .state-item,
    .comparison-table,
    .variant-grid .variant-section,
    .guidelines-grid .guideline-item {
      background: #4a5568;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);

      h4 {
        color: #e2e8f0;
      }

      p,
      li {
        color: #a0aec0;
      }
    }

    .comparison-table {
      .table-header {
        background: #2b6cb0;
      }

      .table-row {
        border-bottom-color: #4a5568;

        &:nth-child(even) {
          background: #2d3748;
        }

        .state-label {
          color: #e2e8f0;
        }
      }
    }
  }
}
