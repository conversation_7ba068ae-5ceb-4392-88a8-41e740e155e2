<div class="demo-section">
  <div class="demo-header">
    <h2>Badge States</h2>
    <p>
      Explore different badge states and their visual representations across
      various sizes and variants.
    </p>
  </div>

  <!-- ================== STATE OVERVIEW ================== -->
  <div class="state-overview">
    <h3>State Overview</h3>
    <p>
      Badge states define the color scheme and visual priority of the badge.
      Each state has specific use cases and semantic meaning.
    </p>

    <div class="state-grid">
      <div class="state-item high-priority">
        <h4>High Priority</h4>
        <p>
          Used for critical alerts, errors, or urgent notifications that require
          immediate attention.
        </p>
        <div class="state-examples">
          <ava-badges
            state="high-priority"
            size="medium"
            [count]="5"
          ></ava-badges>
          <ava-badges
            state="high-priority"
            size="medium"
            iconName="alert-triangle"
            iconColor="white"
            [iconSize]="12"
          ></ava-badges>
          <ava-badges
            state="high-priority"
            size="medium"
            variant="dots"
          ></ava-badges>
        </div>
      </div>

      <div class="state-item medium-priority">
        <h4>Medium Priority</h4>
        <p>
          Used for warnings, pending actions, or notifications that need
          attention but aren't critical.
        </p>
        <div class="state-examples">
          <ava-badges
            state="medium-priority"
            size="medium"
            [count]="3"
          ></ava-badges>
          <ava-badges
            state="medium-priority"
            size="medium"
            iconName="clock"
            iconColor="white"
            [iconSize]="12"
          ></ava-badges>
          <ava-badges
            state="medium-priority"
            size="medium"
            variant="dots"
          ></ava-badges>
        </div>
      </div>

      <div class="state-item low-priority">
        <h4>Low Priority</h4>
        <p>
          Used for success states, completed actions, or informational content
          that's positive.
        </p>
        <div class="state-examples">
          <ava-badges
            state="low-priority"
            size="medium"
            [count]="12"
          ></ava-badges>
          <ava-badges
            state="low-priority"
            size="medium"
            iconName="check-circle"
            iconColor="white"
            [iconSize]="12"
          ></ava-badges>
          <ava-badges
            state="low-priority"
            size="medium"
            variant="dots"
          ></ava-badges>
        </div>
      </div>

      <div class="state-item neutral">
        <h4>Neutral</h4>
        <p>
          Used for general information, default states, or content that doesn't
          have specific priority.
        </p>
        <div class="state-examples">
          <ava-badges state="neutral" size="medium" [count]="8"></ava-badges>
          <ava-badges
            state="neutral"
            size="medium"
            iconName="info"
            iconColor="black"
            [iconSize]="12"
          ></ava-badges>
          <ava-badges state="neutral" size="medium" variant="dots"></ava-badges>
        </div>
      </div>

      <div class="state-item information">
        <h4>Information</h4>
        <p>
          Used for informational content, help text, or general updates that
          provide context.
        </p>
        <div class="state-examples">
          <ava-badges
            state="information"
            size="medium"
            [count]="15"
          ></ava-badges>
          <ava-badges
            state="information"
            size="medium"
            iconName="help-circle"
            iconColor="black"
            [iconSize]="12"
          ></ava-badges>
          <ava-badges
            state="information"
            size="medium"
            variant="dots"
          ></ava-badges>
        </div>
      </div>
    </div>
  </div>

  <!-- ================== SIZE COMPARISON ================== -->
  <div class="size-comparison">
    <h3>Size Comparison by State</h3>
    <p>
      See how each state looks across different sizes to understand the visual
      hierarchy.
    </p>

    <div class="comparison-table">
      <div class="table-header">
        <div class="header-cell">State</div>
        <div class="header-cell">XSmall</div>
        <div class="header-cell">Small</div>
        <div class="header-cell">Medium</div>
        <div class="header-cell">Large</div>
      </div>

      <div class="table-row">
        <div class="state-label">High Priority</div>
        <div class="badge-cell">
          <ava-badges
            state="high-priority"
            size="xsmall"
            [count]="9"
          ></ava-badges>
        </div>
        <div class="badge-cell">
          <ava-badges
            state="high-priority"
            size="small"
            [count]="9"
          ></ava-badges>
        </div>
        <div class="badge-cell">
          <ava-badges
            state="high-priority"
            size="medium"
            [count]="9"
          ></ava-badges>
        </div>
        <div class="badge-cell">
          <ava-badges
            state="high-priority"
            size="large"
            [count]="9"
          ></ava-badges>
        </div>
      </div>

      <div class="table-row">
        <div class="state-label">Medium Priority</div>
        <div class="badge-cell">
          <ava-badges
            state="medium-priority"
            size="xsmall"
            [count]="9"
          ></ava-badges>
        </div>
        <div class="badge-cell">
          <ava-badges
            state="medium-priority"
            size="small"
            [count]="9"
          ></ava-badges>
        </div>
        <div class="badge-cell">
          <ava-badges
            state="medium-priority"
            size="medium"
            [count]="9"
          ></ava-badges>
        </div>
        <div class="badge-cell">
          <ava-badges
            state="medium-priority"
            size="large"
            [count]="9"
          ></ava-badges>
        </div>
      </div>

      <div class="table-row">
        <div class="state-label">Low Priority</div>
        <div class="badge-cell">
          <ava-badges
            state="low-priority"
            size="xsmall"
            [count]="9"
          ></ava-badges>
        </div>
        <div class="badge-cell">
          <ava-badges
            state="low-priority"
            size="small"
            [count]="9"
          ></ava-badges>
        </div>
        <div class="badge-cell">
          <ava-badges
            state="low-priority"
            size="medium"
            [count]="9"
          ></ava-badges>
        </div>
        <div class="badge-cell">
          <ava-badges
            state="low-priority"
            size="large"
            [count]="9"
          ></ava-badges>
        </div>
      </div>

      <div class="table-row">
        <div class="state-label">Neutral</div>
        <div class="badge-cell">
          <ava-badges state="neutral" size="xsmall" [count]="9"></ava-badges>
        </div>
        <div class="badge-cell">
          <ava-badges state="neutral" size="small" [count]="9"></ava-badges>
        </div>
        <div class="badge-cell">
          <ava-badges state="neutral" size="medium" [count]="9"></ava-badges>
        </div>
        <div class="badge-cell">
          <ava-badges state="neutral" size="large" [count]="9"></ava-badges>
        </div>
      </div>

      <div class="table-row">
        <div class="state-label">Information</div>
        <div class="badge-cell">
          <ava-badges
            state="information"
            size="xsmall"
            [count]="9"
          ></ava-badges>
        </div>
        <div class="badge-cell">
          <ava-badges state="information" size="small" [count]="9"></ava-badges>
        </div>
        <div class="badge-cell">
          <ava-badges
            state="information"
            size="medium"
            [count]="9"
          ></ava-badges>
        </div>
        <div class="badge-cell">
          <ava-badges state="information" size="large" [count]="9"></ava-badges>
        </div>
      </div>
    </div>
  </div>

  <!-- ================== VARIANT COMPARISON ================== -->
  <div class="variant-comparison">
    <h3>Variant Comparison by State</h3>
    <p>
      Compare how different variants (count, icon, dots) look across various
      states.
    </p>

    <div class="variant-grid">
      <div class="variant-section">
        <h4>Count Variant</h4>
        <div class="variant-examples">
          <ava-badges
            state="high-priority"
            size="medium"
            [count]="5"
          ></ava-badges>
          <ava-badges
            state="medium-priority"
            size="medium"
            [count]="5"
          ></ava-badges>
          <ava-badges
            state="low-priority"
            size="medium"
            [count]="5"
          ></ava-badges>
          <ava-badges state="neutral" size="medium" [count]="5"></ava-badges>
          <ava-badges
            state="information"
            size="medium"
            [count]="5"
          ></ava-badges>
        </div>
      </div>

      <div class="variant-section">
        <h4>Icon Variant</h4>
        <div class="variant-examples">
          <ava-badges
            state="high-priority"
            size="medium"
            iconName="alert-triangle"
            iconColor="white"
            [iconSize]="12"
          ></ava-badges>
          <ava-badges
            state="medium-priority"
            size="medium"
            iconName="clock"
            iconColor="white"
            [iconSize]="12"
          ></ava-badges>
          <ava-badges
            state="low-priority"
            size="medium"
            iconName="check-circle"
            iconColor="white"
            [iconSize]="12"
          ></ava-badges>
          <ava-badges
            state="neutral"
            size="medium"
            iconName="info"
            iconColor="black"
            [iconSize]="12"
          ></ava-badges>
          <ava-badges
            state="information"
            size="medium"
            iconName="help-circle"
            iconColor="black"
            [iconSize]="12"
          ></ava-badges>
        </div>
      </div>

      <div class="variant-section">
        <h4>Dots Variant</h4>
        <div class="variant-examples">
          <ava-badges
            state="high-priority"
            size="medium"
            variant="dots"
          ></ava-badges>
          <ava-badges
            state="medium-priority"
            size="medium"
            variant="dots"
          ></ava-badges>
          <ava-badges
            state="low-priority"
            size="medium"
            variant="dots"
          ></ava-badges>
          <ava-badges state="neutral" size="medium" variant="dots"></ava-badges>
          <ava-badges
            state="information"
            size="medium"
            variant="dots"
          ></ava-badges>
        </div>
      </div>
    </div>
  </div>

  <!-- ================== USAGE GUIDELINES ================== -->
  <div class="usage-guidelines">
    <h3>Usage Guidelines</h3>
    <p>Best practices for choosing the right badge state for your use case.</p>

    <div class="guidelines-grid">
      <div class="guideline-item">
        <h4>High Priority</h4>
        <ul>
          <li>Critical system errors or failures</li>
          <li>Security alerts or breaches</li>
          <li>Urgent user notifications</li>
          <li>Payment failures or timeouts</li>
        </ul>
      </div>

      <div class="guideline-item">
        <h4>Medium Priority</h4>
        <ul>
          <li>Warning messages</li>
          <li>Pending approvals or actions</li>
          <li>System maintenance notices</li>
          <li>Feature deprecation warnings</li>
        </ul>
      </div>

      <div class="guideline-item">
        <h4>Low Priority</h4>
        <ul>
          <li>Successful operations</li>
          <li>Completed tasks or processes</li>
          <li>Positive status indicators</li>
          <li>Confirmation messages</li>
        </ul>
      </div>

      <div class="guideline-item">
        <h4>Neutral</h4>
        <ul>
          <li>General information</li>
          <li>Default states</li>
          <li>Non-critical updates</li>
          <li>Background processes</li>
        </ul>
      </div>

      <div class="guideline-item">
        <h4>Information</h4>
        <ul>
          <li>Help text and tips</li>
          <li>Feature announcements</li>
          <li>Educational content</li>
          <li>Contextual information</li>
        </ul>
      </div>
    </div>
  </div>
</div>
