<div class="demo-container">
  <div class="demo-section">
    <h3 style="color: var(--color-text-primary)">Labeled Textarea</h3>
    <ava-textarea
      [(ngModel)]="labeledValue"
      label="Description"
      placeholder="Enter your description..."
      [rows]="3"
      (textareaChange)="onLabeledChange($event)"
    ></ava-textarea>
  </div>

  <div class="demo-section">
    <h3 style="color: var(--color-text-primary)">Textarea with Helper Text</h3>
    <ava-textarea
      [(ngModel)]="helperValue"
      label="Comments"
      placeholder="Share your thoughts..."
      helper="Please provide detailed feedback to help us improve our service."
      [rows]="3"
      (textareaChange)="onHelperChange($event)"
    ></ava-textarea>
  </div>

  <div class="demo-section">
    <h3 style="color: var(--color-text-primary)">Textarea with Error State</h3>
    <ava-textarea
      [(ngModel)]="errorValue"
      label="Email"
      placeholder="Enter your email..."
      error="Please enter a valid email address."
      [rows]="3"
      (textareaChange)="onErrorChange($event)"
    ></ava-textarea>
  </div>

  <div class="demo-section">
    <h3 style="color: var(--color-text-primary)">Required Textarea</h3>
    <ava-textarea
      [(ngModel)]="requiredValue"
      label="Required Field"
      placeholder="This field is required..."
      [required]="true"
      [rows]="3"
      (textareaChange)="onRequiredChange($event)"
    ></ava-textarea>
  </div>

  <div class="demo-section">
    <h3 style="color: var(--color-text-primary)">
      Textarea with Character Limit
    </h3>
    <ava-textarea
      [(ngModel)]="maxLengthValue"
      label="Bio"
      placeholder="Tell us about yourself (max 200 characters)..."
      [maxlength]="200"
      [rows]="3"
      (textareaChange)="onMaxLengthChange($event)"
    ></ava-textarea>
    <div class="char-count">Characters: {{ maxLengthValue.length }}/200</div>
  </div>
</div>
