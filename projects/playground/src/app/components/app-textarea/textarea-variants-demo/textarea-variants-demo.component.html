<div class="demo-container">
  <div class="demo-section">
    <h3 style="color: var(--color-text-primary)">Default Variant</h3>
    <ava-textarea
      [(ngModel)]="defaultValue"
      placeholder="Default variant..."
      variant="default"
      [rows]="3"
      (textareaChange)="onDefaultChange($event)"
    ></ava-textarea>
  </div>

  <div class="demo-section">
    <h3 style="color: var(--color-text-primary)">Primary Variant</h3>
    <ava-textarea
      [(ngModel)]="primaryValue"
      placeholder="Primary variant..."
      variant="primary"
      [rows]="3"
      (textareaChange)="onPrimaryChange($event)"
    ></ava-textarea>
  </div>

  <div class="demo-section">
    <h3 style="color: var(--color-text-primary)">Success Variant</h3>
    <ava-textarea
      [(ngModel)]="successValue"
      placeholder="Success variant..."
      variant="success"
      [rows]="3"
      (textareaChange)="onSuccessChange($event)"
    ></ava-textarea>
  </div>

  <div class="demo-section">
    <h3 style="color: var(--color-text-primary)">Error Variant</h3>
    <ava-textarea
      [(ngModel)]="errorValue"
      placeholder="Error variant..."
      variant="error"
      [rows]="3"
      (textareaChange)="onErrorChange($event)"
    ></ava-textarea>
  </div>

  <div class="demo-section">
    <h3 style="color: var(--color-text-primary)">Warning Variant</h3>
    <ava-textarea
      [(ngModel)]="warningValue"
      placeholder="Warning variant..."
      variant="warning"
      [rows]="3"
      (textareaChange)="onWarningChange($event)"
    ></ava-textarea>
  </div>

  <div class="demo-section">
    <h3 style="color: var(--color-text-primary)">Info Variant</h3>
    <ava-textarea
      [(ngModel)]="infoValue"
      placeholder="Info variant..."
      variant="info"
      [rows]="3"
      (textareaChange)="onInfoChange($event)"
    ></ava-textarea>
  </div>
  <!-- 
  <div class="demo-section">
    <h3>Processing Variants (Different Colors)</h3>
    <div style="display: grid; gap: 1rem">
      <ava-textarea
        [(ngModel)]="processingPrimaryValue"
        placeholder="Processing primary (blue)..."
        variant="primary"
        [processing]="true"
        [rows]="2"
        helper="Primary processing with blue glow"
        (textareaChange)="onProcessingPrimaryChange($event)"
      ></ava-textarea>

      <ava-textarea
        [(ngModel)]="processingSuccessValue"
        placeholder="Processing success (green)..."
        variant="success"
        [processing]="true"
        [rows]="2"
        helper="Success processing with green glow"
        (textareaChange)="onProcessingSuccessChange($event)"
      ></ava-textarea>

      <ava-textarea
        [(ngModel)]="processingWarningValue"
        placeholder="Processing warning (amber)..."
        variant="warning"
        [processing]="true"
        [rows]="2"
        helper="Warning processing with amber glow"
        (textareaChange)="onProcessingWarningChange($event)"
      ></ava-textarea>

      <ava-textarea
        [(ngModel)]="processingErrorValue"
        placeholder="Processing error (red)..."
        variant="error"
        [processing]="true"
        [rows]="2"
        helper="Error processing with red glow"
        (textareaChange)="onProcessingErrorChange($event)"
      ></ava-textarea>

      <ava-textarea
        [(ngModel)]="processingInfoValue"
        placeholder="Processing info (sky blue)..."
        variant="info"
        [processing]="true"
        [rows]="2"
        helper="Info processing with sky blue glow"
        (textareaChange)="onProcessingInfoChange($event)"
      ></ava-textarea>
    </div>
  </div> -->
</div>
