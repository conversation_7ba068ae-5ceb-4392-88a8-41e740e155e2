<div class="demo-container">
  <div class="demo-section">
    <h3>API Reference</h3>
    <p>
      Complete list of properties and events available for the textarea
      component.
    </p>

    <h4>Properties</h4>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Default</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let prop of apiProps">
          <td>
            <code>{{ prop.name }}</code>
          </td>
          <td>
            <code>{{ prop.type }}</code>
          </td>
          <td>
            <code>{{ prop.default }}</code>
          </td>
          <td>{{ prop.description }}</td>
        </tr>
      </tbody>
    </table>

    <h4>Events</h4>
    <table class="api-table">
      <thead>
        <tr>
          <th>Event</th>
          <th>Type</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let event of events">
          <td>
            <code>{{ event.name }}</code>
          </td>
          <td>
            <code>{{ event.type }}</code>
          </td>
          <td>{{ event.description }}</td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
