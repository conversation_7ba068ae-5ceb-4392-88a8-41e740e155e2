.demo-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  margin-bottom: 3rem;

  h3 {
    color: #333;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    font-weight: 600;
  }

  p {
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.6;
  }
}

.icon-examples {
  .icon-example {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;

    .example-header {
      margin-bottom: 1rem;

      h4 {
        color: #333;
        margin-bottom: 0.5rem;
        font-size: 1.2rem;
        font-weight: 500;
      }

      p {
        color: #666;
        margin-bottom: 0;
        font-size: 0.9rem;
      }
    }

    .textarea-demo {
      max-width: 600px;
    }
  }
}

.use-case-examples {
  .use-case-group {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;

    h4 {
      color: #333;
      margin-bottom: 0.5rem;
      font-size: 1.2rem;
      font-weight: 500;
    }

    p {
      color: #666;
      margin-bottom: 1rem;
      font-size: 0.9rem;
    }

    .textarea-examples {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1rem;

      .textarea-example {
        h5 {
          color: #333;
          margin-bottom: 0.5rem;
          font-size: 1rem;
          font-weight: 500;
        }
      }
    }
  }
}

.guidelines-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;

  .guideline-card {
    padding: 1.5rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;

    h4 {
      color: #333;
      margin-bottom: 1rem;
      font-size: 1.1rem;
      font-weight: 500;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        color: #666;
        margin-bottom: 0.5rem;
        padding-left: 1.5rem;
        position: relative;
        line-height: 1.5;

        &::before {
          content: "•";
          color: #007bff;
          font-weight: bold;
          position: absolute;
          left: 0;
        }
      }
    }
  }
}

.interactive-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;

  .interactive-example {
    padding: 1.5rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    background: #fafafa;

    h4 {
      color: #333;
      margin-bottom: 0.5rem;
      font-size: 1.2rem;
      font-weight: 500;
    }

    p {
      color: #666;
      margin-bottom: 1rem;
      font-size: 0.9rem;
    }
  }
}

// Icon hover effects
ava-icon {
  transition: transform 0.2s ease, opacity 0.2s ease;

  &:hover {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

// Responsive design
@media (max-width: 768px) {
  .demo-container {
    padding: 1rem;
  }

  .guidelines-grid {
    grid-template-columns: 1fr;
  }

  .use-case-examples .use-case-group .textarea-examples {
    grid-template-columns: 1fr;
  }

  .interactive-examples {
    grid-template-columns: 1fr;
  }
}

// Focus states for accessibility
ava-textarea:focus-within {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

// Custom styling for processing gradient border
ava-textarea[processinggradientborder="true"] {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
    linear-gradient(45deg, #fa709a, #e5cb3a) border-box;
}
