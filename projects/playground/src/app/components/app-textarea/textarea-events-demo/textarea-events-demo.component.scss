.demo-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.demo-section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background: #fafafa;

  h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
  }

  p {
    margin: 0 0 15px 0;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
  }

  ava-textarea {
    display: block;
    margin-bottom: 15px;
  }
}

.event-log {
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f5f5f5;
  border-bottom: 1px solid #ddd;

  h4 {
    margin: 0;
    font-size: 16px;
    color: #333;
  }
}

.clear-btn {
  padding: 5px 10px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;

  &:hover {
    background: #c82333;
  }
}

.event-list {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
}

.event-item {
  padding: 8px 0;
  border-bottom: 1px solid #eee;
  font-family: monospace;
  font-size: 13px;
  color: #333;

  &:last-child {
    border-bottom: none;
  }
}

.no-events {
  padding: 20px;
  text-align: center;
  color: #999;
  font-style: italic;
}
