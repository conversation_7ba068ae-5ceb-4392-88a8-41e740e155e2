import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AvaTextareaComponent } from '../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-textarea-sizes-demo',
  standalone: true,
  imports: [CommonModule, FormsModule, AvaTextareaComponent],
  templateUrl: './textarea-sizes-demo.component.html',
  styleUrls: ['./textarea-sizes-demo.component.scss'],
})
export class TextareaSizesDemoComponent {
  smallValue = '';
  mediumValue = '';
  largeValue = '';

  onSmallChange(event: Event) {
    const target = event.target as HTMLTextAreaElement;
    console.log('Small textarea value changed:', target.value);
  }

  onMediumChange(event: Event) {
    const target = event.target as HTMLTextAreaElement;
    console.log('Medium textarea value changed:', target.value);
  }

  onLargeChange(event: Event) {
    const target = event.target as HTMLTextAreaElement;
    console.log('Large textarea value changed:', target.value);
  }
}
