<div class="demo-container">
  <div class="demo-section">
    <ava-textarea
      [(ngModel)]="smallValue"
      placeholder="Small textarea..."
      size="sm"
      [rows]="2"
      (textareaChange)="onSmallChange($event)"
    ></ava-textarea>
  </div>

  <div class="demo-section">
    <ava-textarea
      [(ngModel)]="mediumValue"
      placeholder="Medium textarea..."
      size="md"
      [rows]="3"
      (textareaChange)="onMediumChange($event)"
    ></ava-textarea>
  </div>

  <div class="demo-section">
    <ava-textarea
      [(ngModel)]="largeValue"
      placeholder="Large textarea..."
      size="lg"
      [rows]="4"
      (textareaChange)="onLargeChange($event)"
    ></ava-textarea>
  </div>
</div>
