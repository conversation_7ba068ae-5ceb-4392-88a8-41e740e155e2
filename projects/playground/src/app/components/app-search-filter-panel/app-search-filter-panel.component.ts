import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import {
  SearchFilterPanelComponent,
  SearchFilterConfig,
  SearchFilterEvent,
} from '../../../../../play-comp-library/src/lib/composite-components/search-filter-panel/search-filter-panel.component';
import { CardComponent } from '../../../../../play-comp-library/src/lib/components/card/card.component';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';
import { IconComponent } from '../../../../../play-comp-library/src/lib/components/icon/icon.component';

@Component({
  selector: 'ava-search-filter-panel-demo',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    SearchFilterPanelComponent,
    CardComponent,
    ButtonComponent,
    IconComponent,
  ],
  templateUrl: './app-search-filter-panel.component.html',
  styleUrl: './app-search-filter-panel.component.scss',
})
export class AppSearchFilterPanelComponent {
  searchFilterConfig: SearchFilterConfig = {
    searchPlaceholder: 'Search users, projects, or tasks...',
    filters: [
      {
        id: 'status',
        label: 'Status',
        value: '',
        type: 'dropdown',
        options: [
          { name: 'Active', value: 'active' },
          { name: 'Inactive', value: 'inactive' },
          { name: 'Pending', value: 'pending' },
          { name: 'Completed', value: 'completed' },
        ],
        placeholder: 'Select status...',
      },
      {
        id: 'category',
        label: 'Category',
        value: '',
        type: 'dropdown',
        options: [
          { name: 'Development', value: 'development' },
          { name: 'Design', value: 'design' },
          { name: 'Marketing', value: 'marketing' },
          { name: 'Sales', value: 'sales' },
          { name: 'Support', value: 'support' },
        ],
        placeholder: 'Select category...',
        multiple: true,
      },
      {
        id: 'priority',
        label: 'Priority',
        value: '',
        type: 'autocomplete',
        autocompleteOptions: [
          { label: 'High', value: 'high' },
          { label: 'Medium', value: 'medium' },
          { label: 'Low', value: 'low' },
          { label: 'Critical', value: 'critical' },
        ],
        placeholder: 'Type to search priority...',
      },
      {
        id: 'assignee',
        label: 'Assignee',
        value: '',
        type: 'autocomplete',
        autocompleteOptions: [
          { label: 'John Doe', value: 'john-doe' },
          { label: 'Jane Smith', value: 'jane-smith' },
          { label: 'Bob Johnson', value: 'bob-johnson' },
          { label: 'Alice Brown', value: 'alice-brown' },
        ],
        placeholder: 'Search assignee...',
        multiple: true,
      },
    ],
    actions: [
      {
        id: 'search',
        label: 'Search',
        icon: 'search',
        variant: 'primary',
        size: 'medium',
      },
      {
        id: 'export',
        label: 'Export',
        icon: 'download',
        variant: 'secondary',
        size: 'medium',
      },
      {
        id: 'reset',
        label: 'Reset',
        icon: 'refresh-cw',
        variant: 'info',
        size: 'medium',
      },
    ],
    showClearAll: true,
    collapsible: true,
    defaultCollapsed: false,
  };

  loading = false;
  disabled = false;
  lastEvent: SearchFilterEvent | null = null;

  onSearchChange(term: string) {
    console.log('Search term changed:', term);
  }

  onFilterChange(filters: Record<string, string | string[]>) {
    console.log('Filters changed:', filters);
  }

  onActionClick(actionId: string) {
    console.log('Action clicked:', actionId);

    switch (actionId) {
      case 'search':
        this.loading = true;
        setTimeout(() => (this.loading = false), 2000);
        break;
      case 'export':
        alert('Export functionality would be implemented here');
        break;
      case 'reset':
        // Reset is handled by the component itself
        break;
    }
  }

  onClearAll() {
    console.log('All filters cleared');
  }

  onSearchFilterChange(event: SearchFilterEvent) {
    this.lastEvent = event;
    console.log('Combined search filter event:', event);
  }

  toggleLoading() {
    this.loading = !this.loading;
  }

  toggleDisabled() {
    this.disabled = !this.disabled;
  }
}
