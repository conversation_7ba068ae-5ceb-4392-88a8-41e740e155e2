import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  SearchFilterPanelComponent,
  SearchFilterConfig,
  SearchFilterEvent,
} from '../../../../../../../play-comp-library/src/lib/composite-components/search-filter-panel/search-filter-panel.component';

@Component({
  selector: 'ava-search-filter-panel-horizontal-demo',
  standalone: true,
  imports: [CommonModule, SearchFilterPanelComponent],
  template: `
    <div class="demo-main">
      <div class="panel-container">
        <ava-search-filter-panel
          [config]="searchFilterConfig"
          (searchChange)="onSearchChange($event)"
          (filterChange)="onFilterChange($event)"
          (actionClick)="onActionClick($event)"
          (clearAll)="onClearAll()"
          (searchFilterChange)="onSearchFilterChange($event)"
        >
        </ava-search-filter-panel>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-main {
        padding: 4rem 2rem;
        display: flex;
        justify-content: center;
        align-items: flex-start;
      }

      .panel-container {
        max-width: 800px;
        width: 100%;
      }

      /* Responsive Design */
      @media (max-width: 768px) {
        .demo-header {
          padding: 2rem 1rem;
        }

        .demo-title {
          font-size: 2rem;
        }

        .demo-description {
          font-size: 1.1rem;
        }

        .demo-main {
          padding: 2rem 1rem;
        }

        .panel-container {
          border-radius: 16px;
        }

        .features-section {
          padding: 2rem 1rem;
        }

        .features-title {
          font-size: 2rem;
        }

        .features-grid {
          grid-template-columns: 1fr;
          gap: 1.5rem;
        }

        .feature-card {
          padding: 1.5rem;
        }
      }

      @media (max-width: 480px) {
        .demo-title {
          font-size: 1.75rem;
        }

        .demo-description {
          font-size: 1rem;
        }

        .features-title {
          font-size: 1.75rem;
        }
      }
    `,
  ],
})
export class SearchFilterPanelHorizontalDemoComponent {
  searchFilterConfig: SearchFilterConfig = {
    searchPlaceholder: 'Search users, projects, or tasks...',
    filters: [
      {
        id: 'status',
        label: 'Status',
        value: '',
        type: 'dropdown',
        options: [
          { name: 'Active', value: 'active' },
          { name: 'Inactive', value: 'inactive' },
          { name: 'Pending', value: 'pending' },
          { name: 'Completed', value: 'completed' },
        ],
        placeholder: 'Select status...',
      },
      //   {
      //     id: 'category',
      //     label: 'Category',
      //     value: '',
      //     type: 'dropdown',
      //     options: [
      //       { name: 'Development', value: 'development' },
      //       { name: 'Design', value: 'design' },
      //       { name: 'Marketing', value: 'marketing' },
      //       { name: 'Sales', value: 'sales' },
      //       { name: 'Support', value: 'support' },
      //     ],
      //     placeholder: 'Select category...',
      //     multiple: true,
      //   },
      {
        id: 'priority',
        label: 'Priority',
        value: '',
        type: 'autocomplete',
        autocompleteOptions: [
          { label: 'High', value: 'high' },
          { label: 'Medium', value: 'medium' },
          { label: 'Low', value: 'low' },
          { label: 'Critical', value: 'critical' },
        ],
        placeholder: 'Type to search priority...',
      },
      {
        id: 'assignee',
        label: 'Assignee',
        value: '',
        type: 'autocomplete',
        autocompleteOptions: [
          { label: 'John Doe', value: 'john-doe' },
          { label: 'Jane Smith', value: 'jane-smith' },
          { label: 'Bob Johnson', value: 'bob-johnson' },
          { label: 'Alice Brown', value: 'alice-brown' },
        ],
        placeholder: 'Search assignee...',
        multiple: true,
      },
    ],
    actions: [
      {
        id: 'search',
        label: 'Search',
        icon: 'search',
        variant: 'primary',
        size: 'medium',
      },
      {
        id: 'export',
        label: 'Export',
        icon: 'download',
        variant: 'secondary',
        size: 'medium',
      },
    ],
    showClearAll: true,
    // collapsible: true,
    defaultCollapsed: false,
  };

  constructor() {
    console.log('Search Filter Panel Horizontal Demo Component loaded!');
  }

  onSearchChange(term: string) {
    console.log('Search term changed:', term);
  }

  onFilterChange(filters: Record<string, string | string[]>) {
    console.log('Filters changed:', filters);
  }

  onActionClick(actionId: string) {
    console.log('Action clicked:', actionId);
  }

  onClearAll() {
    console.log('All filters cleared');
  }

  onSearchFilterChange(event: SearchFilterEvent) {
    console.log('Combined search filter event:', event);
  }
}
