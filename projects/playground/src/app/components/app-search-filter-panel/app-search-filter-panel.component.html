<div class="search-filter-panel-demo">
  <div class="demo-header">
    <h2>Search Filter Panel Component</h2>
    <p>
      This component demonstrates a comprehensive search and filter interface
      with multiple filter types and actions.
    </p>
    <div class="demo-navigation">
      <a routerLink="/search-filter-panel/basic-usage" class="demo-link">
        <span class="demo-icon">📝</span>
        <span class="demo-text">Basic Usage Demo</span>
      </a>
      <a routerLink="/search-filter-panel/horizontal" class="demo-link">
        <span class="demo-icon">🔄</span>
        <span class="demo-text">Horizontal Demo</span>
      </a>
    </div>
  </div>

  <div class="demo-controls-card">
    <ava-card>
      <div header>
        <h3>Demo Controls</h3>
      </div>
      <div content>
        <div class="controls-grid">
          <ava-button
            (click)="toggleLoading()"
            [variant]="loading ? 'warning' : 'primary'"
            size="medium"
            [label]="loading ? 'Stop Loading' : 'Start Loading'"
            [iconName]="loading ? 'pause' : 'play'"
            [iconSize]="16"
          >
          </ava-button>
          <ava-button
            (click)="toggleDisabled()"
            [variant]="disabled ? 'success' : 'secondary'"
            size="medium"
            [label]="disabled ? 'Enable' : 'Disable'"
            [iconName]="disabled ? 'unlock' : 'lock'"
            [iconSize]="16"
          >
          </ava-button>
        </div>
      </div>
    </ava-card>
  </div>

  <div class="demo-section">
    <ava-card>
      <div header>
        <h3>Search Filter Panel Demo</h3>
        <p>Try interacting with the search and filters below</p>
      </div>
      <div content>
        <ava-search-filter-panel
          [config]="searchFilterConfig"
          [loading]="loading"
          [disabled]="disabled"
          (searchChange)="onSearchChange($event)"
          (filterChange)="onFilterChange($event)"
          (actionClick)="onActionClick($event)"
          (clearAll)="onClearAll()"
          (searchFilterChange)="onSearchFilterChange($event)"
        >
        </ava-search-filter-panel>
      </div>
    </ava-card>
  </div>

  <div class="event-log-card" *ngIf="lastEvent">
    <ava-card>
      <div header>
        <h3>Event Log</h3>
        <p>Real-time events from the search filter panel</p>
      </div>
      <div content>
        <div class="event-content">
          <pre>{{ lastEvent | json }}</pre>
        </div>
      </div>
    </ava-card>
  </div>

  <div class="features-card">
    <ava-card>
      <div header>
        <h3>Features Demonstrated</h3>
        <p>Comprehensive list of capabilities</p>
      </div>
      <div content>
        <div class="features-grid">
          <div class="feature-item">
            <ava-icon
              iconName="search"
              [iconSize]="20"
              iconColor="#007bff"
            ></ava-icon>
            <div class="feature-content">
              <strong>Search:</strong> Global search with autocomplete
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="chevron-down"
              [iconSize]="20"
              iconColor="#28a745"
            ></ava-icon>
            <div class="feature-content">
              <strong>Dropdown Filters:</strong> Single and multiple selection
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="type"
              [iconSize]="20"
              iconColor="#ffc107"
            ></ava-icon>
            <div class="feature-content">
              <strong>Autocomplete Filters:</strong> Searchable options with
              single/multiple selection
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="mouse-pointer-2"
              [iconSize]="20"
              iconColor="#dc3545"
            ></ava-icon>
            <div class="feature-content">
              <strong>Action Buttons:</strong> Primary, secondary, and info
              variants
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="x"
              [iconSize]="20"
              iconColor="#6c757d"
            ></ava-icon>
            <div class="feature-content">
              <strong>Clear All:</strong> Reset all filters and search
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="chevron-up"
              [iconSize]="20"
              iconColor="#17a2b8"
            ></ava-icon>
            <div class="feature-content">
              <strong>Collapsible:</strong> Toggle panel visibility
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="loader"
              [iconSize]="20"
              iconColor="#fd7e14"
            ></ava-icon>
            <div class="feature-content">
              <strong>Loading State:</strong> Visual feedback during operations
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="lock"
              [iconSize]="20"
              iconColor="#6f42c1"
            ></ava-icon>
            <div class="feature-content">
              <strong>Disabled State:</strong> Prevent interactions
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="tag"
              [iconSize]="20"
              iconColor="#e83e8c"
            ></ava-icon>
            <div class="feature-content">
              <strong>Active Filters Badge:</strong> Shows count of active
              filters
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="smartphone"
              [iconSize]="20"
              iconColor="#20c997"
            ></ava-icon>
            <div class="feature-content">
              <strong>Responsive Design:</strong> Adapts to different screen
              sizes
            </div>
          </div>
        </div>
      </div>
    </ava-card>
  </div>
</div>
