/* ===================================================================
   TIMEPICKER DEMO CONTAINER
   =================================================================== */
.timepicker-demo-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--color-text-primary, #1a202c);
  margin-bottom: 1rem;
  text-align: center;
}

.component-description {
  font-size: 1.25rem;
  color: var(--color-text-secondary, #64748b);
  line-height: 1.6;
  text-align: center;
  margin-bottom: 3rem;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* ===================================================================
   DEMO NAVIGATION STYLES
   =================================================================== */
.demo-navigation {
  margin-bottom: 3rem;

  .demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
  }

  .demo-card {
    background: var(--color-background-primary, #ffffff);
    border: 2px solid var(--color-border-default, #e2e8f0);
    border-radius: var(--global-radius-lg, 0.75rem);
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &:hover {
      border-color: var(--color-brand-primary, #e91e63);
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(233, 30, 99, 0.15);
    }

    &.active {
      border-color: var(--color-brand-primary, #e91e63);
      background: linear-gradient(135deg, #fef7ff 0%, #fdf2ff 100%);
      box-shadow: 0 4px 15px rgba(233, 30, 99, 0.1);
    }

    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--color-text-primary, #1a202c);
      margin: 0 0 0.75rem 0;
      line-height: 1.3;
    }

    p {
      color: var(--color-text-secondary, #64748b);
      margin: 0;
      line-height: 1.5;
      font-size: 0.9375rem;
    }

    .demo-arrow {
      position: absolute;
      top: 1.5rem;
      right: 1.5rem;
      opacity: 0;
      transform: translateX(-10px);
      transition: all 0.3s ease;
      color: var(--color-brand-primary, #e91e63);
      font-size: 1.25rem;
      font-weight: bold;

      span {
        display: inline-block;
        transition: transform 0.3s ease;
      }
    }

    &:hover .demo-arrow {
      opacity: 1;
      transform: translateX(0);

      span {
        transform: translateX(4px);
      }
    }

    &.active .demo-arrow {
      opacity: 1;
      transform: translateX(0);
    }
  }
}

/* ===================================================================
   DEMO CONTENT STYLES
   =================================================================== */
.demo-content {
  background: var(--color-background-primary, #ffffff);
  border: 1px solid var(--color-border-default, #e2e8f0);
  border-radius: var(--global-radius-lg, 0.75rem);
  padding: 2rem;
  margin-bottom: 3rem;
  box-shadow: var(--global-elevation-01);

  h2 {
    font-size: 1.875rem;
    font-weight: 600;
    color: var(--color-text-primary, #1a202c);
    margin-bottom: 1rem;
  }

  p {
    color: var(--color-text-secondary, #64748b);
    line-height: 1.6;
    margin-bottom: 1.5rem;
  }

  .demo-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--color-surface-secondary, #f8fafc);
    border-radius: var(--global-radius-md, 0.5rem);
    border: 1px solid var(--color-border-subtle, #e2e8f0);

    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--color-text-primary, #1a202c);
      margin-bottom: 1rem;
    }

    p {
      margin-bottom: 1rem;
    }
  }

  .button-group {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.5rem;

    ava-button {
      flex: 0 0 auto;
    }
  }
}

/* ===================================================================
   RESPONSIVE DESIGN
   =================================================================== */
@media (max-width: 768px) {
  .timepicker-demo-container {
    padding: 1rem;
  }

  h1 {
    font-size: 2rem;
  }

  .component-description {
    font-size: 1.125rem;
  }

  .demo-navigation .demo-grid {
    grid-template-columns: 1fr;
  }

  .demo-card {
    padding: 1.25rem;
  }
}
