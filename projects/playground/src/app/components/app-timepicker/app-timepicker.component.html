<div class="timepicker-demo-container">
  <h1>Time Picker Component</h1>
  <p class="component-description">
    A sophisticated time selection component with scroll-based interface, inline
    editing, keyboard navigation, and full accessibility support for precise
    time input in 12-hour format.
  </p>

  <!-- Demo Navigation -->
  <div class="demo-navigation">
    <div class="demo-grid">
      <div
        *ngFor="let demo of demos"
        class="demo-card"
        [routerLink]="[demo.path]"
        routerLinkActive="active"
      >
        <h3>{{ demo.title }}</h3>
        <p>{{ demo.description }}</p>
        <div class="demo-arrow"><span>→</span></div>
      </div>
    </div>
  </div>

  <ava-time-picker (timeSelected)="onTimeSelected($event)"></ava-time-picker>

  <!-- Demo Content -->
  <div class="demo-content">
    <router-outlet></router-outlet>
  </div>
</div>
