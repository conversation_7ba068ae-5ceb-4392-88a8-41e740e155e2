import { Component, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { TimePickerComponent } from '../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'app-app-timepicker',
  imports: [CommonModule, RouterModule, TimePickerComponent],
  standalone: true,
  templateUrl: './app-timepicker.component.html',
  styleUrls: ['./app-timepicker.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class AppTimepickerComponent {
  // Demo navigation data
  demos = [
    {
      path: 'basic-usage',
      title: 'Basic Usage',
      description: 'Simple time picker with default behavior and display mode',
    },
    {
      path: 'scroll-mode',
      title: 'Scroll Mode Interface',
      description:
        'Interactive scroll-based time selection with smooth animations',
    },
    {
      path: 'inline-input',
      title: 'Inline Input Editing',
      description: 'Direct editing capability with inline input fields',
    },
    {
      path: 'keyboard-navigation',
      title: 'Keyboard Navigation',
      description:
        'Full keyboard accessibility with arrow keys and focus management',
    },
    {
      path: 'validation',
      title: 'Validation and Constraints',
      description: 'Comprehensive validation with proper error handling',
    },
    {
      path: 'custom-styling',
      title: 'Custom Styling',
      description: 'Customizable appearance with CSS custom properties',
    },
  ];

  onTimeSelected(selectedTime: string) {
    console.log('Selected time:', selectedTime);
  }
}
