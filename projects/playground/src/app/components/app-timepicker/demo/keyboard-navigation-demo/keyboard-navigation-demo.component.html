<div class="demo-container">
  <div class="demo-header">
    <h2>Keyboard Navigation</h2>
    <p>
      Full keyboard accessibility with arrow keys, number input, and focus
      management.
    </p>
  </div>

  <div class="demo-section">
    <div class="demo-example">
      <h3>Keyboard Controls</h3>
      <p>
        Use keyboard navigation to interact with the time picker. Tab to focus,
        arrow keys to navigate, and Enter to select.
      </p>

      <div class="time-picker-wrapper">
        <ava-time-picker
          (timeSelected)="onTimeSelected($event)"
        ></ava-time-picker>
      </div>

      <div class="demo-output" *ngIf="selectedTime">
        <h4>Selected Time:</h4>
        <p class="selected-time">{{ selectedTime }}</p>
      </div>

      <div class="keyboard-events" *ngIf="keyboardEvents.length > 0">
        <h4>Recent Keyboard Events:</h4>
        <ul>
          <li *ngFor="let event of keyboardEvents">{{ event }}</li>
        </ul>
      </div>
    </div>

    <div class="demo-code">
      <h3>Keyboard Shortcuts</h3>
      <div class="shortcuts-grid">
        <div class="shortcut-item">
          <kbd>Tab</kbd>
          <span>Navigate between time components</span>
        </div>
        <div class="shortcut-item">
          <kbd>↑</kbd>
          <span>Increment current value</span>
        </div>
        <div class="shortcut-item">
          <kbd>↓</kbd>
          <span>Decrement current value</span>
        </div>
        <div class="shortcut-item">
          <kbd>Enter</kbd>
          <span>Select current value</span>
        </div>
        <div class="shortcut-item">
          <kbd>Escape</kbd>
          <span>Cancel editing or close picker</span>
        </div>
        <div class="shortcut-item">
          <kbd>0-9</kbd>
          <span>Direct number input</span>
        </div>
      </div>

      <h4>Accessibility Features</h4>
      <ul>
        <li>
          <strong>Focus Management:</strong> Proper focus trapping and
          restoration
        </li>
        <li>
          <strong>Screen Reader Support:</strong> ARIA labels and semantic
          structure
        </li>
        <li>
          <strong>Keyboard Only:</strong> Full functionality without mouse
        </li>
        <li>
          <strong>Focus Indicators:</strong> Clear visual focus indicators
        </li>
      </ul>
    </div>
  </div>

  <div class="demo-features">
    <h3>Keyboard Navigation Features</h3>
    <div class="features-grid">
      <div class="feature-card">
        <h4>⌨️ Full Keyboard Support</h4>
        <p>
          Complete keyboard accessibility with arrow keys, number input, and
          navigation.
        </p>
      </div>

      <div class="feature-card">
        <h4>🎯 Focus Management</h4>
        <p>
          Proper focus trapping, restoration, and logical tab order through all
          elements.
        </p>
      </div>

      <div class="feature-card">
        <h4>🔊 Screen Reader Ready</h4>
        <p>
          Comprehensive ARIA attributes and semantic HTML for screen reader
          compatibility.
        </p>
      </div>

      <div class="feature-card">
        <h4>👁️ Visual Indicators</h4>
        <p>Clear focus indicators and selection states for keyboard users.</p>
      </div>

      <div class="feature-card">
        <h4>⚡ Quick Input</h4>
        <p>Direct number input for fast time entry without scrolling.</p>
      </div>

      <div class="feature-card">
        <h4>🚫 Boundary Respect</h4>
        <p>
          Keyboard navigation respects time boundaries and validation rules.
        </p>
      </div>
    </div>
  </div>
</div>
