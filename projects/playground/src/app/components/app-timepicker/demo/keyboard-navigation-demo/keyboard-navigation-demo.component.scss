.demo-container {
  padding: 2rem;
  max-width: 1000px;
  margin: 0 auto;
}

.demo-header {
  margin-bottom: 2rem;
  text-align: center;

  h2 {
    font-size: 2rem;
    font-weight: 600;
    color: var(--color-text-primary, #1a202c);
    margin-bottom: 0.5rem;
  }

  p {
    font-size: 1.125rem;
    color: var(--color-text-secondary, #64748b);
    line-height: 1.6;
  }
}

.demo-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.demo-example {
  background: var(--color-background-primary, #ffffff);
  border: 1px solid var(--color-border-default, #e2e8f0);
  border-radius: var(--global-radius-lg, 0.75rem);
  padding: 2rem;
  box-shadow: var(--global-elevation-01);

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--color-text-primary, #1a202c);
    margin-bottom: 1rem;
  }

  p {
    color: var(--color-text-secondary, #64748b);
    margin-bottom: 1.5rem;
    line-height: 1.6;
  }
}

.time-picker-wrapper {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: var(--color-surface-secondary, #f8fafc);
  border-radius: var(--global-radius-md, 0.5rem);
}

.demo-output {
  margin-top: 1.5rem;
  padding: 1rem;
  background: var(--color-surface-tertiary, #f1f5f9);
  border-radius: var(--global-radius-md, 0.5rem);
  border-left: 4px solid var(--color-brand-primary, #e91e63);

  h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--color-text-primary, #1a202c);
    margin-bottom: 0.5rem;
  }

  .selected-time {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color-brand-primary, #e91e63);
    margin: 0;
    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  }
}

.keyboard-events {
  margin-top: 1rem;
  padding: 1rem;
  background: var(--color-surface-secondary, #f8fafc);
  border-radius: var(--global-radius-md, 0.5rem);
  border: 1px solid var(--color-border-subtle, #e2e8f0);

  h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--color-text-primary, #1a202c);
    margin-bottom: 0.5rem;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      font-size: 0.75rem;
      color: var(--color-text-secondary, #64748b);
      padding: 0.25rem 0;
      font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
    }
  }
}

.demo-code {
  background: var(--color-surface-secondary, #f8fafc);
  border: 1px solid var(--color-border-default, #e2e8f0);
  border-radius: var(--global-radius-lg, 0.75rem);
  padding: 1.5rem;

  h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--color-text-primary, #1a202c);
    margin-bottom: 1rem;
  }

  h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--color-text-primary, #1a202c);
    margin: 1.5rem 0 0.75rem 0;
  }
}

.shortcuts-grid {
  display: grid;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.shortcut-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  background: var(--color-surface-tertiary, #f1f5f9);
  border-radius: var(--global-radius-md, 0.5rem);
  border: 1px solid var(--color-border-subtle, #e2e8f0);

  kbd {
    background: var(--color-surface-primary, #ffffff);
    border: 1px solid var(--color-border-default, #d1d5db);
    border-radius: var(--global-radius-sm, 0.25rem);
    padding: 0.25rem 0.5rem;
    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--color-text-primary, #1a202c);
    min-width: 2rem;
    text-align: center;
  }

  span {
    color: var(--color-text-secondary, #64748b);
    font-size: 0.875rem;
  }
}

.demo-code ul {
  list-style: none;
  padding: 0;
  margin: 0;

  li {
    padding: 0.5rem 0;
    color: var(--color-text-secondary, #64748b);
    line-height: 1.5;
    font-size: 0.875rem;

    strong {
      color: var(--color-text-primary, #1a202c);
      font-weight: 600;
    }
  }
}

.demo-features {
  background: var(--color-background-primary, #ffffff);
  border: 1px solid var(--color-border-default, #e2e8f0);
  border-radius: var(--global-radius-lg, 0.75rem);
  padding: 2rem;
  box-shadow: var(--global-elevation-01);

  h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--color-text-primary, #1a202c);
    margin-bottom: 1.5rem;
    text-align: center;
  }
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.feature-card {
  background: var(--color-surface-secondary, #f8fafc);
  border: 1px solid var(--color-border-subtle, #e2e8f0);
  border-radius: var(--global-radius-md, 0.5rem);
  padding: 1.5rem;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--color-brand-primary, #e91e63);
    transform: translateY(-2px);
    box-shadow: var(--global-elevation-01);
  }

  h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--color-text-primary, #1a202c);
    margin-bottom: 0.75rem;
  }

  p {
    color: var(--color-text-secondary, #64748b);
    line-height: 1.6;
    margin: 0;
    font-size: 0.875rem;
  }
}
