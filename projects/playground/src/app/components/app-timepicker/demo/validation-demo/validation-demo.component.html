<div class="demo-container">
  <div class="demo-header">
    <h2>Validation and Constraints</h2>
    <p>
      Comprehensive validation with proper error handling and boundary
      enforcement.
    </p>
  </div>

  <div class="demo-section">
    <div class="demo-example">
      <h3>Input Validation</h3>
      <p>
        Try entering invalid values to see the validation in action. The time
        picker enforces valid time ranges and formats.
      </p>

      <div class="time-picker-wrapper">
        <ava-time-picker
          (timeSelected)="onTimeSelected($event)"
        ></ava-time-picker>
      </div>

      <div class="demo-output" *ngIf="selectedTime">
        <h4>Valid Time Selected:</h4>
        <p class="selected-time">{{ selectedTime }}</p>
      </div>

      <div class="validation-messages" *ngIf="validationMessages.length > 0">
        <h4>Validation Events:</h4>
        <ul>
          <li *ngFor="let message of validationMessages">{{ message }}</li>
        </ul>
      </div>
    </div>

    <div class="demo-code">
      <h3>Validation Rules</h3>
      <div class="validation-rules">
        <div class="rule-item">
          <h4>Hours (01-12)</h4>
          <p>Must be between 01 and 12, zero-padded</p>
        </div>
        <div class="rule-item">
          <h4>Minutes (00-59)</h4>
          <p>Must be between 00 and 59, zero-padded</p>
        </div>
        <div class="rule-item">
          <h4>Period (AM/PM)</h4>
          <p>Must be either AM or PM</p>
        </div>
      </div>

      <h4>Validation Features</h4>
      <ul>
        <li>
          <strong>Real-time Validation:</strong> Immediate feedback on invalid
          input
        </li>
        <li>
          <strong>Boundary Enforcement:</strong> Prevents invalid time
          selections
        </li>
        <li>
          <strong>Format Consistency:</strong> Maintains consistent time
          formatting
        </li>
        <li>
          <strong>Error Recovery:</strong> Clear ways to correct validation
          errors
        </li>
      </ul>
    </div>
  </div>

  <div class="demo-features">
    <h3>Validation Capabilities</h3>
    <div class="features-grid">
      <div class="feature-card">
        <h4>✅ Input Validation</h4>
        <p>Thorough validation of all user inputs with clear error messages.</p>
      </div>

      <div class="feature-card">
        <h4>🔒 Boundary Enforcement</h4>
        <p>
          Prevents selection of invalid time values outside acceptable ranges.
        </p>
      </div>

      <div class="feature-card">
        <h4>🔄 Error Handling</h4>
        <p>
          Provides clear error messages and recovery options for invalid inputs.
        </p>
      </div>

      <div class="feature-card">
        <h4>🎨 Format Consistency</h4>
        <p>Maintains consistent time formatting across all input methods.</p>
      </div>

      <div class="feature-card">
        <h4>⚡ Edge Case Handling</h4>
        <p>
          Handles edge cases like leap seconds, DST changes, and boundary
          conditions.
        </p>
      </div>

      <div class="feature-card">
        <h4>🛡️ Data Integrity</h4>
        <p>Ensures data integrity and prevents invalid time states.</p>
      </div>
    </div>
  </div>
</div>
