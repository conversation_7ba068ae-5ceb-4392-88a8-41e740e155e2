import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TimePickerComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-inline-input-demo',
  standalone: true,
  imports: [CommonModule, TimePickerComponent],
  templateUrl: './inline-input-demo.component.html',
  styleUrls: ['./inline-input-demo.component.scss'],
})
export class InlineInputDemoComponent {
  selectedTime = '';
  inputEvents: string[] = [];

  onTimeSelected(time: string) {
    this.selectedTime = time;
    console.log('Selected time:', time);
  }

  onInputEvent(event: string) {
    this.inputEvents.unshift(
      `Input event: ${event} - ${new Date().toLocaleTimeString()}`
    );
    if (this.inputEvents.length > 5) {
      this.inputEvents.pop();
    }
  }
}
