<div class="demo-container">
  <div class="demo-header">
    <h2>Custom Styling</h2>
    <p>
      Customizable appearance with CSS custom properties and theme integration.
    </p>
  </div>

  <div class="demo-section">
    <div class="demo-example">
      <h3>Theme Switcher</h3>
      <p>
        Switch between different themes to see how the time picker adapts to
        different styling.
      </p>

      <div class="theme-controls">
        <button
          *ngFor="let theme of ['default', 'dark', 'blue', 'green']"
          (click)="setTheme(theme)"
          [class.active]="currentTheme === theme"
          class="theme-button"
        >
          {{ theme | titlecase }}
        </button>
      </div>

      <div class="time-picker-wrapper">
        <ava-time-picker
          (timeSelected)="onTimeSelected($event)"
        ></ava-time-picker>
      </div>

      <div class="demo-output" *ngIf="selectedTime">
        <h4>Selected Time:</h4>
        <p class="selected-time">{{ selectedTime }}</p>
      </div>
    </div>

    <div class="demo-code">
      <h3>CSS Custom Properties</h3>
      <pre><code>/* Theme Customization */
.time-picker-container {{ '{' }}
  --timepicker-background: #ffffff;
  --timepicker-border-radius: 8px;
  --timepicker-display-font: 16px;
  --timepicker-display-text: #333333;
  --timepicker-scroll-background: #f8f9fa;
  --timepicker-time-item-text: #666666;
  --timepicker-time-item-selected-text: #007bff;
  --timepicker-icon-color: #007bff;
{{ '}' }}</code></pre>

      <h4>Styling Features</h4>
      <ul>
        <li><strong>CSS Variables:</strong> Easy theme customization</li>
        <li>
          <strong>Responsive Design:</strong> Adapts to different screen sizes
        </li>
        <li>
          <strong>Theme Integration:</strong> Works with design system themes
        </li>
        <li>
          <strong>Custom Colors:</strong> Full color customization support
        </li>
      </ul>
    </div>
  </div>

  <div class="demo-features">
    <h3>Styling Capabilities</h3>
    <div class="features-grid">
      <div class="feature-card">
        <h4>🎨 CSS Variables</h4>
        <p>
          Comprehensive CSS custom properties for easy theming and
          customization.
        </p>
      </div>

      <div class="feature-card">
        <h4>📱 Responsive Design</h4>
        <p>Adapts to different screen sizes and device orientations.</p>
      </div>

      <div class="feature-card">
        <h4>🎯 Theme Integration</h4>
        <p>
          Seamlessly integrates with design system themes and color schemes.
        </p>
      </div>

      <div class="feature-card">
        <h4>🌈 Color Customization</h4>
        <p>Full control over colors, backgrounds, and visual styling.</p>
      </div>

      <div class="feature-card">
        <h4>📐 Layout Control</h4>
        <p>Flexible layout options and spacing customization.</p>
      </div>

      <div class="feature-card">
        <h4>🔧 Component Styling</h4>
        <p>Individual styling for each component part and state.</p>
      </div>
    </div>
  </div>
</div>
