.demo-page {
  min-height: 100vh;
}

.demo-header {
  background-color: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border-subtle);
  padding: var(--global-spacing-8, 4rem) 0 var(--global-spacing-6, 3rem);

  h1 {
    font-size: var(--global-font-size-4xl, 2.5rem);
    font-weight: var(--global-font-weight-bold, 700);
    margin: 0 0 var(--global-spacing-2, 0.5rem) 0;
    color: var(--color-text-primary);
  }

  p {
    font-size: var(--global-font-size-lg, 1.125rem);
    color: var(--color-text-secondary);
    margin: 0;
    line-height: var(--global-line-height-relaxed, 1.625);
  }
}

.demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--global-spacing-6, 3rem);
  margin-top: var(--global-spacing-6, 3rem);
}

.demo-card {
  background: var(--color-background-primary);
  border: 1px solid var(--color-border-subtle);
  border-radius: var(--global-radius-lg, 12px);
  padding: var(--global-spacing-6, 3rem);
  transition: all var(--global-motion-duration-normal, 0.2s)
    var(--global-motion-easing-standard, ease);

  &:hover {
    border-color: var(--color-primary);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  h3 {
    margin: 0 0 var(--global-spacing-3, 0.75rem);
    color: var(--color-text-primary);
    font-size: var(--global-font-size-xl, 1.25rem);
    font-weight: var(--global-font-weight-semibold, 600);
  }

  p {
    margin: 0 0 var(--global-spacing-5, 2rem);
    color: var(--color-text-secondary);
    font-size: var(--global-font-size-sm, 0.875rem);
    line-height: var(--global-line-height-relaxed, 1.625);
  }

  .demo-link {
    display: inline-flex;
    align-items: center;
    color: var(--color-primary);
    text-decoration: none;
    font-weight: var(--global-font-weight-medium, 500);
    font-size: var(--global-font-size-sm, 0.875rem);
    transition: color var(--global-motion-duration-fast, 0.1s)
      var(--global-motion-easing-standard, ease);

    &:hover {
      color: var(--color-primary-emphasis);
    }
  }
}

.demo-content {
  background-color: var(--color-background-primary);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--global-spacing-4, 1.5rem);
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -var(--global-spacing-2, 0.5rem);
}

.col-12 {
  flex: 0 0 100%;
  padding: 0 var(--global-spacing-2, 0.5rem);
}
