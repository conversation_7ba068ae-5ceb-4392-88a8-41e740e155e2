.demo-content {
  padding: var(--global-spacing-6, 3rem) 0;
}

.demo-section {
  margin-bottom: var(--global-spacing-8, 4rem);

  h2 {
    font-size: var(--global-font-size-2xl, 1.5rem);
    font-weight: var(--global-font-weight-semibold, 600);
    margin-bottom: var(--global-spacing-2, 0.5rem);
    color: var(--color-text-primary);
  }

  p {
    color: var(--color-text-secondary);
    margin-bottom: var(--global-spacing-4, 1.5rem);
    line-height: var(--global-line-height-normal, 1.5);
  }
}

.table-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--global-spacing-4, 1.5rem);
  padding: var(--global-spacing-4, 1.5rem);
  background-color: var(--color-background-secondary);
  border: 1px solid var(--color-border-subtle);
  border-radius: var(--global-radius-md, 8px);
  flex-wrap: wrap;
  gap: var(--global-spacing-3, 0.75rem);

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
}

.filters {
  display: flex;
  gap: var(--global-spacing-3, 0.75rem);
  align-items: center;
  flex-wrap: wrap;

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
  }
}

.bulk-actions {
  display: flex;
  gap: var(--global-spacing-2, 0.5rem);
  align-items: center;
}

.selection-count {
  font-size: var(--global-font-size-sm, 0.875rem);
  color: var(--color-text-secondary);
  margin-right: var(--global-spacing-2, 0.5rem);
}

.demo-card {
  border: 1px solid var(--color-border-subtle);
  border-radius: var(--global-radius-md, 8px);
  overflow: hidden;
  margin-bottom: var(--global-spacing-6, 3rem);
}

.card-header {
  padding: var(--global-spacing-4, 1.5rem);
  border-bottom: 1px solid var(--color-border-subtle);
  background-color: var(--color-background-secondary);

  h3 {
    font-size: var(--global-font-size-lg, 1.125rem);
    font-weight: var(--global-font-weight-semibold, 600);
    margin: 0 0 var(--global-spacing-1, 0.25rem) 0;
    color: var(--color-text-primary);
  }

  p {
    font-size: var(--global-font-size-sm, 0.875rem);
    color: var(--color-text-secondary);
    margin: 0;
  }
}

.card-content {
  padding: var(--global-spacing-4, 1.5rem);
}

// Custom cell styles
.employee-cell {
  display: flex;
  align-items: center;
  gap: var(--global-spacing-3, 0.75rem);
}

.employee-info {
  display: flex;
  flex-direction: column;
  gap: var(--global-spacing-1, 0.25rem);
}

.employee-name {
  font-weight: var(--global-font-weight-medium, 500);
  color: var(--color-text-primary);
}

.employee-email {
  font-size: var(--global-font-size-sm, 0.875rem);
  color: var(--color-text-secondary);
}

.performance-cell {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--global-spacing-1, 0.25rem);
}

.performance-score {
  font-weight: var(--global-font-weight-semibold, 600);
  font-size: var(--global-font-size-lg, 1.125rem);
}

.performance-stars {
  display: flex;
  gap: 1px;
}

.action-buttons {
  display: flex;
  gap: var(--global-spacing-1, 0.25rem);
  align-items: center;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--global-spacing-4, 1.5rem);
  margin-top: var(--global-spacing-4, 1.5rem);
}

.feature-card {
  display: flex;
  align-items: flex-start;
  gap: var(--global-spacing-3, 0.75rem);
  padding: var(--global-spacing-4, 1.5rem);
  border: 1px solid var(--color-border-subtle);
  border-radius: var(--global-radius-md, 8px);
  background-color: var(--color-background-secondary);
}

.feature-content {
  flex: 1;

  h4 {
    font-size: var(--global-font-size-base, 1rem);
    font-weight: var(--global-font-weight-semibold, 600);
    margin: 0 0 var(--global-spacing-2, 0.5rem) 0;
    color: var(--color-text-primary);
  }

  p {
    font-size: var(--global-font-size-sm, 0.875rem);
    color: var(--color-text-secondary);
    margin: 0;
    line-height: var(--global-line-height-normal, 1.5);
  }
}

.code-block {
  background-color: var(--color-background-secondary);
  border: 1px solid var(--color-border-subtle);
  border-radius: var(--global-radius-md, 8px);
  padding: var(--global-spacing-4, 1.5rem);
  overflow-x: auto;

  pre {
    margin: 0;
    font-family: var(--global-font-family-mono, "Courier New", monospace);
    font-size: var(--global-font-size-sm, 0.875rem);
    line-height: var(--global-line-height-normal, 1.5);
    color: var(--color-text-primary);
  }

  code {
    white-space: pre;
  }
}
