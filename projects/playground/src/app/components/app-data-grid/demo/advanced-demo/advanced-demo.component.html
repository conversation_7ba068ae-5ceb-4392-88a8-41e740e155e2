<div class="demo-content">
  <div class="demo-section">
    <h2>Advanced Features</h2>
    <p>
      Complex table functionality including row selection, inline editing, bulk
      actions, and advanced filtering.
    </p>

    <!-- Table Controls -->
    <div class="table-controls">
      <div class="filters">
        <ava-textbox [(ngModel)]="searchTerm" placeholder="Search employees..." (ngModelChange)="applyFilters()">
        </ava-textbox>

        <ava-select [(ngModel)]="selectedDepartment" (ngModelChange)="applyFilters()">
          <ava-select-option *ngFor="let dept of departments" [value]="dept">
            {{ dept === "All" ? "All Departments" : dept }}
          </ava-select-option>
        </ava-select>

        <ava-select [(ngModel)]="selectedStatus" (ngModelChange)="applyFilters()">
          <ava-select-option *ngFor="let status of statusOptions" [value]="status">
            {{ status === "All" ? "All Status" : status }}
          </ava-select-option>
        </ava-select>

        <ava-button variant="secondary" (userClick)="clearFilters()">Clear Filters</ava-button>
      </div>

      <div class="bulk-actions" *ngIf="selectedCount > 0">
        <span class="selection-count">{{ selectedCount }} selected</span>
        <ava-button variant="primary" size="small" (userClick)="onExport()">
          <ava-icon iconName="download" iconSize="16"></ava-icon>
          Export
        </ava-button>
        <ava-button variant="danger" size="small" (userClick)="onBulkDelete()">
          <ava-icon iconName="trash" iconSize="16"></ava-icon>
          Delete
        </ava-button>
      </div>
    </div>

    <div class="demo-card">
      <div class="card-header">
        <h3>Employee Management System</h3>
        <p>
          Full-featured data table with selection, editing, and bulk operations
        </p>
      </div>
      <div class="card-content">
        <ava-data-grid [dataSource]="employeeData" [displayedColumns]="displayedColumns"
          (dataSorted)="onDataSorted($event)">
          <!-- Selection column -->
          <ng-container avaColumnDef="select">
            <ng-container *avaHeaderCellDef>
              <ava-checkbox (isCheckedChange)="toggleAll($event)"></ava-checkbox>
            </ng-container>
            <ng-container *avaCellDef="let row">
              <ava-checkbox [isChecked]="row.select" (isCheckedChange)="row.select = $event; onSelectionChange()">
              </ava-checkbox>
            </ng-container>
          </ng-container>

          <!-- Employee info column -->
          <ng-container avaColumnDef="employee" [sortable]="true" [filter]="true">
            <ng-container *avaHeaderCellDef>Employee</ng-container>
            <ng-container *avaCellDef="let row">
              <div class="employee-cell">
                <ava-avatars size="small"></ava-avatars>
                <div class="employee-info">
                  <div class="employee-name" *ngIf="!row.editable">
                    {{ row.name }}
                  </div>
                  <ava-textbox *ngIf="row.editable" [(ngModel)]="row.name" size="sm">
                  </ava-textbox>
                  <div class="employee-email">{{ row.email }}</div>
                </div>
              </div>
            </ng-container>
          </ng-container>

          <!-- Department column -->
          <ng-container avaColumnDef="department" [sortable]="true" [filter]="true">
            <ng-container *avaHeaderCellDef>Department</ng-container>
            <ng-container *avaCellDef="let row">
              <span *ngIf="!row.editable">{{ row.department }}</span>
              <ava-textbox *ngIf="row.editable" [(ngModel)]="row.department" size="sm">
              </ava-textbox>
            </ng-container>
          </ng-container>

          <!-- Position column -->
          <ng-container avaColumnDef="position" [sortable]="true" [filter]="true">
            <ng-container *avaHeaderCellDef>Position</ng-container>
            <ng-container *avaCellDef="let row">
              <span *ngIf="!row.editable">{{ row.position }}</span>
              <ava-textbox *ngIf="row.editable" [(ngModel)]="row.position" size="sm">
              </ava-textbox>
            </ng-container>
          </ng-container>

          <!-- Salary column with inline editing -->
          <ng-container avaColumnDef="salary" [sortable]="true">
            <ng-container *avaHeaderCellDef>Salary</ng-container>
            <ng-container *avaCellDef="let row">
              <span *ngIf="!row.editable">{{ formatSalary(row.salary) }}</span>
              <ava-textbox *ngIf="row.editable" [(ngModel)]="row.salary" type="number" size="sm">
              </ava-textbox>
            </ng-container>
          </ng-container>

          <!-- Performance column -->
          <ng-container avaColumnDef="performance" [sortable]="true">
            <ng-container *avaHeaderCellDef>Performance</ng-container>
            <ng-container *avaCellDef="let row">
              <div class="performance-cell">
                <div class="performance-score" [style.color]="getPerformanceColor(row.performance)">
                  {{ row.performance }}
                </div>
                <div class="performance-stars">
                  <ava-icon *ngFor="let star of [1, 2, 3, 4, 5]" iconName="star" iconSize="12" [style.color]="
                      star <= row.performance
                        ? 'var(--color-warning-emphasis)'
                        : 'var(--color-neutral-subtle)'
                    ">
                  </ava-icon>
                </div>
              </div>
            </ng-container>
          </ng-container>

          <!-- Status column -->
          <ng-container avaColumnDef="status" [sortable]="true" [filter]="true">
            <ng-container *avaHeaderCellDef>Status</ng-container>
            <ng-container *avaCellDef="let row">
              <ava-badges [state]="getStatusState(row.status)">
                {{ row.status }}
              </ava-badges>
            </ng-container>
          </ng-container>

          <!-- Actions column -->
          <ng-container avaColumnDef="actions">
            <ng-container *avaHeaderCellDef>Actions</ng-container>
            <ng-container *avaCellDef="let row">
              <div class="action-buttons">
                <!-- Edit mode actions -->
                <ng-container *ngIf="row.editable">
                  <ava-button variant="primary" size="small" (userClick)="onSave(row)">
                    <ava-icon iconName="check" iconSize="16"></ava-icon>
                  </ava-button>
                  <ava-button variant="secondary" size="small" (userClick)="onCancel(row)">
                    <ava-icon iconName="x" iconSize="16"></ava-icon>
                  </ava-button>
                </ng-container>

                <!-- View mode actions -->
                <ng-container *ngIf="!row.editable">
                  <ava-button variant="secondary" size="small" (userClick)="onEdit(row)">
                    <ava-icon iconName="SquarePen" iconSize="16"></ava-icon>
                  </ava-button>
                  <ava-button variant="danger" size="small" (userClick)="onDelete(row)">
                    <ava-icon iconName="trash" iconSize="16"></ava-icon>
                  </ava-button>
                </ng-container>
              </div>
            </ng-container>
          </ng-container>
        </ava-data-grid>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h2>Advanced Capabilities</h2>
    <div class="feature-grid">
      <div class="feature-card">
        <ava-icon iconName="check-square" iconSize="24"></ava-icon>
        <div class="feature-content">
          <h4>Row Selection</h4>
          <p>Select individual rows or all rows with master checkbox</p>
        </div>
      </div>
      <div class="feature-card">
        <ava-icon iconName="edit" iconSize="24"></ava-icon>
        <div class="feature-content">
          <h4>Inline Editing</h4>
          <p>Edit data directly in the table with save/cancel actions</p>
        </div>
      </div>
      <div class="feature-card">
        <ava-icon iconName="layers" iconSize="24"></ava-icon>
        <div class="feature-content">
          <h4>Bulk Operations</h4>
          <p>Perform actions on multiple selected rows simultaneously</p>
        </div>
      </div>
      <div class="feature-card">
        <ava-icon iconName="filter" iconSize="24"></ava-icon>
        <div class="feature-content">
          <h4>Advanced Filtering</h4>
          <p>Multiple filter controls with search and dropdown filters</p>
        </div>
      </div>
      <div class="feature-card">
        <ava-icon iconName="download" iconSize="24"></ava-icon>
        <div class="feature-content">
          <h4>Data Export</h4>
          <p>Export selected data or entire dataset</p>
        </div>
      </div>
      <div class="feature-card">
        <ava-icon iconName="star" iconSize="24"></ava-icon>
        <div class="feature-content">
          <h4>Rich Data Display</h4>
          <p>Performance ratings, status indicators, and formatted data</p>
        </div>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h2>Code Example</h2>
    <div class="code-block">
      <pre><code>{{codeExample}}</code></pre>
    </div>
  </div>
</div>