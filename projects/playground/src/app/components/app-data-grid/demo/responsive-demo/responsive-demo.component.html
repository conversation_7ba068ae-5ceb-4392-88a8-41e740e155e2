<div class="demo-content">
  <div class="demo-section">
    <h2>Responsive Data Grid</h2>
    <p>
      The data grid automatically adapts to different screen sizes with
      horizontal scrolling for wide tables.
    </p>

    <div class="demo-card">
      <div class="card-header">
        <h3>Wide Product Catalog</h3>
        <p>Scrolls horizontally on smaller screens</p>
      </div>
      <div class="card-content">
        <ava-data-grid [dataSource]="wideData" [displayedColumns]="wideColumns">
          <ng-container
            avaColumnDef="productCode"
            [sortable]="true"
            [filter]="true"
          >
            <ng-container *avaHeaderCellDef>Product Code</ng-container>
            <ng-container *avaCellDef="let row">{{
              row.productCode
            }}</ng-container>
          </ng-container>

          <ng-container avaColumnDef="name" [sortable]="true" [filter]="true">
            <ng-container *avaHeaderCellDef>Product Name</ng-container>
            <ng-container *avaCellDef="let row">{{ row.name }}</ng-container>
          </ng-container>

          <ng-container
            avaColumnDef="category"
            [sortable]="true"
            [filter]="true"
          >
            <ng-container *avaHeaderCellDef>Category</ng-container>
            <ng-container *avaCellDef="let row">{{
              row.category
            }}</ng-container>
          </ng-container>

          <ng-container avaColumnDef="brand" [sortable]="true" [filter]="true">
            <ng-container *avaHeaderCellDef>Brand</ng-container>
            <ng-container *avaCellDef="let row">{{ row.brand }}</ng-container>
          </ng-container>

          <ng-container avaColumnDef="model" [sortable]="true" [filter]="true">
            <ng-container *avaHeaderCellDef>Model</ng-container>
            <ng-container *avaCellDef="let row">{{ row.model }}</ng-container>
          </ng-container>

          <ng-container avaColumnDef="price" [sortable]="true">
            <ng-container *avaHeaderCellDef>Price</ng-container>
            <ng-container *avaCellDef="let row">${{ row.price }}</ng-container>
          </ng-container>

          <ng-container avaColumnDef="stock" [sortable]="true">
            <ng-container *avaHeaderCellDef>Stock</ng-container>
            <ng-container *avaCellDef="let row"
              >{{ row.stock }} units</ng-container
            >
          </ng-container>

          <ng-container
            avaColumnDef="location"
            [sortable]="true"
            [filter]="true"
          >
            <ng-container *avaHeaderCellDef>Location</ng-container>
            <ng-container *avaCellDef="let row">{{
              row.location
            }}</ng-container>
          </ng-container>

          <ng-container
            avaColumnDef="supplier"
            [sortable]="true"
            [filter]="true"
          >
            <ng-container *avaHeaderCellDef>Supplier</ng-container>
            <ng-container *avaCellDef="let row">{{
              row.supplier
            }}</ng-container>
          </ng-container>

          <ng-container avaColumnDef="lastUpdated" [sortable]="true">
            <ng-container *avaHeaderCellDef>Last Updated</ng-container>
            <ng-container *avaCellDef="let row">{{
              row.lastUpdated | date
            }}</ng-container>
          </ng-container>

          <ng-container avaColumnDef="status" [sortable]="true" [filter]="true">
            <ng-container *avaHeaderCellDef>Status</ng-container>
            <ng-container *avaCellDef="let row">
              <ava-badges [state]="getStatusState(row.status)">
                {{ row.status }}
              </ava-badges>
            </ng-container>
          </ng-container>
        </ava-data-grid>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h2>Mobile-Optimized Table</h2>
    <p>
      Fewer columns for better mobile experience without horizontal scrolling.
    </p>

    <div class="demo-card">
      <div class="card-header">
        <h3>User List (Mobile Friendly)</h3>
        <p>Essential information only for mobile screens</p>
      </div>
      <div class="card-content">
        <ava-data-grid
          [dataSource]="mobileData"
          [displayedColumns]="mobileColumns"
        >
          <ng-container avaColumnDef="name" [sortable]="true" [filter]="true">
            <ng-container *avaHeaderCellDef>Name</ng-container>
            <ng-container *avaCellDef="let row">{{ row.name }}</ng-container>
          </ng-container>

          <ng-container avaColumnDef="role" [sortable]="true" [filter]="true">
            <ng-container *avaHeaderCellDef>Role</ng-container>
            <ng-container *avaCellDef="let row">{{ row.role }}</ng-container>
          </ng-container>

          <ng-container avaColumnDef="status" [sortable]="true" [filter]="true">
            <ng-container *avaHeaderCellDef>Status</ng-container>
            <ng-container *avaCellDef="let row">
              <ava-badges [state]="getStatusState(row.status)">
                {{ row.status }}
              </ava-badges>
            </ng-container>
          </ng-container>
        </ava-data-grid>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h2>Responsive Features</h2>
    <div class="feature-grid">
      <div class="feature-card">
        <h4>Horizontal Scrolling</h4>
        <p>Wide tables scroll horizontally on small screens automatically</p>
      </div>
      <div class="feature-card">
        <h4>Touch Friendly</h4>
        <p>Optimized for touch interactions on mobile devices</p>
      </div>
      <div class="feature-card">
        <h4>Adaptive Layout</h4>
        <p>Table layout adapts to available screen space</p>
      </div>
      <div class="feature-card">
        <h4>Column Priority</h4>
        <p>Consider showing fewer columns on mobile for better UX</p>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h2>Best Practices</h2>
    <div class="tips-grid">
      <div class="tip-card">
        <ava-icon iconName="smartphone" iconSize="24"></ava-icon>
        <div class="tip-content">
          <h4>Mobile First</h4>
          <p>
            Design with 3-4 essential columns for mobile, add more for larger
            screens
          </p>
        </div>
      </div>
      <div class="tip-card">
        <ava-icon iconName="monitor" iconSize="24"></ava-icon>
        <div class="tip-content">
          <h4>Progressive Enhancement</h4>
          <p>Start with core data, add detailed columns for desktop views</p>
        </div>
      </div>
      <div class="tip-card">
        <ava-icon iconName="finger" iconSize="24"></ava-icon>
        <div class="tip-content">
          <h4>Touch Targets</h4>
          <p>
            Ensure buttons and interactive elements are large enough for touch
          </p>
        </div>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h2>Code Example</h2>
    <div class="code-block">
      <pre><code>{{codeExample}}</code></pre>
    </div>
  </div>
</div>
