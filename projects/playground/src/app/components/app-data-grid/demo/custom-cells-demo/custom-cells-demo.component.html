<div class="demo-content">
  <div class="demo-section">
    <h2>Custom Cell Templates</h2>
    <p>
      Create rich, interactive table cells using atomic components like avatars,
      badges, buttons, and icons.
    </p>

    <div class="demo-card">
      <div class="card-header">
        <h3>User Management</h3>
        <p>Complex cells with avatars, badges, toggles, and action buttons</p>
      </div>
      <div class="card-content">
        <ava-data-grid [dataSource]="userData" [displayedColumns]="userColumns">
          <!-- Avatar + Name cell -->
          <ng-container avaColumnDef="user" [sortable]="true" [filter]="true">
            <ng-container *avaHeaderCellDef>User</ng-container>
            <ng-container *avaCellDef="let row">
              <div class="user-cell">
                <ava-avatars size="small"></ava-avatars>
                <div class="user-info">
                  <div class="user-name">{{ row.name }}</div>
                  <ava-link [href]="'mailto:' + row.email" variant="subtle">
                    {{ row.email }}
                  </ava-link>
                </div>
              </div>
            </ng-container>
          </ng-container>

          <!-- Badge cell -->
          <ng-container avaColumnDef="role" [sortable]="true" [filter]="true">
            <ng-container *avaHeaderCellDef>Role</ng-container>
            <ng-container *avaCellDef="let row">
              <ava-badges [state]="getRoleBadgeState(row.role)">
                {{ row.role }}
              </ava-badges>
            </ng-container>
          </ng-container>

          <!-- Status badge -->
          <ng-container avaColumnDef="status" [sortable]="true" [filter]="true">
            <ng-container *avaHeaderCellDef>Status</ng-container>
            <ng-container *avaCellDef="let row">
              <ava-badges [state]="getStatusBadgeState(row.status)">
                {{ row.status }}
              </ava-badges>
            </ng-container>
          </ng-container>

          <!-- Formatted date -->
          <ng-container avaColumnDef="lastLogin" [sortable]="true">
            <ng-container *avaHeaderCellDef>Last Login</ng-container>
            <ng-container *avaCellDef="let row">
              <div class="date-cell">
                <ava-icon iconName="calendar" iconSize="14"></ava-icon>
                <span>{{ row.lastLogin | date : "short" }}</span>
              </div>
            </ng-container>
          </ng-container>

          <!-- Progress indicator -->
          <ng-container avaColumnDef="permissions" [sortable]="true">
            <ng-container *avaHeaderCellDef>Permissions</ng-container>
            <ng-container *avaCellDef="let row">
              <div class="permission-cell">
                <div class="permission-bar">
                  <div
                    class="permission-fill"
                    [style.width.%]="(row.permissions / 5) * 100"
                  ></div>
                </div>
                <span class="permission-text">{{ row.permissions }}/5</span>
              </div>
            </ng-container>
          </ng-container>

          <!-- Toggle switch -->
          <ng-container avaColumnDef="isActive">
            <ng-container *avaHeaderCellDef>Active</ng-container>
            <ng-container *avaCellDef="let row">
              <ava-toggle
                [checked]="row.isActive"
                (toggledChange)="onToggleActive(row)"
              >
              </ava-toggle>
            </ng-container>
          </ng-container>

          <!-- Action buttons -->
          <ng-container avaColumnDef="actions">
            <ng-container *avaHeaderCellDef>Actions</ng-container>
            <ng-container *avaCellDef="let row">
              <div class="action-buttons">
                <ava-button
                  variant="secondary"
                  size="small"
                  (userClick)="onView(row)"
                >
                  <ava-icon iconName="eye" iconSize="16"></ava-icon>
                </ava-button>
                <ava-button
                  variant="secondary"
                  size="small"
                  (userClick)="onEdit(row)"
                >
                  <ava-icon iconName="SquarePen" iconSize="16"></ava-icon>
                </ava-button>
                <ava-button
                  variant="danger"
                  size="small"
                  (userClick)="onDelete(row)"
                >
                  <ava-icon iconName="trash" iconSize="16"></ava-icon>
                </ava-button>
              </div>
            </ng-container>
          </ng-container>
        </ava-data-grid>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h2>Project Dashboard</h2>
    <p>
      Advanced custom cells with team avatars, progress bars, and formatted
      data.
    </p>

    <div class="demo-card">
      <div class="card-header">
        <h3>Project Overview</h3>
        <p>Rich data visualization with multiple atomic components</p>
      </div>
      <div class="card-content">
        <ava-data-grid
          [dataSource]="projectData"
          [displayedColumns]="projectColumns"
        >
          <!-- Project name with icon -->
          <ng-container avaColumnDef="name" [sortable]="true" [filter]="true">
            <ng-container *avaHeaderCellDef>Project Name</ng-container>
            <ng-container *avaCellDef="let row">
              <div class="project-cell">
                <ava-icon iconName="folder" iconSize="16"></ava-icon>
                <span class="project-name">{{ row.name }}</span>
              </div>
            </ng-container>
          </ng-container>

          <!-- Progress bar -->
          <ng-container avaColumnDef="progress" [sortable]="true">
            <ng-container *avaHeaderCellDef>Progress</ng-container>
            <ng-container *avaCellDef="let row">
              <div class="progress-cell">
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    [style.width.%]="row.progress"
                  ></div>
                </div>
                <span class="progress-text">{{ row.progress }}%</span>
              </div>
            </ng-container>
          </ng-container>

          <!-- Priority badge -->
          <ng-container
            avaColumnDef="priority"
            [sortable]="true"
            [filter]="true"
          >
            <ng-container *avaHeaderCellDef>Priority</ng-container>
            <ng-container *avaCellDef="let row">
              <ava-badges [state]="getPriorityBadgeState(row.priority)">
                {{ row.priority }}
              </ava-badges>
            </ng-container>
          </ng-container>

          <!-- Team avatars -->
          <ng-container avaColumnDef="team">
            <ng-container *avaHeaderCellDef>Team</ng-container>
            <ng-container *avaCellDef="let row">
              <div class="team-cell">
                <ava-avatars
                  *ngFor="let member of row.team; let i = index"
                  size="small"
                  [style.margin-left.px]="i > 0 ? -8 : 0"
                  [style.z-index]="row.team.length - i"
                >
                </ava-avatars>
                <span class="team-count">+{{ row.team.length }}</span>
              </div>
            </ng-container>
          </ng-container>

          <!-- Deadline with warning -->
          <ng-container avaColumnDef="deadline" [sortable]="true">
            <ng-container *avaHeaderCellDef>Deadline</ng-container>
            <ng-container *avaCellDef="let row">
              <div class="deadline-cell">
                <ava-icon
                  iconName="calendar"
                  iconSize="14"
                  [class.overdue]="isOverdue(row.deadline)"
                >
                </ava-icon>
                <span [class.overdue]="isOverdue(row.deadline)">
                  {{ row.deadline | date : "MMM d, y" }}
                </span>
              </div>
            </ng-container>
          </ng-container>

          <!-- Formatted budget -->
          <ng-container avaColumnDef="budget" [sortable]="true">
            <ng-container *avaHeaderCellDef>Budget</ng-container>
            <ng-container *avaCellDef="let row">
              <div class="budget-cell">
                <ava-icon iconName="dollar-sign" iconSize="14"></ava-icon>
                <span>{{ formatCurrency(row.budget) }}</span>
              </div>
            </ng-container>
          </ng-container>

          <!-- Project actions -->
          <ng-container avaColumnDef="actions">
            <ng-container *avaHeaderCellDef>Actions</ng-container>
            <ng-container *avaCellDef="let row">
              <div class="action-buttons">
                <ava-button
                  variant="primary"
                  size="small"
                  label="View"
                  (userClick)="onView(row)"
                >
                </ava-button>
                <ava-button
                  variant="secondary"
                  size="small"
                  (userClick)="onEdit(row)"
                >
                  <ava-icon iconName="SquarePen" iconSize="16"></ava-icon>
                </ava-button>
              </div>
            </ng-container>
          </ng-container>
        </ava-data-grid>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h2>Custom Cell Components</h2>
    <div class="feature-grid">
      <div class="feature-card">
        <h4>Avatars</h4>
        <p>User profile images with initials fallback</p>
      </div>
      <div class="feature-card">
        <h4>Badges</h4>
        <p>Status indicators with semantic colors</p>
      </div>
      <div class="feature-card">
        <h4>Buttons</h4>
        <p>Action buttons with icons and variants</p>
      </div>
      <div class="feature-card">
        <h4>Icons</h4>
        <p>Visual indicators and decorative elements</p>
      </div>
      <div class="feature-card">
        <h4>Links</h4>
        <p>Clickable text with different styles</p>
      </div>
      <div class="feature-card">
        <h4>Toggles</h4>
        <p>Interactive switches for boolean values</p>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h2>Code Example</h2>
    <div class="code-block">
      <pre><code>{{codeExample}}</code></pre>
    </div>
  </div>
</div>
