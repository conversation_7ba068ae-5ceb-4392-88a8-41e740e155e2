import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  RadioButtonComponent,
  RadioOption,
} from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-animations-demo',
  standalone: true,
  imports: [CommonModule, RadioButtonComponent],
  template: `
    <div class="demo-section center-demo">
      <h3>Animations</h3>
      <p class="description">
        Interactive animation effects to enhance user feedback and engagement.
      </p>

      <div class="animations-grid">
        <div class="animation-group">
          <h4>No Animation (Default)</h4>
          <p class="animation-description">
            Standard radio button without animation effects for simple
            interactions.
          </p>
          <ava-radio-button
            [options]="animationOptions"
            name="no-animation"
            animation="none"
            [(selectedValue)]="noAnimationValue"
            (selectedValueChange)="onSelectionChange('no-animation', $event)"
          ></ava-radio-button>
        </div>

        <div class="animation-group">
          <h4>Shadow Animation</h4>
          <p class="animation-description">
            Animated shadow effect on selection with smooth cubic-bezier
            transition.
          </p>
          <ava-radio-button
            [options]="animationOptions"
            name="shadow-animation"
            animation="shadow"
            [(selectedValue)]="shadowAnimationValue"
            (selectedValueChange)="
              onSelectionChange('shadow-animation', $event)
            "
          ></ava-radio-button>
        </div>

        <div class="animation-group">
          <h4>Custom Color with Shadow</h4>
          <p class="animation-description">
            Shadow animation combined with custom colors for enhanced visual
            feedback.
          </p>
          <ava-radio-button
            [options]="animationOptions"
            name="custom-shadow"
            animation="shadow"
            color="#3498db"
            [(selectedValue)]="customShadowValue"
            (selectedValueChange)="onSelectionChange('custom-shadow', $event)"
          ></ava-radio-button>
        </div>
      </div>

      <div class="animation-info">
        <h4>Animation Details</h4>
        <div class="info-grid">
          <div class="info-item">
            <h5>Shadow Animation</h5>
            <ul>
              <li>Appears when radio button is selected</li>
              <li>Smooth cubic-bezier transition (0.34, 1.56, 0.64, 1)</li>
              <li>Adds depth and visual emphasis</li>
              <li>Subtle bounce effect for delightful interaction</li>
            </ul>
          </div>
          <div class="info-item">
            <h5>Additional Effects</h5>
            <ul>
              <li>
                <strong>Hover glow</strong> - Soft glow effect on mouse hover
              </li>
              <li>
                <strong>Click scale</strong> - Brief scale animation on
                selection
              </li>
              <li>
                <strong>Smooth transitions</strong> - All state changes animated
                smoothly
              </li>
            </ul>
          </div>
          <div class="info-item">
            <h5>Animation Features</h5>
            <ul>
              <li>
                <strong>Performance optimized</strong> - Uses CSS transforms for
                smooth animations
              </li>
              <li>
                <strong>Accessible</strong> - Respects user motion preferences
              </li>
              <li>
                <strong>Customizable</strong> - Works with custom colors and
                sizes
              </li>
              <li>
                <strong>Cross-browser</strong> - Consistent behavior across
                browsers
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .center-demo {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 60vh;
      }
      .demo-section {
        margin-bottom: 2rem;
        padding: 2rem;
        background: #fff;
        border-radius: 8px;
        max-width: 900px;
        margin-left: auto;
        margin-right: auto;
      }
      .description {
        color: #666;
        margin-bottom: 2rem;
        text-align: center;
      }
      .animations-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
      }
      .animation-group {
        background: #fff;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .animation-group h4 {
        margin: 0 0 0.5rem 0;
        color: #1f2937;
        font-size: 1.1rem;
      }
      .animation-description {
        color: #6b7280;
        font-size: 0.9rem;
        margin-bottom: 1rem;
        line-height: 1.4;
      }
      .animation-info {
        background: #fff;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .animation-info h4 {
        margin: 0 0 1rem 0;
        color: #1f2937;
        font-size: 1.1rem;
      }
      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
      }
      .info-item {
        padding: 1rem;
        background: #f9fafb;
        border-radius: 6px;
        border: 1px solid #e5e7eb;
      }
      .info-item h5 {
        margin: 0 0 0.75rem 0;
        color: #1f2937;
        font-size: 1rem;
      }
      .info-item ul {
        margin: 0;
        padding-left: 1.25rem;
      }
      .info-item li {
        margin-bottom: 0.25rem;
        line-height: 1.4;
        font-size: 0.9rem;
      }
    `,
  ],
})
export class AnimationsDemoComponent {
  noAnimationValue = '';
  shadowAnimationValue = '';
  customShadowValue = '';

  animationOptions: RadioOption[] = [
    { label: 'Option 1', value: 'option1' },
    { label: 'Option 2', value: 'option2' },
    { label: 'Option 3', value: 'option3' },
    { label: 'Option 4', value: 'option4' },
  ];

  onSelectionChange(variant: string, value: string) {
    console.log(`${variant} animation variant selection changed:`, value);
  }
}
