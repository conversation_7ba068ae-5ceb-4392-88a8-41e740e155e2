import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  RadioButtonComponent,
  RadioOption,
} from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-states-demo',
  standalone: true,
  imports: [CommonModule, RadioButtonComponent],
  template: `
    <div class="demo-section center-demo">
      <div class="states-grid">
        <div class="state-group">
          <h4 style="color: var(--text-primary)" class="state-title">
            Default States
          </h4>
          <ava-radio-button
            [options]="defaultOptions"
            name="default-states"
            [(selectedValue)]="defaultValue"
            (selectedValueChange)="onSelectionChange('default', $event)"
          ></ava-radio-button>
        </div>

        <div class="state-group">
          <h4 style="color: var(--text-primary)" class="state-title">
            Disabled Options
          </h4>
          <ava-radio-button
            [options]="disabledOptions"
            name="disabled-states"
            [(selectedValue)]="disabledValue"
            (selectedValueChange)="onSelectionChange('disabled', $event)"
          ></ava-radio-button>
        </div>

        <div class="state-group">
          <h4 style="color: var(--text-primary)" class="state-title">
            All Disabled
          </h4>
          <ava-radio-button
            [options]="allDisabledOptions"
            name="all-disabled"
            [(selectedValue)]="allDisabledValue"
            (selectedValueChange)="onSelectionChange('all-disabled', $event)"
          ></ava-radio-button>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .center-demo {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 60vh;
      }
      .demo-section {
        margin-bottom: 2rem;
        padding: 2rem;
        border-radius: 8px;
        max-width: 870px;
        margin-left: auto;
        margin-right: auto;
      }
      .description {
        color: #666;
        margin-bottom: 2rem;
        text-align: center;
      }
      .states-grid {
        display: flex;
        gap: 2rem;
        justify-content: center;
        flex-wrap: wrap;
        margin-bottom: 2rem;
      }
      .state-group {
        padding: 1.5rem;
        margin-top: 0;
      }
      .state-group h4 {
        margin: 0 0 0.5rem 0;
        margin-bottom: 1.5rem;
        color: #1f2937;
        font-size: 1.1rem;
      }
      .state-description {
        color: #6b7280;
        font-size: 0.9rem;
        margin-bottom: 1rem;
        line-height: 1.4;
      }
      .state-info {
        background: #fff;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .state-info h4 {
        margin: 0 0 1rem 0;
        color: #1f2937;
        font-size: 1.1rem;
      }
      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
      }
      .info-item {
        padding: 1rem;
        background: #f9fafb;
        border-radius: 6px;
        border: 1px solid #e5e7eb;
      }
      .info-item h5 {
        margin: 0 0 0.75rem 0;
        color: #1f2937;
        font-size: 1rem;
      }
      .info-item ul {
        margin: 0;
        padding-left: 1.25rem;
      }
      .info-item li {
        margin-bottom: 0.25rem;
        line-height: 1.4;
        font-size: 0.9rem;
      }
    `,
  ],
})
export class StatesDemoComponent {
  defaultValue = '';
  disabledValue = '';
  mixedValue = 'option2';
  allDisabledValue = 'option1';

  defaultOptions: RadioOption[] = [
    { label: 'Option 1', value: 'option1' },
    { label: 'Option 2', value: 'option2' },
    { label: 'Option 3', value: 'option3' },
    { label: 'Option 4', value: 'option4' },
  ];

  disabledOptions: RadioOption[] = [
    { label: 'Option 1 (Disabled)', value: 'option1', disabled: true },
    { label: 'Option 2', value: 'option2' },
    { label: 'Option 3 (Disabled)', value: 'option3', disabled: true },
    { label: 'Option 4', value: 'option4' },
  ];

  mixedOptions: RadioOption[] = [
    { label: 'Option 1', value: 'option1' },
    { label: 'Option 2 (Selected)', value: 'option2' },
    { label: 'Option 3 (Disabled)', value: 'option3', disabled: true },
    { label: 'Option 4', value: 'option4' },
  ];

  allDisabledOptions: RadioOption[] = [
    { label: 'Option 1 (Disabled)', value: 'option1', disabled: true },
    { label: 'Option 2 (Disabled)', value: 'option2', disabled: true },
    { label: 'Option 3 (Disabled)', value: 'option3', disabled: true },
    { label: 'Option 4 (Disabled)', value: 'option4', disabled: true },
  ];

  onSelectionChange(variant: string, value: string) {
    console.log(`${variant} state variant selection changed:`, value);
  }
}
