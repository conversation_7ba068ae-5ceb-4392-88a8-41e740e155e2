import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  RadioButtonComponent,
  RadioOption,
} from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-form-integration-demo',
  standalone: true,
  imports: [CommonModule, RadioButtonComponent, ReactiveFormsModule],
  template: `
    <div class="demo-section center-demo">
      <h3>Form Integration</h3>
      <p class="description">
        Complete Angular forms integration with ControlValueAccessor
        implementation for reactive and template-driven forms.
      </p>

      <div class="forms-grid">
        <div class="form-group">
          <h4>Reactive Form</h4>
          <p class="form-description">
            Integration with Angular reactive forms including validation and
            form control binding.
          </p>

          <form [formGroup]="reactiveForm" (ngSubmit)="onReactiveSubmit()">
            <div class="form-field">
              <label>Theme Selection:</label>
              <ava-radio-button
                formControlName="theme"
                [options]="themeOptions"
                name="theme-selection"
                (selectedValueChange)="onReactiveThemeChange($event)"
              ></ava-radio-button>
            </div>

            <div class="form-field">
              <label>Size Preference:</label>
              <ava-radio-button
                formControlName="size"
                [options]="sizeOptions"
                name="size-selection"
                (selectedValueChange)="onReactiveSizeChange($event)"
              ></ava-radio-button>
            </div>

            <div class="form-status">
              <p><strong>Form Status:</strong> {{ reactiveForm.status }}</p>
              <p>
                <strong>Theme Value:</strong>
                {{ reactiveForm.get('theme')?.value || 'Not selected' }}
              </p>
              <p>
                <strong>Size Value:</strong>
                {{ reactiveForm.get('size')?.value || 'Not selected' }}
              </p>
            </div>

            <button
              type="submit"
              [disabled]="!reactiveForm.valid"
              class="submit-btn"
            >
              Submit Reactive Form
            </button>
          </form>
        </div>

        <div class="form-group">
          <h4>Template-Driven Form</h4>
          <p class="form-description">
            Two-way data binding with [(ngModel)] for template-driven forms.
          </p>

          <div class="form-field">
            <label>Language Preference:</label>
            <ava-radio-button
              [(selectedValue)]="templateLanguage"
              [options]="languageOptions"
              name="language-selection"
              (selectedValueChange)="onTemplateLanguageChange($event)"
            ></ava-radio-button>
          </div>

          <div class="form-field">
            <label>Notification Settings:</label>
            <ava-radio-button
              [(selectedValue)]="templateNotifications"
              [options]="notificationOptions"
              name="notification-selection"
              (selectedValueChange)="onTemplateNotificationChange($event)"
            ></ava-radio-button>
          </div>

          <div class="form-status">
            <p>
              <strong>Language Value:</strong>
              {{ templateLanguage || 'Not selected' }}
            </p>
            <p>
              <strong>Notifications Value:</strong>
              {{ templateNotifications || 'Not selected' }}
            </p>
          </div>

          <button (click)="onTemplateSubmit()" class="submit-btn">
            Submit Template Form
          </button>
        </div>
      </div>

      <div class="form-info">
        <h4>Form Integration Features</h4>
        <div class="info-grid">
          <div class="info-item">
            <h5>Reactive Forms</h5>
            <ul>
              <li>
                <strong>FormControl binding</strong> - Direct integration with
                Angular reactive forms
              </li>
              <li>
                <strong>Validation support</strong> - Works with Angular
                validators
              </li>
              <li>
                <strong>Value tracking</strong> - Proper change detection and
                emission
              </li>
              <li>
                <strong>Disabled control</strong> - Programmatic enable/disable
                support
              </li>
            </ul>
          </div>
          <div class="info-item">
            <h5>Template-Driven Forms</h5>
            <ul>
              <li>
                <strong>NgModel support</strong> - Two-way data binding with
                [(ngModel)]
              </li>
              <li>
                <strong>Template validation</strong> - Works with validation
                directives
              </li>
              <li>
                <strong>Form submission</strong> - Integrates with form
                workflows
              </li>
            </ul>
          </div>
          <div class="info-item">
            <h5>Event Handling</h5>
            <ul>
              <li>
                <strong>selectedValueChange</strong> - Emitted when selection
                changes
              </li>
              <li>
                <strong>Value type</strong> - Always emits string values
                matching option values
              </li>
              <li>
                <strong>Change detection</strong> - Proper Angular change
                detection integration
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .center-demo {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 60vh;
      }
      .demo-section {
        margin-bottom: 2rem;
        padding: 2rem;
        background: #fff;
        border-radius: 8px;
        max-width: 900px;
        margin-left: auto;
        margin-right: auto;
      }
      .description {
        color: #666;
        margin-bottom: 2rem;
        text-align: center;
      }
      .forms-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
      }
      .form-group {
        background: #fff;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .form-group h4 {
        margin: 0 0 0.5rem 0;
        color: #1f2937;
        font-size: 1.1rem;
      }
      .form-description {
        color: #6b7280;
        font-size: 0.9rem;
        margin-bottom: 1rem;
        line-height: 1.4;
      }
      .form-field {
        margin-bottom: 1.5rem;
      }
      .form-field label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #374151;
      }
      .form-status {
        background: #f9fafb;
        padding: 1rem;
        border-radius: 6px;
        border: 1px solid #e5e7eb;
        margin-bottom: 1rem;
      }
      .form-status p {
        margin: 0.25rem 0;
        font-size: 0.9rem;
      }
      .submit-btn {
        background: #3b82f6;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 6px;
        cursor: pointer;
        font-size: 0.9rem;
      }
      .submit-btn:hover {
        background: #2563eb;
      }
      .submit-btn:disabled {
        background: #9ca3af;
        cursor: not-allowed;
      }
      .form-info {
        background: #fff;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .form-info h4 {
        margin: 0 0 1rem 0;
        color: #1f2937;
        font-size: 1.1rem;
      }
      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
      }
      .info-item {
        padding: 1rem;
        background: #f9fafb;
        border-radius: 6px;
        border: 1px solid #e5e7eb;
      }
      .info-item h5 {
        margin: 0 0 0.75rem 0;
        color: #1f2937;
        font-size: 1rem;
      }
      .info-item ul {
        margin: 0;
        padding-left: 1.25rem;
      }
      .info-item li {
        margin-bottom: 0.25rem;
        line-height: 1.4;
        font-size: 0.9rem;
      }
    `,
  ],
})
export class FormIntegrationDemoComponent {
  reactiveForm: FormGroup;
  templateLanguage = '';
  templateNotifications = '';

  themeOptions: RadioOption[] = [
    { label: 'Light Theme', value: 'light' },
    { label: 'Dark Theme', value: 'dark' },
    { label: 'Auto Theme', value: 'auto' },
  ];

  sizeOptions: RadioOption[] = [
    { label: 'Small', value: 'small' },
    { label: 'Medium', value: 'medium' },
    { label: 'Large', value: 'large' },
  ];

  languageOptions: RadioOption[] = [
    { label: 'English', value: 'en' },
    { label: 'Spanish', value: 'es' },
    { label: 'French', value: 'fr' },
    { label: 'German', value: 'de' },
  ];

  notificationOptions: RadioOption[] = [
    { label: 'All Notifications', value: 'all' },
    { label: 'Important Only', value: 'important' },
    { label: 'None', value: 'none' },
  ];

  constructor(private fb: FormBuilder) {
    this.reactiveForm = this.fb.group({
      theme: ['', Validators.required],
      size: ['medium', Validators.required],
    });
  }

  onReactiveThemeChange(value: string) {
    console.log('Reactive form theme changed:', value);
  }

  onReactiveSizeChange(value: string) {
    console.log('Reactive form size changed:', value);
  }

  onTemplateLanguageChange(value: string) {
    console.log('Template form language changed:', value);
  }

  onTemplateNotificationChange(value: string) {
    console.log('Template form notifications changed:', value);
  }

  onReactiveSubmit() {
    if (this.reactiveForm.valid) {
      console.log('Reactive form submitted:', this.reactiveForm.value);
    }
  }

  onTemplateSubmit() {
    console.log('Template form submitted:', {
      language: this.templateLanguage,
      notifications: this.templateNotifications,
    });
  }
}
