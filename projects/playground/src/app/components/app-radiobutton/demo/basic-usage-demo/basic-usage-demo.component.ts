import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  RadioButtonComponent,
  RadioOption,
} from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-basic-usage-demo',
  standalone: true,
  imports: [CommonModule, RadioButtonComponent],
  template: `
    <div class="demo-section center-demo">
      <div class="radio-group">
        <ava-radio-button
          [options]="basicOptions"
          name="basic-radio"
          [(selectedValue)]="selectedValue"
          (selectedValueChange)="onSelectionChange($event)"
        ></ava-radio-button>
      </div>
    </div>
  `,
  styles: [
    `
      .center-demo {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 60vh;
      }
      .demo-section {
        margin-bottom: 2rem;
        padding: 2rem;
        border-radius: 8px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
      }
      .description {
        color: #666;
        margin-bottom: 1.5rem;
        text-align: center;
      }
      .radio-group {
        margin-bottom: 2rem;
      }
      .status-display {
        background: #f9fafb;
        padding: 1rem;
        border-radius: 6px;
        border: 1px solid #e5e7eb;
        text-align: center;
      }
      .status-display p {
        margin: 0.25rem 0;
      }
    `,
  ],
})
export class BasicUsageDemoComponent {
  selectedValue = '';

  basicOptions: RadioOption[] = [
    { label: 'Option 1', value: 'option1' },
    { label: 'Option 2', value: 'option2' },
    { label: 'Option 3', value: 'option3' },
    { label: 'Option 4', value: 'option4' },
  ];

  onSelectionChange(value: string) {
    console.log('Radio button selection changed:', value);
  }
}
