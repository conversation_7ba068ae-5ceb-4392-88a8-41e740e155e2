import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  RadioButtonComponent,
  RadioOption,
} from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-custom-colors-demo',
  standalone: true,
  imports: [CommonModule, RadioButtonComponent],
  template: `
    <div class="demo-section center-demo">
      <div class="colors-grid">
        <div class="color-group">
          <ava-radio-button
            [options]="colorOptions"
            name="full-custom"
            color="#8e44ad"
            labelColor="#1996FC"
            [(selectedValue)]="fullCustomValue"
            (selectedValueChange)="onSelectionChange('full-custom', $event)"
          ></ava-radio-button>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .center-demo {
        display: flex;
        flex-direction: column;
        align-items: center;
        min-height: 60vh;
      }
      .demo-section {
        margin-bottom: 2rem;
        padding: 2rem;
        margin-top: 0;
        max-width: 870px;
        margin-left: auto;
        margin-right: auto;
      }
      .description {
        color: #666;
        margin-bottom: 2rem;
        text-align: center;
      }
      .colors-grid {
        display: flex;
        gap: 2rem;
        justify-content: center;
        flex-wrap: wrap;
        margin-bottom: 2rem;
      }
      .color-group {
        padding: 1.5rem;
        margin-top: 0;
      }
      .color-group h4 {
        margin: 0 0 0.5rem 0;
        color: #1f2937;
        font-size: 1.1rem;
      }
      .color-description {
        color: #6b7280;
        font-size: 0.9rem;
        margin-bottom: 1rem;
        line-height: 1.4;
      }
      .color-info {
        background: #fff;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .color-info h4 {
        margin: 0 0 1rem 0;
        color: #1f2937;
        font-size: 1.1rem;
      }
      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
      }
      .info-item {
        padding: 1rem;
        background: #f9fafb;
        border-radius: 6px;
        border: 1px solid #e5e7eb;
      }
      .info-item h5 {
        margin: 0 0 0.75rem 0;
        color: #1f2937;
        font-size: 1rem;
      }
      .info-item ul {
        margin: 0;
        padding-left: 1.25rem;
      }
      .info-item li {
        margin-bottom: 0.25rem;
        line-height: 1.4;
        font-size: 0.9rem;
      }
    `,
  ],
})
export class CustomColorsDemoComponent {
  defaultValue = '';
  customRadioValue = '';
  customLabelValue = '';
  fullCustomValue = '';

  colorOptions: RadioOption[] = [
    { label: 'Option 1', value: 'option1' },
    { label: 'Option 2', value: 'option2' },
    { label: 'Option 3', value: 'option3' },
    { label: 'Option 4', value: 'option4' },
  ];

  onSelectionChange(variant: string, value: string) {
    console.log(`${variant} color variant selection changed:`, value);
  }
}
