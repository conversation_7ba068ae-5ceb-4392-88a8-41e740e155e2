import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  RadioButtonComponent,
  RadioOption,
} from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-orientations-demo',
  standalone: true,
  imports: [CommonModule, RadioButtonComponent],
  template: `
    <div class="demo-section center-demo">
      <div class="orientations-grid">
        <div class="orientation-group">
          <h4 style="color: var(--text-primary)">Vertical Layout (Default)</h4>
          <ava-radio-button
            [options]="orientationOptions"
            name="vertical-radio"
            orientation="vertical"
            [(selectedValue)]="verticalValue"
            (selectedValueChange)="onSelectionChange('vertical', $event)"
          ></ava-radio-button>
        </div>
        <div class="orientation-group">
          <h4 style="color: var(--text-primary)">Horizontal Layout</h4>
          <ava-radio-button
            [options]="orientationOptions"
            name="horizontal-radio"
            orientation="horizontal"
            [(selectedValue)]="horizontalValue"
            (selectedValueChange)="onSelectionChange('horizontal', $event)"
          ></ava-radio-button>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .center-demo {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 60vh;
      }
      .demo-section {
        margin-bottom: 2rem;
        padding: 2rem;
        margin-top: 0;
        max-width: 900px;
        margin-left: auto;
        margin-right: auto;
      }
      .description {
        color: #666;
        margin-bottom: 2rem;
        text-align: center;
      }
      .orientations-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
      }
      .orientation-group {
        padding: 1.5rem;
        margin-top: 0;
      }
      .orientation-group h4 {
        margin: 0 0 0.5rem 0;
        color: #1f2937;
        font-size: 1.1rem;
      }
      .orientation-description {
        color: #6b7280;
        font-size: 0.9rem;
        margin-bottom: 1rem;
        line-height: 1.4;
      }
      .orientation-info {
        background: #fff;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
      }
      .orientation-info h4 {
        margin: 0 0 1rem 0;
        color: #1f2937;
        font-size: 1.1rem;
      }
      .info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
      }
      .info-item {
        padding: 1rem;
        background: #f9fafb;
        border-radius: 6px;
        border: 1px solid #e5e7eb;
      }
      .info-item h5 {
        margin: 0 0 0.75rem 0;
        color: #1f2937;
        font-size: 1rem;
      }
      .info-item ul {
        margin: 0;
        padding-left: 1.25rem;
      }
      .info-item li {
        margin-bottom: 0.25rem;
        line-height: 1.4;
        font-size: 0.9rem;
      }
    `,
  ],
})
export class OrientationsDemoComponent {
  verticalValue = '';
  horizontalValue = '';

  orientationOptions: RadioOption[] = [
    { label: 'Option 1', value: 'option1' },
    { label: 'Option 2', value: 'option2' },
    { label: 'Option 3', value: 'option3' },
    { label: 'Option 4', value: 'option4' },
  ];

  onSelectionChange(orientation: string, value: string) {
    console.log(`${orientation} orientation selection changed:`, value);
  }
}
