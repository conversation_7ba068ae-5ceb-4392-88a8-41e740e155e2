import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  AvaAutocompleteComponent,
  AvaAutocompleteOption,
} from '../../../../../../../play-comp-library/src/lib/components/autocomplete/ava-autocomplete.component';
import { colorOptions, techStackOptions } from '../autocomplete-demo.data';

@Component({
  selector: 'ava-autocomplete-custom-styles-demo',
  standalone: true,
  imports: [CommonModule, AvaAutocompleteComponent],
  template: `
    <div class="demo-section">
      <div class="demo-description">
        <h3>Custom Styles</h3>
        <p>
          Customize autocomplete appearance with CSS custom properties and
          advanced styling options.
        </p>
      </div>

      <div class="demo-grid">
        <div class="demo-item">
          <h4>Color Picker Style</h4>
          <ava-autocomplete
            [options]="colorOptions"
            placeholder="Choose a color..."
            label="Select color"
            helper="Pick a color from the palette"
            [optionTemplate]="colorTemplate"
            (optionSelected)="onColorSelected($event)"
            class="color-picker-style"
          ></ava-autocomplete>
        </div>

        <div class="demo-item">
          <h4>Technology Stack Style</h4>
          <ava-autocomplete
            [options]="techStackOptions"
            placeholder="Select technologies..."
            label="Tech Stack"
            helper="Choose your technology stack"
            [multi]="true"
            tagColor="success"
            tagVariant="outlined"
            [tagPill]="true"
            [tagRemovable]="true"
            (valueChange)="onTechStackChange($event)"
            class="tech-stack-style"
          ></ava-autocomplete>
        </div>

        <div class="demo-item">
          <h4>Dark Theme Style</h4>
          <ava-autocomplete
            [options]="colorOptions"
            placeholder="Search in dark mode..."
            label="Dark Theme"
            helper="Autocomplete with dark theme styling"
            startIcon="moon"
            startIconColor="#a78bfa"
            (optionSelected)="onDarkThemeSelected($event)"
            class="dark-theme-style"
          ></ava-autocomplete>
        </div>

        <div class="demo-item">
          <h4>Minimal Style</h4>
          <ava-autocomplete
            [options]="colorOptions"
            placeholder="Minimal design..."
            label="Minimal"
            helper="Clean and minimal autocomplete design"
            (optionSelected)="onMinimalSelected($event)"
            class="minimal-style"
          ></ava-autocomplete>
        </div>
      </div>

      <div
        class="demo-output"
        *ngIf="
          selectedColor ||
          selectedTechStack.length > 0 ||
          selectedDarkTheme ||
          selectedMinimal
        "
      >
        <h4>Output:</h4>
        <div class="output-item" *ngIf="selectedColor">
          <strong>Selected Color:</strong>
          <span
            class="color-preview"
            [style.background]="selectedColor['color']"
          ></span>
          {{ selectedColor.label }}
        </div>
        <div class="output-item" *ngIf="selectedTechStack.length > 0">
          <strong>Tech Stack:</strong> {{ selectedTechStack.join(', ') }}
        </div>
        <div class="output-item" *ngIf="selectedDarkTheme">
          <strong>Dark Theme Selection:</strong> {{ selectedDarkTheme.label }}
        </div>
        <div class="output-item" *ngIf="selectedMinimal">
          <strong>Minimal Selection:</strong> {{ selectedMinimal.label }}
        </div>
      </div>

      <div class="features-list">
        <h4>Custom Styling Features:</h4>
        <ul>
          <li>
            <strong>CSS Custom Properties:</strong> Easy theming with CSS
            variables
          </li>
          <li>
            <strong>Custom Templates:</strong> Complete control over option
            appearance
          </li>
          <li>
            <strong>Theme Variants:</strong> Light, dark, and custom theme
            support
          </li>
          <li>
            <strong>Responsive Design:</strong> Adapts to different screen sizes
          </li>
          <li>
            <strong>Brand Integration:</strong> Match your brand colors and
            styling
          </li>
        </ul>
      </div>
    </div>

    <!-- Color Template -->
    <ng-template #colorTemplate let-option>
      <div class="color-option">
        <div class="color-swatch" [style.background]="option.color"></div>
        <span class="color-name">{{ option.label }}</span>
      </div>
    </ng-template>
  `,
  styles: [
    `
      .demo-section {
        max-width: 1200px;
        margin: 2rem auto;
        padding: 2rem;
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }

      .demo-description {
        margin-bottom: 2rem;
        text-align: center;
      }

      .demo-description h3 {
        color: #1f2937;
        margin-bottom: 0.5rem;
        font-size: 1.5rem;
        font-weight: 600;
      }

      .demo-description p {
        color: #6b7280;
        font-size: 0.95rem;
        line-height: 1.5;
      }

      .demo-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
      }

      .demo-item {
        padding: 1.5rem;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        background: #fafafa;
      }

      .demo-item h4 {
        color: #374151;
        margin-bottom: 1rem;
        font-size: 1.1rem;
        font-weight: 600;
      }

      .demo-output {
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
      }

      .demo-output h4 {
        color: #374151;
        margin-bottom: 1rem;
        font-size: 1.1rem;
        font-weight: 600;
      }

      .output-item {
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: #fff;
        border-radius: 6px;
        border-left: 3px solid #3b82f6;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .output-item strong {
        color: #374151;
        margin-right: 0.5rem;
      }

      .color-preview {
        width: 20px;
        height: 20px;
        border-radius: 4px;
        border: 1px solid #d1d5db;
      }

      .features-list {
        background: #f0f9ff;
        border: 1px solid #bae6fd;
        border-radius: 8px;
        padding: 1.5rem;
      }

      .features-list h4 {
        color: #0369a1;
        margin-bottom: 1rem;
        font-size: 1.1rem;
        font-weight: 600;
      }

      .features-list ul {
        margin: 0;
        padding-left: 1.5rem;
      }

      .features-list li {
        margin-bottom: 0.5rem;
        color: #0c4a6e;
        line-height: 1.5;
      }

      .features-list li strong {
        color: #0369a1;
      }

      /* Color Option Template Styles */
      .color-option {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.5rem;
      }

      .color-swatch {
        width: 24px;
        height: 24px;
        border-radius: 4px;
        border: 1px solid #d1d5db;
        flex-shrink: 0;
      }

      .color-name {
        font-weight: 500;
        color: #374151;
      }

      /* Custom Style Classes */
      .color-picker-style {
        --autocomplete-background: #fefefe;
        --autocomplete-border-color: #e5e7eb;
        --autocomplete-border-radius: 12px;
        --autocomplete-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }

      .tech-stack-style {
        --autocomplete-background: #f0fdf4;
        --autocomplete-border-color: #bbf7d0;
        --autocomplete-border-radius: 8px;
        --autocomplete-shadow: 0 2px 4px rgba(34, 197, 94, 0.1);
      }

      .dark-theme-style {
        --autocomplete-background: #1f2937;
        --autocomplete-border-color: #374151;
        --autocomplete-border-radius: 8px;
        --autocomplete-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        --autocomplete-option-color: #f9fafb;
        --autocomplete-option-hover-bg: #374151;
        --autocomplete-option-hover-color: #ffffff;
      }

      .minimal-style {
        --autocomplete-background: #ffffff;
        --autocomplete-border-color: #d1d5db;
        --autocomplete-border-width: 1px;
        --autocomplete-border-radius: 4px;
        --autocomplete-shadow: none;
        --autocomplete-option-hover-bg: #f3f4f6;
      }

      @media (max-width: 768px) {
        .demo-grid {
          grid-template-columns: 1fr;
        }
      }

      :host {
        display: block;
        background: #f3f4f6;
        min-height: 100vh;
        padding: 1rem;
      }
    `,
  ],
})
export class AppAutocompleteCustomStylesDemoComponent {
  colorOptions = colorOptions;
  techStackOptions = techStackOptions;
  selectedColor: AvaAutocompleteOption | null = null;
  selectedTechStack: string[] = [];
  selectedDarkTheme: AvaAutocompleteOption | null = null;
  selectedMinimal: AvaAutocompleteOption | null = null;

  onColorSelected(option: AvaAutocompleteOption) {
    this.selectedColor = option;
  }

  onTechStackChange(value: string | string[]) {
    this.selectedTechStack = Array.isArray(value) ? value : [];
  }

  onDarkThemeSelected(option: AvaAutocompleteOption) {
    this.selectedDarkTheme = option;
  }

  onMinimalSelected(option: AvaAutocompleteOption) {
    this.selectedMinimal = option;
  }
}
