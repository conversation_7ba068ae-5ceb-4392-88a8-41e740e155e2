import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  AvaAutocompleteComponent,
  AvaAutocompleteOption,
} from '../../../../../../../play-comp-library/src/lib/components/autocomplete/ava-autocomplete.component';
import { countryOptions } from '../autocomplete-demo.data';

@Component({
  selector: 'ava-autocomplete-multi-select-demo',
  standalone: true,
  imports: [CommonModule, AvaAutocompleteComponent],
  template: `
    <div class="demo-section">
      <div class="demo-container">
        <ava-autocomplete
          [options]="options"
          [multi]="true"
          placeholder="Search for countries..."
          label="Select countries"
          helper="Start typing to see filtered options"
          tagColor="primary"
          tagVariant="filled"
          tagSize="sm"
          [tagPill]="true"
          [tagRemovable]="true"
          (valueChange)="onValueChange($event)"
        ></ava-autocomplete>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-section {
        max-width: 700px;
        margin: 2rem auto;
        margin-top: 0;
        padding: 2rem;
      }

      .demo-description {
        margin-bottom: 2rem;
        text-align: center;
      }

      .demo-description h3 {
        color: #1f2937;
        margin-bottom: 0.5rem;
        font-size: 1.5rem;
        font-weight: 600;
      }

      .demo-description p {
        color: #6b7280;
        font-size: 0.95rem;
        line-height: 1.5;
      }

      .demo-container {
        margin-bottom: 2rem;
      }

      .demo-output {
        padding: 1.5rem;
        margin-top: 1.5rem;
      }

      .demo-output h4 {
        color: #374151;
        margin-bottom: 1rem;
        font-size: 1.1rem;
        font-weight: 600;
      }

      .output-item {
        margin-bottom: 1rem;
        padding: 0.75rem;
      }

      .output-item strong {
        color: #374151;
        margin-right: 0.5rem;
      }

      .selected-list {
        margin: 0.5rem 0 0 0;
        padding-left: 1.5rem;
      }

      .selected-list li {
        margin-bottom: 0.25rem;
        color: #4b5563;
      }

      :host {
        display: block;
        min-height: 100vh;
        padding: 1rem;
      }
    `,
  ],
})
export class AppAutocompleteMultiSelectDemoComponent {
  options = countryOptions;
  selectedOptions: AvaAutocompleteOption[] = [];
  currentValue = '';

  onValueChange(value: string | string[]) {
    this.currentValue = Array.isArray(value) ? value.join(', ') : value;
    // Update selected options based on the emitted values
    this.selectedOptions = this.options.filter((option) =>
      Array.isArray(value)
        ? value.includes(option.value)
        : value === option.value
    );
  }
}
