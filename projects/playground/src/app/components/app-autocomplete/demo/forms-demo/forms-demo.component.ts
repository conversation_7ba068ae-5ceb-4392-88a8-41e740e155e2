import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import {
  AvaAutocompleteComponent,
  AvaAutocompleteOption,
} from '../../../../../../../play-comp-library/src/lib/components/autocomplete/ava-autocomplete.component';
import { validationOptions } from '../autocomplete-demo.data';

@Component({
  selector: 'ava-autocomplete-forms-demo',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, AvaAutocompleteComponent],
  template: `
    <div class="demo-section">
      <div class="demo-description">
        <h3>Form Integration</h3>
        <p>
          Seamless integration with Angular reactive forms and template-driven
          forms.
        </p>
      </div>

      <form [formGroup]="form" (ngSubmit)="onSubmit()" class="demo-form">
        <div class="form-row">
          <div class="form-field">
            <ava-autocomplete
              formControlName="validationType"
              [options]="validationOptions"
              placeholder="Select validation type..."
              label="Validation Type"
              helper="Choose a validation type for the form"
              [required]="true"
              error="Please select a validation type"
            ></ava-autocomplete>
          </div>
        </div>

        <div class="form-row">
          <div class="form-field">
            <ava-autocomplete
              formControlName="email"
              [options]="emailOptions"
              placeholder="Enter email..."
              label="Email Address"
              helper="Enter a valid email address"
              [required]="true"
              [error]="getFieldError('email')"
            ></ava-autocomplete>
          </div>
        </div>

        <div class="form-row">
          <div class="form-field">
            <ava-autocomplete
              formControlName="phone"
              [options]="phoneOptions"
              placeholder="Enter phone number..."
              label="Phone Number"
              helper="Enter a valid phone number"
              [error]="getFieldError('phone')"
            ></ava-autocomplete>
          </div>
        </div>

        <div class="form-actions">
          <button type="submit" class="submit-btn" [disabled]="form.invalid">
            Submit Form
          </button>
          <button type="button" class="reset-btn" (click)="resetForm()">
            Reset Form
          </button>
        </div>
      </form>

      <div class="demo-output" *ngIf="formSubmitted">
        <h4>Form Submission:</h4>
        <div class="output-item">
          <strong>Form Valid:</strong> {{ form.valid ? 'Yes' : 'No' }}
        </div>
        <div class="output-item">
          <strong>Form Values:</strong>
          <pre>{{ form.value | json }}</pre>
        </div>
        <div class="output-item" *ngIf="form.errors">
          <strong>Form Errors:</strong>
          <pre>{{ form.errors | json }}</pre>
        </div>
      </div>

      <div class="features-list">
        <h4>Form Features:</h4>
        <ul>
          <li>
            <strong>ControlValueAccessor:</strong> Full Angular form integration
          </li>
          <li>
            <strong>Validation Support:</strong> Works with form validators
          </li>
          <li>
            <strong>Error Display:</strong> Automatic error message display
          </li>
          <li>
            <strong>Required Fields:</strong> Support for required field
            validation
          </li>
          <li>
            <strong>Disabled States:</strong> Proper disabled state handling
          </li>
        </ul>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-section {
        max-width: 700px;
        margin: 2rem auto;
        padding: 2rem;
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }

      .demo-description {
        margin-bottom: 2rem;
        text-align: center;
      }

      .demo-description h3 {
        color: #1f2937;
        margin-bottom: 0.5rem;
        font-size: 1.5rem;
        font-weight: 600;
      }

      .demo-description p {
        color: #6b7280;
        font-size: 0.95rem;
        line-height: 1.5;
      }

      .demo-form {
        margin-bottom: 2rem;
      }

      .form-row {
        margin-bottom: 1.5rem;
      }

      .form-field {
        width: 100%;
      }

      .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
      }

      .submit-btn,
      .reset-btn {
        padding: 0.75rem 1.5rem;
        border-radius: 6px;
        font-size: 0.95rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s;
        border: none;
      }

      .submit-btn {
        background: #3b82f6;
        color: white;
      }

      .submit-btn:hover:not(:disabled) {
        background: #2563eb;
      }

      .submit-btn:disabled {
        background: #9ca3af;
        cursor: not-allowed;
      }

      .reset-btn {
        background: #f3f4f6;
        color: #374151;
        border: 1px solid #d1d5db;
      }

      .reset-btn:hover {
        background: #e5e7eb;
      }

      .demo-output {
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
      }

      .demo-output h4 {
        color: #374151;
        margin-bottom: 1rem;
        font-size: 1.1rem;
        font-weight: 600;
      }

      .output-item {
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: #fff;
        border-radius: 6px;
        border-left: 3px solid #3b82f6;
      }

      .output-item strong {
        color: #374151;
        margin-right: 0.5rem;
      }

      .output-item pre {
        margin: 0.5rem 0 0 0;
        padding: 0.5rem;
        background: #f3f4f6;
        border-radius: 4px;
        font-size: 0.85rem;
        overflow-x: auto;
      }

      .features-list {
        background: #f0f9ff;
        border: 1px solid #bae6fd;
        border-radius: 8px;
        padding: 1.5rem;
      }

      .features-list h4 {
        color: #0369a1;
        margin-bottom: 1rem;
        font-size: 1.1rem;
        font-weight: 600;
      }

      .features-list ul {
        margin: 0;
        padding-left: 1.5rem;
      }

      .features-list li {
        margin-bottom: 0.5rem;
        color: #0c4a6e;
        line-height: 1.5;
      }

      .features-list li strong {
        color: #0369a1;
      }

      :host {
        display: block;
        background: #f3f4f6;
        min-height: 100vh;
        padding: 1rem;
      }
    `,
  ],
})
export class AppAutocompleteFormsDemoComponent {
  form: FormGroup;
  formSubmitted = false;
  validationOptions = validationOptions;
  emailOptions: AvaAutocompleteOption[] = [
    { label: '<EMAIL>', value: '<EMAIL>' },
    { label: '<EMAIL>', value: '<EMAIL>' },
    { label: '<EMAIL>', value: '<EMAIL>' },
  ];
  phoneOptions: AvaAutocompleteOption[] = [
    { label: '+****************', value: '+****************' },
    { label: '+****************', value: '+****************' },
    { label: '+****************', value: '+****************' },
  ];

  constructor(private fb: FormBuilder) {
    this.form = this.fb.group({
      validationType: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', Validators.pattern(/^\+?[\d\s\-()]+$/)],
    });
  }

  onSubmit() {
    this.formSubmitted = true;
    if (this.form.valid) {
      console.log('Form submitted:', this.form.value);
    }
  }

  resetForm() {
    this.form.reset();
    this.formSubmitted = false;
  }

  getFieldError(fieldName: string): string {
    const field = this.form.get(fieldName);
    if (field && field.errors && field.touched) {
      if (field.errors['required']) {
        return `${
          fieldName.charAt(0).toUpperCase() + fieldName.slice(1)
        } is required`;
      }
      if (field.errors['email']) {
        return 'Please enter a valid email address';
      }
      if (field.errors['pattern']) {
        return 'Please enter a valid phone number';
      }
    }
    return '';
  }
}
