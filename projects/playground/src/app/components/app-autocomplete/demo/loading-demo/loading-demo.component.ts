import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Observable, of, delay, map } from 'rxjs';
import {
  AvaAutocompleteComponent,
  AvaAutocompleteOption,
} from '../../../../../../../play-comp-library/src/lib/components/autocomplete/ava-autocomplete.component';
import { userOptions } from '../autocomplete-demo.data';

@Component({
  selector: 'ava-autocomplete-loading-demo',
  standalone: true,
  imports: [CommonModule, AvaAutocompleteComponent],
  template: `
    <div class="demo-section">
      <div class="demo-description">
        <h3>Loading States</h3>
        <p>
          Autocomplete with loading indicators while fetching options from
          asynchronous sources.
        </p>
      </div>

      <div class="demo-container">
        <ava-autocomplete
          [options]="options$"
          [loading]="loading"
          placeholder="Search for users..."
          label="Search users"
          helper="Simple loading state demonstration"
          startIcon="search"
          startIconColor="#6b7280"
          [minLength]="1"
          (optionSelected)="onOptionSelected($event)"
          (valueChange)="onValueChange($event)"
        ></ava-autocomplete>
      </div>

      <div
        class="demo-output"
        *ngIf="selectedOption || currentValue || loading"
      >
        <h4>Output:</h4>
        <div class="output-item" *ngIf="loading">
          <strong>Status:</strong>
          <span class="loading-status">🔄 Loading...</span>
        </div>
        <div class="output-item" *ngIf="selectedOption">
          <strong>Selected Option:</strong> {{ selectedOption.label }} ({{
            selectedOption.value
          }})
        </div>
        <div class="output-item" *ngIf="currentValue">
          <strong>Current Value:</strong> {{ currentValue }}
        </div>
      </div>

      <div class="features-list">
        <h4>Loading Features:</h4>
        <ul>
          <li>
            <strong>Observable Integration:</strong> Simple Observable-based
            data loading
          </li>
          <li>
            <strong>Loading States:</strong> Built-in loading indicator support
          </li>
          <li>
            <strong>Delayed Loading:</strong> Simulates real API loading with
            delay
          </li>
          <li>
            <strong>Reset Functionality:</strong> Easy demo reset with fresh
            loading state
          </li>
        </ul>
      </div>

      <div class="demo-controls">
        <button class="reset-btn" (click)="resetDemo()" [disabled]="loading">
          Reset Demo
        </button>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-section {
        max-width: 700px;
        margin: 2rem auto;
        padding: 2rem;
        background: #fff;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
      }

      .demo-description {
        margin-bottom: 2rem;
        text-align: center;
      }

      .demo-description h3 {
        color: #1f2937;
        margin-bottom: 0.5rem;
        font-size: 1.5rem;
        font-weight: 600;
      }

      .demo-description p {
        color: #6b7280;
        font-size: 0.95rem;
        line-height: 1.5;
      }

      .demo-container {
        margin-bottom: 2rem;
      }

      .demo-output {
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
      }

      .demo-output h4 {
        color: #374151;
        margin-bottom: 1rem;
        font-size: 1.1rem;
        font-weight: 600;
      }

      .output-item {
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: #fff;
        border-radius: 6px;
        border-left: 3px solid #3b82f6;
      }

      .output-item strong {
        color: #374151;
        margin-right: 0.5rem;
      }

      .features-list {
        background: #f0f9ff;
        border: 1px solid #bae6fd;
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
      }

      .features-list h4 {
        color: #0369a1;
        margin-bottom: 1rem;
        font-size: 1.1rem;
        font-weight: 600;
      }

      .features-list ul {
        margin: 0;
        padding-left: 1.5rem;
      }

      .features-list li {
        margin-bottom: 0.5rem;
        color: #0c4a6e;
        line-height: 1.5;
      }

      .features-list li strong {
        color: #0369a1;
      }

      .demo-controls {
        text-align: center;
      }

      .reset-btn {
        background: #3b82f6;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 6px;
        font-size: 0.95rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
      }

      .reset-btn:hover:not(:disabled) {
        background: #2563eb;
      }

      .reset-btn:disabled {
        background: #9ca3af;
        cursor: not-allowed;
      }

      .loading-status {
        color: #3b82f6;
        font-weight: 500;
        animation: pulse 1.5s ease-in-out infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
      }

      :host {
        display: block;
        background: #f3f4f6;
        min-height: 100vh;
        padding: 1rem;
      }
    `,
  ],
})
export class AppAutocompleteLoadingDemoComponent implements OnInit {
  // Simple Observable approach like the working example
  options$: Observable<AvaAutocompleteOption[]> = of([]);
  loading = false;
  selectedOption: AvaAutocompleteOption | null = null;
  currentValue = '';

  ngOnInit() {
    // Simple Observable with loading simulation
    this.options$ = of(userOptions).pipe(
      delay(1000), // Simulate loading delay
      map((options) => {
        this.loading = false;
        return options.slice(0, 8);
      })
    );

    // Start with loading state
    this.loading = true;
  }

  onOptionSelected(option: AvaAutocompleteOption) {
    this.selectedOption = option;
  }

  onValueChange(value: string | string[]) {
    this.currentValue = Array.isArray(value) ? value.join(', ') : value;
  }

  resetDemo() {
    this.selectedOption = null;
    this.currentValue = '';
    this.loading = true;

    // Recreate the observable to simulate fresh loading
    this.options$ = of(userOptions).pipe(
      delay(1000),
      map((options) => {
        this.loading = false;
        return options.slice(0, 8);
      })
    );
  }
}
