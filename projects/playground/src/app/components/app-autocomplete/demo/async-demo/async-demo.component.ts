import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Observable, of, delay, map } from 'rxjs';
import {
  AvaAutocompleteComponent,
  AvaAutocompleteOption,
} from '../../../../../../../play-comp-library/src/lib/components/autocomplete/ava-autocomplete.component';
import { userOptions } from '../autocomplete-demo.data';

@Component({
  selector: 'ava-autocomplete-async-demo',
  standalone: true,
  imports: [CommonModule, AvaAutocompleteComponent],
  template: `
    <div class="demo-section">
      <div class="demo-container">
        <ava-autocomplete
          [options]="options$"
          placeholder="Search for users..."
          label="Search users"
          helper="Simple Observable-based autocomplete demo"
          startIcon="search"
          startIconColor="#6b7280"
          [minLength]="1"
          (optionSelected)="onOptionSelected($event)"
          (valueChange)="onValueChange($event)"
        ></ava-autocomplete>
      </div>
      <!-- 
      <div class="demo-output" *ngIf="selectedOption || currentValue">
        <h4>Output:</h4>
        <div class="output-item" *ngIf="selectedOption">
          <strong>Selected Option:</strong> {{ selectedOption.label }} ({{
            selectedOption.value
          }})
        </div>
        <div
          class="output-item"
          *ngIf="selectedOption && selectedOption['email']"
        >
          <strong>Email:</strong> {{ selectedOption['email'] }}
        </div>
        <div class="output-item" *ngIf="currentValue">
          <strong>Current Value:</strong> {{ currentValue }}
        </div>
        <div class="output-item">
          <strong>API Calls:</strong> {{ apiCallCount }} requests made
        </div>
      </div>

      <div class="demo-controls">
        <div class="control-group">
          <span class="control-label">Search Mode:</span>
          <div class="radio-group">
            <label class="radio-item">
              <input
                type="radio"
                [checked]="searchMode === 'api'"
                (change)="setSearchMode('api')"
              />
              <span>API Search (Simulated)</span>
            </label>
            <label class="radio-item">
              <input
                type="radio"
                [checked]="searchMode === 'local'"
                (change)="setSearchMode('local')"
              />
              <span>Local Filter</span>
            </label>
          </div>
        </div>

        <button class="reset-btn" (click)="resetDemo()">Reset Demo</button>
      </div>

      <div class="features-list">
        <h4>Async Features:</h4>
        <ul>
          <li>
            <strong>Observable Support:</strong> Simple Observable integration
            like the main example
          </li>
          <li>
            <strong>Mode Switching:</strong> Toggle between API and local data
            sources
          </li>
          <li>
            <strong>Delayed Loading:</strong> Simulates API delay for realistic
            testing
          </li>
          <li>
            <strong>Built-in Filtering:</strong> Component handles all filtering
            internally
          </li>
        </ul>
      </div> -->
    </div>
  `,
  styles: [
    `
      .demo-section {
        max-width: 700px;
        margin: 2rem auto;
        margin-top: 0;
        padding: 2rem;
      }

      .demo-description {
        margin-bottom: 2rem;
        text-align: center;
      }

      .demo-description h3 {
        color: #1f2937;
        margin-bottom: 0.5rem;
        font-size: 1.5rem;
        font-weight: 600;
      }

      .demo-description p {
        color: #6b7280;
        font-size: 0.95rem;
        line-height: 1.5;
      }

      .demo-container {
        margin-bottom: 2rem;
      }

      .demo-output {
        margin-bottom: 2rem;
      }

      .demo-output h4 {
        color: #374151;
        margin-bottom: 1rem;
        font-size: 1.1rem;
        font-weight: 600;
      }

      .output-item {
        margin-bottom: 1rem;
        padding: 0.75rem;
      }

      .output-item strong {
        color: #374151;
        margin-right: 0.5rem;
      }

      .demo-controls {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
        margin-bottom: 2rem;
        padding: 1.5rem;
      }

      .control-group {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
      }

      .control-label {
        font-weight: 600;
        color: #374151;
        font-size: 0.95rem;
      }

      .radio-group {
        display: flex;
        gap: 1.5rem;
      }

      .radio-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        cursor: pointer;
        font-size: 0.9rem;
        color: #4b5563;
      }

      .radio-item input[type='radio'] {
        margin: 0;
      }

      .reset-btn {
        align-self: center;
        background: #3b82f6;
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 6px;
        font-size: 0.95rem;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s;
      }

      .reset-btn:hover:not(:disabled) {
        background: #2563eb;
      }

      .reset-btn:disabled {
        background: #9ca3af;
        cursor: not-allowed;
      }

      .features-list {
        background: #f0f9ff;
        border: 1px solid #bae6fd;
        border-radius: 8px;
        padding: 1.5rem;
      }

      .features-list h4 {
        color: #0369a1;
        margin-bottom: 1rem;
        font-size: 1.1rem;
        font-weight: 600;
      }

      .features-list ul {
        margin: 0;
        padding-left: 1.5rem;
      }

      .features-list li {
        margin-bottom: 0.5rem;
        color: #0c4a6e;
        line-height: 1.5;
      }

      .features-list li strong {
        color: #0369a1;
      }

      @media (max-width: 768px) {
        .radio-group {
          flex-direction: column;
          gap: 0.75rem;
        }
      }

      :host {
        display: block;
        min-height: 100vh;
        padding: 1rem;
      }
    `,
  ],
})
export class AppAutocompleteAsyncDemoComponent implements OnInit {
  // Simple approach like the working example
  apiOptions$: Observable<AvaAutocompleteOption[]> = of([]);
  localOptions$: Observable<AvaAutocompleteOption[]> = of([]);

  selectedOption: AvaAutocompleteOption | null = null;
  currentValue = '';
  apiCallCount = 0;
  searchMode: 'api' | 'local' = 'api';

  ngOnInit() {
    // API options - simulate API call
    this.apiOptions$ = of([]).pipe(
      delay(500),
      map(() => userOptions.slice(0, 8))
    );

    // Local options - immediate
    this.localOptions$ = of(userOptions);
  }

  get options$(): Observable<AvaAutocompleteOption[]> {
    return this.searchMode === 'api' ? this.apiOptions$ : this.localOptions$;
  }

  onOptionSelected(option: AvaAutocompleteOption) {
    this.selectedOption = option;
  }

  onValueChange(value: string | string[]) {
    this.currentValue = Array.isArray(value) ? value.join(', ') : value;
  }

  setSearchMode(mode: 'api' | 'local') {
    this.searchMode = mode;
  }

  resetDemo() {
    this.selectedOption = null;
    this.currentValue = '';
    this.apiCallCount = 0;
  }
}
