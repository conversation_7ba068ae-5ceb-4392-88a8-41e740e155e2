import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  AvaAutocompleteComponent,
  AvaAutocompleteOption,
} from '../../../../../../../play-comp-library/src/lib/components/autocomplete/ava-autocomplete.component';
import { programmingLanguages } from '../autocomplete-demo.data';
import { IconComponent } from '../../../../../../../play-comp-library/src/lib/components/icon/icon.component';

@Component({
  selector: 'ava-autocomplete-templates-demo',
  standalone: true,
  imports: [CommonModule, AvaAutocompleteComponent, IconComponent],
  template: `
    <div class="demo-section">
      <div class="demo-description">
        <h3>Custom Templates</h3>
        <p>
          Create custom option templates for complex data display and enhanced
          user experience.
        </p>
      </div>

      <div class="demo-container">
        <ava-autocomplete
          [options]="options"
          placeholder="Search for programming languages..."
          label="Select a language"
          helper="Choose a programming language with detailed information"
          [optionTemplate]="customTemplate"
          (optionSelected)="onOptionSelected($event)"
          (valueChange)="onValueChange($event)"
        ></ava-autocomplete>
      </div>

      <!-- <div class="demo-output" *ngIf="selectedOption || currentValue">
        <h4>Output:</h4>
        <div class="output-item" *ngIf="selectedOption">
          <strong>Selected Option:</strong> {{ selectedOption.label }} ({{
            selectedOption.value
          }})
        </div>
        <div class="output-item" *ngIf="currentValue">
          <strong>Current Value:</strong> {{ currentValue }}
        </div>
        <div class="output-item" *ngIf="selectedOption">
          <strong>Description:</strong> {{ selectedOption['description'] }}
        </div>
        <div class="output-item" *ngIf="selectedOption">
          <strong>Popularity:</strong> {{ selectedOption['popularity'] }}%
        </div>
        <div class="output-item" *ngIf="selectedOption">
          <strong>Year Created:</strong> {{ selectedOption['year'] }}
        </div>
      </div>

      <div class="features-list">
        <h4>Template Features:</h4>
        <ul>
          <li>
            <strong>Flexible Layout:</strong> Complete control over option
            appearance
          </li>
          <li>
            <strong>Rich Content:</strong> Support for complex HTML structures
          </li>
          <li>
            <strong>Data Binding:</strong> Access to full option data in
            templates
          </li>
          <li>
            <strong>Responsive Design:</strong> Templates adapt to container
            width
          </li>
          <li>
            <strong>Consistent Styling:</strong> Maintain design system
            consistency
          </li>
        </ul>
      </div> -->
    </div>

    <!-- Custom Template -->
    <ng-template #customTemplate let-option>
      <div class="custom-option">
        <div class="option-header">
          <div class="option-icon">
            <ava-icon [iconName]="option.icon"></ava-icon>
          </div>
          <div class="option-main">
            <div class="option-title">{{ option.label }}</div>
            <div class="option-description">{{ option.description }}</div>
          </div>
          <div class="option-meta">
            <div
              class="popularity-badge"
              [style.background]="getPopularityColor(option.popularity)"
            >
              {{ option.popularity }}%
            </div>
          </div>
        </div>
        <div class="option-footer">
          <span class="year-badge">Since {{ option.year }}</span>
        </div>
      </div>
    </ng-template>
  `,
  styles: [
    `
      .demo-section {
        max-width: 800px;
        margin: 2rem auto;
        padding: 2rem;
      }

      .demo-description {
        margin-bottom: 2rem;
        text-align: center;
      }

      .demo-description h3 {
        color: #1f2937;
        margin-bottom: 0.5rem;
        font-size: 1.5rem;
        font-weight: 600;
      }

      .demo-description p {
        color: #6b7280;
        font-size: 0.95rem;
        line-height: 1.5;
      }

      .demo-container {
        margin-bottom: 2rem;
      }

      .demo-output {
        border-radius: 8px;
        padding: 1.5rem;
        margin-bottom: 2rem;
      }

      .demo-output h4 {
        color: #374151;
        margin-bottom: 1rem;
        font-size: 1.1rem;
        font-weight: 600;
      }

      .output-item {
        margin-bottom: 1rem;
        padding: 0.75rem;
        background: #fff;
        border-radius: 6px;
        border-left: 3px solid #3b82f6;
      }

      .output-item strong {
        color: #374151;
        margin-right: 0.5rem;
      }

      .features-list {
        background: #f0f9ff;
        border: 1px solid #bae6fd;
        border-radius: 8px;
        padding: 1.5rem;
      }

      .features-list h4 {
        color: #0369a1;
        margin-bottom: 1rem;
        font-size: 1.1rem;
        font-weight: 600;
      }

      .features-list ul {
        margin: 0;
        padding-left: 1.5rem;
      }

      .features-list li {
        margin-bottom: 0.5rem;
        color: #0c4a6e;
        line-height: 1.5;
      }

      .features-list li strong {
        color: #0369a1;
      }

      /* Custom Option Template Styles */
      .custom-option {
        padding: 0.75rem;
        border-radius: 6px;
        transition: background-color 0.2s;
      }

      .custom-option:hover {
        background-color: #f3f4f6;
      }

      .option-header {
        display: flex;
        align-items: flex-start;
        gap: 0.75rem;
        margin-bottom: 0.5rem;
      }

      .option-icon {
        flex-shrink: 0;
        width: 32px;
        height: 32px;
        background: #e5e7eb;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .option-icon .icon {
        font-size: 1rem;
        color: #6b7280;
      }

      .option-main {
        flex: 1;
        min-width: 0;
      }

      .option-title {
        font-weight: 600;
        color: #1f2937;
        margin-bottom: 0.25rem;
        font-size: 0.95rem;
      }

      .option-description {
        color: #6b7280;
        font-size: 0.85rem;
        line-height: 1.4;
      }

      .option-meta {
        flex-shrink: 0;
      }

      .popularity-badge {
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        color: white;
        text-align: center;
        min-width: 40px;
      }

      .option-footer {
        margin-left: 3.5rem;
      }

      .year-badge {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        background: #f3f4f6;
        color: #6b7280;
        border-radius: 4px;
        font-size: 0.75rem;
        font-weight: 500;
      }

      :host {
        display: block;
        min-height: 100vh;
        padding: 1rem;
      }
    `,
  ],
})
export class AppAutocompleteTemplatesDemoComponent {
  options = programmingLanguages;
  selectedOption: AvaAutocompleteOption | null = null;
  currentValue = '';

  onOptionSelected(option: AvaAutocompleteOption) {
    this.selectedOption = option;
  }

  onValueChange(value: string | string[]) {
    this.currentValue = Array.isArray(value) ? value.join(', ') : value;
  }

  getPopularityColor(popularity: number): string {
    if (popularity >= 90) return '#10b981'; // Green
    if (popularity >= 80) return '#3b82f6'; // Blue
    if (popularity >= 70) return '#f59e0b'; // Yellow
    return '#ef4444'; // Red
  }
}
