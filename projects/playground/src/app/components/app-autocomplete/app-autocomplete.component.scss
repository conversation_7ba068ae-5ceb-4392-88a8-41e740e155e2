/**
 * =========================================================================
 * Play+ Design System: Autocomplete Demo Component SCSS
 *
 * Clean, organized styling for the autocomplete component documentation
 * with consistent design patterns and responsive layout
 * =========================================================================
 */

/* =======================
   AUTCOMPLETE DEMO PAGE STYLING
   ======================= */

.documentation {
  background: #ffffff;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
  line-height: 1.6;
  color: var(--text-primary, #1e293b);

  /* Header Section */
  .doc-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 3rem 0 2rem;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 2rem;

    h1 {
      font-size: 2.5rem;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 1rem;
      background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .description {
      font-size: 1.1rem;
      color: #64748b;
      max-width: 600px;
      line-height: 1.6;
      margin: 0;
    }
  }

  /* Demo Navigation Links */
  .demo-navigation {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    padding: 3rem 0;
    border-bottom: 1px solid #cbd5e1;

    .nav-links {
      text-align: center;

      h3 {
        font-size: 2rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 2rem;
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      .nav-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        max-width: 1200px;
        margin: 0 auto;

        .nav-link {
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 1.5rem;
          background: rgba(255, 255, 255, 0.8);
          border: 2px solid transparent;
          border-radius: 1rem;
          text-decoration: none;
          color: #374151;
          transition: all 0.3s ease;
          backdrop-filter: blur(10px);
          box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

          &:hover {
            transform: translateY(-4px);
            border-color: #6366f1;
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
            color: #6366f1;
          }

          .nav-icon {
            font-size: 2rem;
            margin-bottom: 0.75rem;
            display: block;
          }

          .nav-text {
            font-size: 1rem;
            font-weight: 500;
            text-align: center;
          }
        }
      }
    }
  }

  /* Demo Blocks */
  .demo-block {
    background: #ffffff;
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      transform: translateY(-2px);
    }

    h2 {
      font-size: 1.5rem;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 1.5rem;
      padding-bottom: 0.5rem;
      border-bottom: 2px solid #f1f5f9;
    }

    /* Autocomplete Component Styling */
    ava-autocomplete {
      display: block;
      margin-bottom: 1rem;
    }

    /* Demo Value Display */
    .demo-value {
      background: #f8fafc;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 1rem;
      margin-top: 1rem;
      font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
      font-size: 0.875rem;
      color: #374151;

      strong {
        color: #1e293b;
        font-weight: 600;
      }
    }

    /* Demo Note Styling */
    .demo-note {
      background: #fef3c7;
      border: 1px solid #f59e0b;
      border-radius: 8px;
      padding: 1rem;
      margin-top: 1rem;
      font-size: 0.875rem;
      color: #92400e;

      a {
        color: #d97706;
        text-decoration: none;
        font-weight: 500;

        &:hover {
          text-decoration: underline;
        }
      }
    }
  }

  /* Grid Layout for Multiple Components */
  .row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -1rem;

    .col-12 {
      flex: 0 0 100%;
      max-width: 100%;
      padding: 0 1rem;
    }
  }

  /* Custom Option Template Styling */
  .custom-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 6px;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f1f5f9;
    }
  }

  /* Responsive Design */
  @media (max-width: 768px) {
    .demo-navigation {
      padding: 2rem 0;

      .nav-links {
        h3 {
          font-size: 1.5rem;
        }

        .nav-grid {
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 1rem;

          .nav-link {
            padding: 1rem;

            .nav-icon {
              font-size: 1.5rem;
            }

            .nav-text {
              font-size: 0.875rem;
            }
          }
        }
      }
    }

    .doc-header {
      padding: 2rem 0 1.5rem;

      h1 {
        font-size: 2rem;
      }

      .description {
        font-size: 1rem;
      }
    }

    .demo-block {
      padding: 1.5rem;
      margin-bottom: 1.5rem;

      h2 {
        font-size: 1.25rem;
      }
    }

    .row {
      margin: 0 -0.5rem;

      .col-12 {
        padding: 0 0.5rem;
      }
    }
  }

  @media (max-width: 480px) {
    .doc-header {
      padding: 1.5rem 0 1rem;

      h1 {
        font-size: 1.75rem;
      }
    }

    .demo-block {
      padding: 1rem;
      margin-bottom: 1rem;

      h2 {
        font-size: 1.125rem;
      }
    }
  }

  /* Dark Theme Support */
  html[data-theme="dark"] & {
    background: #0f172a;
    color: #f8fafc;

    .doc-header {
      background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
      border-bottom-color: #475569;

      h1 {
        color: #f8fafc;
      }

      .description {
        color: #cbd5e1;
      }
    }

    .demo-block {
      background: #1e293b;
      border-color: #475569;
      color: #f8fafc;

      h2 {
        color: #f8fafc;
        border-bottom-color: #334155;
      }

      .demo-value {
        background: #334155;
        border-color: #475569;
        color: #cbd5e1;

        strong {
          color: #f8fafc;
        }
      }

      .demo-note {
        background: #451a03;
        border-color: #92400e;
        color: #fbbf24;

        a {
          color: #f59e0b;
        }
      }
    }
  }

  /* Animation Enhancements */
  .demo-block {
    animation: fadeInUp 0.6s ease-out;

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  }

  /* Focus States for Accessibility */
  .demo-block:focus-within {
    outline: 2px solid #6366f1;
    outline-offset: 2px;
  }

  /* Print Styles */
  @media print {
    .demo-block {
      break-inside: avoid;
      box-shadow: none;
      border: 1px solid #000;

      &:hover {
        transform: none;
      }
    }
  }
}
