.nav-bar-demo {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 3rem;

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--color-text-primary);
    margin-bottom: 1rem;
  }

  p {
    font-size: 1.1rem;
    color: var(--color-text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }
}

.demo-section {
  margin-bottom: 4rem;
  border: 1px solid var(--color-border-default);
  border-radius: var(--global-radius-lg);
  padding: 2rem;
  background: var(--color-background-primary);

  h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: 0.5rem;
  }

  p {
    color: var(--color-text-secondary);
    margin-bottom: 2rem;
    font-size: 0.95rem;
  }

  h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--color-text-primary);
    margin: 1.5rem 0 1rem 0;
  }
}

.demo-container {
  background: var(--color-background-secondary);
  border: 1px solid var(--color-border-subtle);
  border-radius: var(--global-radius-md);
  padding: 2rem;
  margin-bottom: 1rem;
  display: flex;
  justify-content: center;
  overflow-x: auto;
  height: 500px;
}

.current-route {
  background: var(--color-background-tertiary);
  border: 1px solid var(--color-border-default);
  border-radius: var(--global-radius-sm);
  padding: 1rem;
  margin-top: 1rem;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 0.9rem;
  color: var(--color-text-primary);
}

.route-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  margin: 2rem 0;
}

// Custom styling for active state on demo buttons
ava-button.active {
  // The ava-button component will handle its own hover/focus states
  // We can add custom active state styling here if needed
  opacity: 0.8;
}

// Demo links styling
.demo-links {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  margin: 1.5rem 0;
}

.code-example {
  background: var(--color-background-secondary);
  border: 1px solid var(--color-border-default);
  border-radius: var(--global-radius-md);
  padding: 1.5rem;
  margin: 1rem 0;

  pre {
    margin: 0;
    overflow-x: auto;

    code {
      font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
      font-size: 0.85rem;
      line-height: 1.6;
      color: var(--color-text-primary);
      white-space: pre;
    }
  }
}

.features-list {
  list-style: none;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;

  li {
    background: var(--color-background-secondary);
    padding: 1rem;
    border-radius: var(--global-radius-sm);
    border-left: 4px solid var(--color-surface-primary);
    font-size: 0.9rem;
    color: var(--color-text-primary);
    transition: transform 0.2s ease;

    &:hover {
      transform: translateX(4px);
    }
  }
}

// Component Composition Diagram
.composition-diagram {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
  padding: 2rem;
  background: var(--color-background-secondary);
  border-radius: var(--global-radius-md);
  border: 1px solid var(--color-border-default);
}

.component-box {
  padding: 1.5rem;
  border-radius: var(--global-radius-md);
  text-align: center;
  min-width: 150px;
  border: 2px solid;
  transition: transform 0.2s ease;

  &:hover {
    transform: scale(1.05);
  }

  h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 600;
  }

  p {
    margin: 0;
    font-size: 0.8rem;
    opacity: 0.8;
  }

  &.composite {
    background: var(--color-surface-primary);
    color: var(--color-text-on-brand);
    border-color: var(--color-border-primary);
  }

  &.atomic {
    background: var(--color-surface-secondary);
    color: var(--color-text-primary);
    border-color: var(--color-border-secondary);
  }
}

.atomic-components {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 1rem;
  width: 100%;
  max-width: 600px;
}

.uses-arrow {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--color-text-secondary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

// Responsive design
@media (max-width: 768px) {
  .nav-bar-demo {
    padding: 1rem;
  }

  .demo-header h1 {
    font-size: 2rem;
  }

  .demo-section {
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .demo-container {
    padding: 1rem;
  }

  .features-list {
    grid-template-columns: 1fr;
  }

  .route-buttons {
    flex-direction: column;
    align-items: center;
  }

  .atomic-components {
    grid-template-columns: repeat(2, 1fr);
  }
}
