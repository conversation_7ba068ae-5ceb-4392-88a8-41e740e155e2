.horizontal-timeline-demo{
    margin-bottom: 100px;
}

.horizontal-heading{
    padding:50px;
}



// Order tracking card styles
.order-card-content {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 12px;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

  }

  .card-time {
    font-size: 14px;
    color: #666;
    margin-bottom: 12px;
    font-weight: 500;
  }

  .card-description {
    font-size: 14px;
    line-height: 1.5;
    color: #555;
    margin-bottom: 12px;
  }
}

// Simple card content styles
.simple-card-content {
  .card-image-container {
    margin-bottom: 12px;

    .card-image {
      width: 100%;
      height: 150px;
      object-fit: cover;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.02);
      }
    }
  }

  h4 {
    margin: 0 0 8px 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
  }

  p {
    margin: 0 0 8px 0;
    font-size: 14px;
    line-height: 1.5;
    color: #555;
  }

  small {
    font-size: 12px;
    color: #666;
    font-weight: 500;
    display: block;
    margin-bottom: 8px;
  }

  .event-number {
    background-color: #f0f0f0;
    color: #333;
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 500;
    display: inline-block;
  }
}

