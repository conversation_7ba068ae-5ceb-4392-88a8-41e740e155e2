.demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.demo-section {
  margin-bottom: 3rem;

  h3 {
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
  }

  p {
    color: #555;
    line-height: 1.6;
    margin-bottom: 1.5rem;
  }
}

.disabled-examples,
.use-cases {
  display: grid;
  gap: 2rem;
  margin-bottom: 2rem;
}

.example-item,
.use-case {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }

  h4 {
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  p {
    color: #666;
    margin-bottom: 1rem;
    font-size: 0.95rem;
  }
}

.dropdown-demo {
  margin: 1rem 0;
  padding: 1rem;
  background: white;
  border-radius: 6px;
  border: 1px solid #dee2e6;

  ava-dropdown {
    display: block;
    width: 100%;
    max-width: 300px;
  }
}

.disabled-info {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  padding: 0.75rem;
  margin-top: 1rem;
  font-size: 0.9rem;

  strong {
    color: #856404;
  }
}

.guidelines {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.guideline {
  background: #f8f9fa;
  border-left: 4px solid #3498db;
  padding: 1.5rem;
  border-radius: 0 6px 6px 0;

  h4 {
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      color: #555;
      padding: 0.5rem 0;
      border-bottom: 1px solid #e9ecef;
      position: relative;
      padding-left: 1.5rem;

      &:before {
        content: "•";
        color: #3498db;
        font-weight: bold;
        position: absolute;
        left: 0;
      }

      &:last-child {
        border-bottom: none;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .demo-container {
    padding: 1rem;
  }

  .guidelines {
    grid-template-columns: 1fr;
  }

  .dropdown-demo ava-dropdown {
    max-width: 100%;
  }
}

// Accessibility improvements
.demo-container:focus-within {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}

.example-item:focus-within,
.use-case:focus-within {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}
