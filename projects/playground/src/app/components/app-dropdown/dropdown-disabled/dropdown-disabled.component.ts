import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  DropdownComponent,
  DropdownOption,
} from '../../../../../../play-comp-library/src/public-api';

interface DisabledExample {
  title: string;
  description: string;
  options: DropdownOption[];
  suboptions?: Record<string, DropdownOption[]>;
  config: {
    dropdownTitle: string;
    disabled?: boolean;
    search?: boolean;
  };
}

interface SelectionEvent {
  selectedValue: string;
  selectedOption?: DropdownOption;
  subOption?: DropdownOption;
}

@Component({
  selector: 'ava-dropdown-disabled',
  standalone: true,
  imports: [CommonModule, DropdownComponent],
  templateUrl: './dropdown-disabled.component.html',
  styleUrls: ['./dropdown-disabled.component.scss'],
})
export class DropdownDisabledComponent {
  disabledExamples: DisabledExample[] = [
    {
      title: 'Disabled Dropdown',
      description: 'Entire dropdown component is disabled and non-interactive.',
      options: [
        { name: 'Option 1', value: '1' },
        { name: 'Option 2', value: '2' },
        { name: 'Option 3', value: '3' },
      ],
      config: {
        dropdownTitle: 'Disabled Dropdown',
        disabled: true,
      },
    },
    {
      title: 'Disabled Options',
      description:
        'Individual options are disabled while others remain selectable.',
      options: [
        { name: 'Available Option', value: 'available' },
        { name: 'Disabled Option', value: 'disabled', disabled: true },
        { name: 'Another Available', value: 'available2' },
        { name: 'Also Disabled', value: 'disabled2', disabled: true },
      ],
      config: {
        dropdownTitle: 'Mixed Options',
      },
    },
    {
      title: 'Disabled with Sub-options',
      description: 'Dropdown with disabled options and sub-options.',
      options: [
        { name: 'Categories', value: 'categories' },
        { name: 'Services', value: 'services' },
      ],
      suboptions: {
        Categories: [
          { name: 'Electronics', value: 'electronics' },
          { name: 'Clothing (Disabled)', value: 'clothing', disabled: true },
          { name: 'Books', value: 'books' },
        ],
        Services: [
          { name: 'Support', value: 'support' },
          { name: 'Premium (Disabled)', value: 'premium', disabled: true },
          { name: 'Basic', value: 'basic' },
        ],
      },
      config: {
        dropdownTitle: 'Categories with Disabled Items',
      },
    },
    {
      title: 'Disabled Multi-Select',
      description: 'Multi-select dropdown with some disabled options.',
      options: [
        { name: 'Feature A', value: 'A' },
        { name: 'Feature B (Disabled)', value: 'B', disabled: true },
        { name: 'Feature C', value: 'C' },
        { name: 'Feature D (Disabled)', value: 'D', disabled: true },
      ],
      config: {
        dropdownTitle: 'Disabled Checkboxes',
      },
    },
  ];

  disabledUseCases = [
    {
      title: 'Conditional Access',
      description: 'Dropdown disabled based on user permissions or conditions.',
      options: [
        { name: 'Admin Settings', value: 'admin' },
        { name: 'User Settings', value: 'user' },
        { name: 'System Settings', value: 'system' },
      ],
    },
    {
      title: 'Feature Availability',
      description: 'Options disabled when features are not available.',
      options: [
        { name: 'Basic Plan', value: 'basic' },
        {
          name: 'Premium Plan (Unavailable)',
          value: 'premium',
          disabled: true,
        },
        {
          name: 'Enterprise Plan (Unavailable)',
          value: 'enterprise',
          disabled: true,
        },
      ],
    },
  ];

  onSelectionChange(event: SelectionEvent, exampleTitle: string) {
    console.log(`${exampleTitle} selection changed:`, event);
    alert(`Demo: ${exampleTitle} - Selected: ${event.selectedValue}`);
  }

  getDisabledGuidelines() {
    return [
      'Use disabled state for unavailable options',
      'Provide clear visual indication of disabled state',
      'Include tooltips explaining why options are disabled',
      'Maintain accessibility with proper ARIA attributes',
      'Consider alternative actions for disabled items',
    ];
  }
}
