<div class="demo-container">
  <div class="demo-section">
    <h3>Disabled State Examples</h3>
    <p>
      Demonstrates how to disable dropdowns and individual options for various
      use cases like conditional access, feature availability, and user
      permissions.
    </p>

    <div class="disabled-examples">
      <div class="example-item" *ngFor="let example of disabledExamples">
        <h4>{{ example.title }}</h4>
        <p>{{ example.description }}</p>
        <div class="dropdown-demo">
          <ava-dropdown
            [dropdownTitle]="example.config.dropdownTitle"
            [options]="example.options"
            [suboptions]="example.suboptions ?? {}"
            [disabled]="example.config.disabled ?? false"
            [search]="example.config.search ?? false"
            (selectionChange)="onSelectionChange($event, example.title)"
            tabindex="0"
            role="button"
            [attr.aria-label]="example.title + ' dropdown'"
          >
          </ava-dropdown>
        </div>
        <div class="disabled-info">
          <strong>Disabled:</strong>
          {{
            example.config.disabled ? "Entire dropdown" : "Individual options"
          }}
        </div>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h3>Real-World Disabled Use Cases</h3>
    <p>
      Practical examples of when and how to use disabled states in dropdowns.
    </p>

    <div class="use-cases">
      <div class="use-case" *ngFor="let useCase of disabledUseCases">
        <h4>{{ useCase.title }}</h4>
        <p>{{ useCase.description }}</p>
        <div class="dropdown-demo">
          <ava-dropdown
            [dropdownTitle]="useCase.title"
            [options]="useCase.options"
            (selectionChange)="onSelectionChange($event, useCase.title)"
            tabindex="0"
            role="button"
            [attr.aria-label]="useCase.title + ' dropdown'"
          >
          </ava-dropdown>
        </div>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h3>Implementation Guidelines</h3>
    <p>Best practices for implementing disabled states in dropdowns.</p>

    <div class="guidelines">
      <div class="guideline">
        <h4>✅ When to Use Disabled States</h4>
        <ul>
          <li>User permission restrictions</li>
          <li>Feature availability limitations</li>
          <li>Form validation requirements</li>
          <li>Workflow state dependencies</li>
          <li>Configuration constraints</li>
        </ul>
      </div>

      <div class="guideline">
        <h4>⚠️ Accessibility Considerations</h4>
        <ul>
          <li>Provide clear visual indication of disabled state</li>
          <li>Include tooltips explaining why items are disabled</li>
          <li>Ensure proper ARIA attributes for screen readers</li>
          <li>Maintain keyboard navigation where possible</li>
          <li>Consider alternative actions for disabled items</li>
        </ul>
      </div>

      <div class="guideline">
        <h4>🎯 Implementation Tips</h4>
        <ul>
          <li *ngFor="let tip of getDisabledGuidelines()">{{ tip }}</li>
        </ul>
      </div>
    </div>
  </div>
</div>
