<div class="demo-container">
  <div class="demo-section">
    <h3>Icon Examples</h3>
    <p>
      Demonstrates how to use icons in dropdown options and custom toggle icons
      for enhanced visual representation and user experience.
    </p>

    <div class="icon-examples">
      <div class="example-item" *ngFor="let example of iconExamples">
        <h4>{{ example.title }}</h4>
        <p>{{ example.description }}</p>
        <div class="dropdown-demo">
          <ava-dropdown
            [dropdownTitle]="example.config.dropdownTitle"
            [options]="example.options"
            [suboptions]="example.suboptions ?? {}"
            [dropdownIcon]="example.config.dropdownIcon ?? ''"
            [search]="example.config.search ?? false"
            [selectedValue]="example.config.selectedValue ?? ''"
            (selectionChange)="onSelectionChange($event, example.title)"
            tabindex="0"
            role="button"
            [attr.aria-label]="example.title + ' dropdown'"
          >
          </ava-dropdown>
        </div>
        <div class="icon-info">
          <strong>Toggle Icon:</strong>
          {{ example.config.dropdownIcon || "Default (chevron-down)" }}
          <span *ngIf="example.config.search">
            | <strong>Search:</strong> Enabled</span
          >
        </div>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h3>Toggle Icon Variations</h3>
    <p>Different toggle icons for various use cases and contexts.</p>

    <div class="toggle-icons">
      <div class="icon-variation" *ngFor="let variation of toggleIconExamples">
        <h4>{{ variation.title }}</h4>
        <p>{{ variation.description }}</p>
        <div class="dropdown-demo">
          <ava-dropdown
            [dropdownTitle]="variation.title"
            [dropdownIcon]="variation.icon"
            [options]="[
              { name: 'Option 1', value: '1' },
              { name: 'Option 2', value: '2' },
              { name: 'Option 3', value: '3' }
            ]"
            (selectionChange)="onSelectionChange($event, variation.title)"
            tabindex="0"
            role="button"
            [attr.aria-label]="variation.title + ' dropdown'"
          >
          </ava-dropdown>
        </div>
        <div class="icon-info"><strong>Icon:</strong> {{ variation.icon }}</div>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h3>Icon Use Cases</h3>
    <p>
      Practical examples of how icons enhance dropdown usability in different
      scenarios.
    </p>

    <div class="use-cases">
      <div class="use-case" *ngFor="let useCase of iconUseCases">
        <h4>{{ useCase.title }}</h4>
        <p>{{ useCase.description }}</p>
        <div class="dropdown-demo">
          <ava-dropdown
            [dropdownTitle]="useCase.title"
            [options]="useCase.options"
            [search]="true"
            (selectionChange)="onSelectionChange($event, useCase.title)"
            tabindex="0"
            role="button"
            [attr.aria-label]="useCase.title + ' dropdown'"
          >
          </ava-dropdown>
        </div>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h3>Icon Guidelines</h3>
    <p>Best practices for implementing icons in dropdown components.</p>

    <div class="guidelines">
      <div class="guideline">
        <h4>🎨 Icon Design</h4>
        <ul>
          <li *ngFor="let guideline of getIconGuidelines()">{{ guideline }}</li>
        </ul>
      </div>

      <div class="guideline">
        <h4>✅ When to Use Icons</h4>
        <ul>
          <li>Navigation menus and categories</li>
          <li>File types and document formats</li>
          <li>Status indicators and actions</li>
          <li>Settings and configuration options</li>
          <li>User roles and permissions</li>
        </ul>
      </div>

      <div class="guideline">
        <h4>⚠️ Accessibility Considerations</h4>
        <ul>
          <li>Icons should complement text, not replace it</li>
          <li>Ensure sufficient color contrast</li>
          <li>Provide alternative text for screen readers</li>
          <li>Test with different visual impairments</li>
          <li>Consider icon size for touch targets</li>
        </ul>
      </div>
    </div>
  </div>
</div>
