import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  DropdownComponent,
  DropdownOption,
} from '../../../../../../play-comp-library/src/public-api';

interface IconExample {
  title: string;
  description: string;
  options: DropdownOption[];
  suboptions?: Record<string, DropdownOption[]>;
  config: {
    dropdownTitle: string;
    dropdownIcon?: string;
    search?: boolean;
    selectedValue?: string;
  };
}

interface SelectionEvent {
  selectedValue: string;
  selectedOption?: DropdownOption;
  subOption?: DropdownOption;
}

@Component({
  selector: 'ava-dropdown-icons',
  standalone: true,
  imports: [CommonModule, DropdownComponent],
  templateUrl: './dropdown-icons.component.html',
  styleUrls: ['./dropdown-icons.component.scss'],
})
export class DropdownIconsComponent {
  iconExamples: IconExample[] = [
    {
      title: 'Options with Icons',
      description:
        'Dropdown options with Lucide icons for better visual representation.',
      options: [
        { name: 'Network', value: 'network', icon: 'wifi' },
        { name: 'Settings', value: 'settings', icon: 'settings' },
        { name: 'Profile', value: 'profile', icon: 'user' },
        { name: 'Security', value: 'security', icon: 'shield' },
        { name: 'Notifications', value: 'notifications', icon: 'bell' },
      ],
      config: {
        dropdownTitle: 'System Options',
      },
    },
    {
      title: 'Custom Dropdown Icon',
      description:
        'Dropdown with a custom toggle icon instead of the default chevron.',
      options: [
        { name: 'Option 1', value: '1' },
        { name: 'Option 2', value: '2' },
        { name: 'Option 3', value: '3' },
      ],
      config: {
        dropdownTitle: 'Custom Toggle Icon',
        dropdownIcon: 'circle-check',
      },
    },
    {
      title: 'Icons with Search',
      description: 'Dropdown with icons and search functionality enabled.',
      options: [
        { name: 'Home', value: 'home', icon: 'home' },
        { name: 'Search', value: 'search', icon: 'search' },
        { name: 'Mail', value: 'mail', icon: 'mail' },
        { name: 'Phone', value: 'phone', icon: 'phone' },
        { name: 'Lock', value: 'lock', icon: 'lock' },
        { name: 'Star', value: 'star', icon: 'star' },
        { name: 'Heart', value: 'heart', icon: 'heart' },
        { name: 'Bookmark', value: 'bookmark', icon: 'bookmark' },
      ],
      config: {
        dropdownTitle: 'Navigation Options',
        search: true,
      },
    },
    {
      title: 'Different Toggle Icons',
      description: 'Examples of various toggle icons for different contexts.',
      options: [
        { name: 'Option A', value: 'a' },
        { name: 'Option B', value: 'b' },
        { name: 'Option C', value: 'c' },
      ],
      config: {
        dropdownTitle: 'Settings Icon',
        dropdownIcon: 'settings',
      },
    },
  ];

  toggleIconExamples = [
    {
      title: 'Default Chevron',
      description: 'Standard chevron-down icon',
      icon: 'chevron-down',
    },
    {
      title: 'Circle Check',
      description: 'Circle with checkmark for success states',
      icon: 'circle-check',
    },
    {
      title: 'Settings',
      description: 'Settings gear for configuration dropdowns',
      icon: 'settings',
    },
    {
      title: 'Plus',
      description: 'Plus icon for add/create actions',
      icon: 'plus',
    },
    {
      title: 'Filter',
      description: 'Filter icon for filtering dropdowns',
      icon: 'filter',
    },
  ];

  iconUseCases = [
    {
      title: 'Navigation Menu',
      description: 'Icons help users quickly identify navigation options.',
      options: [
        { name: 'Dashboard', value: 'dashboard', icon: 'layout' },
        { name: 'Analytics', value: 'analytics', icon: 'layout' },
        { name: 'Users', value: 'users', icon: 'users' },
        { name: 'Settings', value: 'settings', icon: 'settings' },
      ],
    },
    {
      title: 'File Types',
      description:
        'Icons represent different file types for better recognition.',
      options: [
        { name: 'Document', value: 'doc', icon: 'file-text' },
        { name: 'Image', value: 'img', icon: 'image' },
        { name: 'Video', value: 'video', icon: 'video' },
        { name: 'Audio', value: 'audio', icon: 'music' },
        { name: 'Archive', value: 'archive', icon: 'archive' },
      ],
    },
  ];

  onSelectionChange(event: SelectionEvent, exampleTitle: string) {
    console.log(`${exampleTitle} selection changed:`, event);
    alert(`Demo: ${exampleTitle} - Selected: ${event.selectedValue}`);
  }

  getIconGuidelines() {
    return [
      'Use consistent icon style (Lucide icons recommended)',
      'Choose icons that clearly represent the option',
      'Keep icon names simple and descriptive',
      'Consider accessibility - icons should complement text, not replace it',
      'Use appropriate icon colors for different states',
    ];
  }
}
