.demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.demo-section {
  margin-bottom: 4rem;

  h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary, #1e293b);
    margin-bottom: 1rem;
    border-bottom: 2px solid var(--primary-color, #3b82f6);
    padding-bottom: 0.5rem;
  }

  > p {
    font-size: 1.1rem;
    color: var(--text-secondary, #64748b);
    margin-bottom: 2rem;
    line-height: 1.6;
  }
}

.icon-examples,
.toggle-icons,
.use-cases {
  display: grid;
  gap: 2rem;
  margin-bottom: 3rem;

  .example-item,
  .icon-variation,
  .use-case {
    background: var(--surface, #ffffff);
    border: 1px solid var(--surface-border, #e2e8f0);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    h4 {
      font-size: 1.3rem;
      font-weight: 600;
      color: var(--text-primary, #1e293b);
      margin-bottom: 0.5rem;
    }

    p {
      color: var(--text-secondary, #64748b);
      margin-bottom: 1.5rem;
      line-height: 1.5;
    }

    .dropdown-demo {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      min-height: 60px;
      padding: 1rem;
      background: var(--surface-ground, #f8fafc);
      border-radius: 8px;
      border: 1px dashed var(--surface-border, #e2e8f0);
      margin-bottom: 1rem;
    }

    .icon-info {
      background: var(--primary-color, #3b82f6);
      color: white;
      padding: 0.75rem 1rem;
      border-radius: 6px;
      font-size: 0.9rem;
      display: inline-block;

      strong {
        font-weight: 600;
      }
    }
  }
}

.guidelines {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;

  .guideline {
    background: var(--surface, #ffffff);
    border: 1px solid var(--surface-border, #e2e8f0);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

    h4 {
      font-size: 1.2rem;
      font-weight: 600;
      color: var(--text-primary, #1e293b);
      margin-bottom: 1rem;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        padding: 0.5rem 0;
        color: var(--text-secondary, #64748b);
        line-height: 1.5;
        position: relative;
        padding-left: 1.5rem;

        &::before {
          content: "•";
          position: absolute;
          left: 0;
          color: var(--primary-color, #3b82f6);
          font-weight: bold;
        }
      }
    }
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .demo-container {
    padding: 1rem;
  }

  .icon-examples .example-item,
  .toggle-icons .icon-variation,
  .use-cases .use-case {
    padding: 1.5rem;
  }

  .guidelines {
    grid-template-columns: 1fr;
  }
}

/* Focus and hover states */
.example-item:hover,
.icon-variation:hover,
.use-case:hover,
.guideline:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* Accessibility improvements */
.dropdown-demo ava-dropdown:focus {
  outline: 2px solid var(--primary-color, #3b82f6);
  outline-offset: 2px;
}

/* Icon-specific styling */
.icon-info {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Icon color variations */
.icon-info {
  &.primary {
    background: var(--primary-color, #3b82f6);
  }

  &.success {
    background: var(--success-color, #10b981);
  }

  &.warning {
    background: var(--warning-color, #f59e0b);
  }
}
