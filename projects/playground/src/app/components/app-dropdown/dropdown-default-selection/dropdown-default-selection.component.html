<div class="demo-container">
  <div class="demo-section">
    <h3>Default Selection Examples</h3>
    <p>
      Demonstrates how to set default selected options when dropdowns load,
      improving user experience by pre-selecting common or expected values.
    </p>

    <div class="default-examples">
      <div class="example-item" *ngFor="let example of defaultExamples">
        <h4>{{ example.title }}</h4>
        <p>{{ example.description }}</p>
        <div class="dropdown-demo">
          <ava-dropdown
            [dropdownTitle]="example.config.dropdownTitle"
            [options]="example.options"
            [suboptions]="example.suboptions ?? {}"
            [selectedValue]="example.config.selectedValue"
            [search]="example.config.search ?? false"
            (selectionChange)="onSelectionChange($event, example.title)"
            tabindex="0"
            role="button"
            [attr.aria-label]="example.title + ' dropdown'"
          >
          </ava-dropdown>
        </div>
        <div class="default-info">
          <strong>Default Value:</strong> {{ example.config.selectedValue }}
        </div>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h3>Dynamic Default Selections</h3>
    <p>
      Examples of setting default values based on user preferences, location, or
      application state.
    </p>

    <div class="dynamic-examples">
      <div class="example-item" *ngFor="let example of dynamicDefaults">
        <h4>{{ example.title }}</h4>
        <p>{{ example.description }}</p>
        <div class="dropdown-demo">
          <ava-dropdown
            [dropdownTitle]="example.title"
            [options]="example.options"
            [selectedValue]="getDefaultValue(example)"
            (selectionChange)="onSelectionChange($event, example.title)"
            tabindex="0"
            role="button"
            [attr.aria-label]="example.title + ' dropdown'"
          >
          </ava-dropdown>
        </div>
        <div class="default-info">
          <strong>Default Value:</strong> {{ getDefaultValue(example) }}
        </div>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h3>Implementation Guidelines</h3>
    <p>Best practices for implementing default selections in dropdowns.</p>

    <div class="guidelines">
      <div class="guideline">
        <h4>✅ When to Use Defaults</h4>
        <ul>
          <li>User preferences or settings</li>
          <li>Geographic location-based defaults</li>
          <li>Most commonly selected options</li>
          <li>Form pre-population from saved data</li>
          <li>Configuration wizards with sensible defaults</li>
        </ul>
      </div>

      <div class="guideline">
        <h4>⚠️ Considerations</h4>
        <ul>
          <li>Ensure defaults are truly relevant to the user</li>
          <li>Allow users to easily change defaults</li>
          <li>Don't pre-select options that require user decision</li>
          <li>
            Consider accessibility - screen readers should announce defaults
          </li>
          <li>Test with different user contexts and preferences</li>
        </ul>
      </div>

      <div class="guideline">
        <h4>🎯 Implementation Tips</h4>
        <ul>
          <li>Use <code>selectedValue</code> property to set defaults</li>
          <li>Match the value with an option's <code>name</code> property</li>
          <li>Consider dynamic defaults based on user data</li>
          <li>Provide clear visual indication of default selections</li>
          <li>Allow users to reset to defaults if needed</li>
        </ul>
      </div>
    </div>
  </div>
</div>
