import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  DropdownComponent,
  DropdownOption,
} from '../../../../../../play-comp-library/src/public-api';

interface DefaultSelectionExample {
  title: string;
  description: string;
  options: DropdownOption[];
  suboptions?: Record<string, DropdownOption[]>;
  config: {
    dropdownTitle: string;
    selectedValue: string;
    search?: boolean;
  };
}

interface SelectionEvent {
  selectedValue: string;
  selectedOption?: DropdownOption;
  subOption?: DropdownOption;
}

@Component({
  selector: 'ava-dropdown-default-selection',
  standalone: true,
  imports: [CommonModule, DropdownComponent],
  templateUrl: './dropdown-default-selection.component.html',
  styleUrls: ['./dropdown-default-selection.component.scss'],
})
export class DropdownDefaultSelectionComponent {
  defaultExamples: DefaultSelectionExample[] = [
    {
      title: 'Simple Default Selection',
      description: 'Dropdown with a single default option selected.',
      options: [
        { name: 'Option 1', value: '1' },
        { name: 'Option 2', value: '2' },
        { name: 'Option 3', value: '3' },
        { name: 'Option 4', value: '4' },
        { name: 'Option 5', value: '5' },
      ],
      config: {
        dropdownTitle: 'Select an option',
        selectedValue: 'Option 3',
      },
    },
    {
      title: 'Default with Sub-options',
      description: 'Dropdown with default selection that has sub-options.',
      options: [
        { name: 'Electronics', value: 'electronics' },
        { name: 'Clothing', value: 'clothing' },
        { name: 'Books', value: 'books' },
      ],
      suboptions: {
        Electronics: [
          { name: 'Phones', value: 'phones' },
          { name: 'Laptops', value: 'laptops' },
          { name: 'Tablets', value: 'tablets' },
        ],
        Clothing: [
          { name: 'Shirts', value: 'shirts' },
          { name: 'Pants', value: 'pants' },
          { name: 'Shoes', value: 'shoes' },
        ],
        Books: [
          { name: 'Fiction', value: 'fiction' },
          { name: 'Non-Fiction', value: 'non-fiction' },
          { name: 'Science Fiction', value: 'sci-fi' },
        ],
      },
      config: {
        dropdownTitle: 'Select Category',
        selectedValue: 'Electronics',
      },
    },
    {
      title: 'Default with Search',
      description:
        'Dropdown with default selection and search functionality enabled.',
      options: [
        { name: 'Apple', value: 'apple' },
        { name: 'Banana', value: 'banana' },
        { name: 'Orange', value: 'orange' },
        { name: 'Grape', value: 'grape' },
        { name: 'Mango', value: 'mango' },
        { name: 'Pineapple', value: 'pineapple' },
        { name: 'Strawberry', value: 'strawberry' },
        { name: 'Blueberry', value: 'blueberry' },
      ],
      config: {
        dropdownTitle: 'Search fruits',
        selectedValue: 'Mango',
        search: true,
      },
    },
    {
      title: 'User Preferences Default',
      description:
        'Dropdown with default selection based on user preferences or settings.',
      options: [
        { name: 'Light Theme', value: 'light' },
        { name: 'Dark Theme', value: 'dark' },
        { name: 'Auto Theme', value: 'auto' },
        { name: 'High Contrast', value: 'high-contrast' },
      ],
      config: {
        dropdownTitle: 'Theme Selection',
        selectedValue: 'Dark Theme',
      },
    },
  ];

  dynamicDefaults = [
    {
      title: 'Country Selection',
      description: 'Default country based on user location or preferences.',
      options: [
        { name: 'United States', value: 'us' },
        { name: 'Canada', value: 'ca' },
        { name: 'United Kingdom', value: 'uk' },
        { name: 'Australia', value: 'au' },
        { name: 'Germany', value: 'de' },
        { name: 'France', value: 'fr' },
        { name: 'Japan', value: 'jp' },
        { name: 'India', value: 'in' },
      ],
      defaultCountry: 'United States',
    },
    {
      title: 'Language Selection',
      description:
        'Default language based on browser settings or user preference.',
      options: [
        { name: 'English', value: 'en' },
        { name: 'Spanish', value: 'es' },
        { name: 'French', value: 'fr' },
        { name: 'German', value: 'de' },
        { name: 'Chinese', value: 'zh' },
        { name: 'Japanese', value: 'ja' },
      ],
      defaultLanguage: 'English',
    },
  ];

  onSelectionChange(event: SelectionEvent, exampleTitle: string) {
    console.log(`${exampleTitle} selection changed:`, event);
    // Show a demo message
    alert(`Demo: ${exampleTitle} - Selected: ${event.selectedValue}`);
  }

  getDefaultValue(example: {
    defaultCountry?: string;
    defaultLanguage?: string;
  }): string {
    return example.defaultCountry || example.defaultLanguage || '';
  }
}
