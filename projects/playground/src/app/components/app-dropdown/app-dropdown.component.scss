// CSS Custom Properties for theming
:host {
  --primary-color: #3498db;
  --text-color-primary: #2c3e50;
  --text-color-secondary: #64748b;
  --surface-card: #ffffff;
  --surface-ground: #f8f9fa;
  --surface-border: #e9ecef;
  --border-radius: 8px;
  --border-radius-sm: 4px;
  --font-font-weight-medium: 500;
  --font-font-family-heading: inherit;
}

// Prevent horizontal overflow
body,
html {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}
.dropdown {
  box-shadow: 0px 2px 2px 0px #0000001f;
  // margin-bottom: 1rem;
}

// Dropdown component styling to match design
ava-dropdown {
  display: inline-block;
  margin: 0.5rem;
}
:host {
  display: block;
  width: 100%;
}

/* Prevent horizontal scroll */
html,
body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
}

* {
  box-sizing: border-box;
}

/* Main layout */
.documentation {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
  overflow-x: hidden;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

/* Demo Navigation */
.demo-navigation {
  margin: 2rem 0;
  padding: 1.5rem;
  background: var(--surface-card, #ffffff);
  border-radius: var(--border-radius, 8px);
  border: 1px solid var(--surface-border, #e9ecef);

  h3 {
    color: var(--text-color-primary, #2c3e50);
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
    border-bottom: 2px solid var(--primary-color, #3498db);
    padding-bottom: 0.5rem;
  }

  .nav-links {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;

    .nav-link {
      display: inline-block;
      padding: 0.75rem 1rem;
      background: var(--surface-ground, #f8f9fa);
      color: var(--text-color-primary, #2c3e50);
      text-decoration: none;
      border-radius: var(--border-radius-sm, 4px);
      border: 1px solid var(--surface-border, #e9ecef);
      transition: all 0.3s ease;
      font-weight: 500;

      &:hover {
        background: var(--primary-color, #3498db);
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
      }

      &:focus {
        outline: 2px solid var(--primary-color, #3498db);
        outline-offset: 2px;
      }
    }
  }

  @media (max-width: 768px) {
    .nav-links {
      grid-template-columns: 1fr;
    }
  }
}

/* Demo Content */
.demo-content {
  margin-top: 2rem;
  min-height: 400px;
}

/* Header */
.doc-header {
  h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
  }

  .description {
    font-size: 1.1rem;
    line-height: 1.6;
  }
}

/* Sections */
.doc-sections {
  margin-top: 4rem;
}

.doc-section {
  margin-bottom: 1rem;

  h2 {
    font-size: 1.8rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
  }

  p {
    font-size: 0.95rem;
    line-height: 1.5;
  }
}

/* Section header with toggle */
.section-header {
  display: flex;
  flex-direction: column;
  position: relative;
  cursor: pointer;
  padding: 1rem;
  background-color: var(--surface);
  border-radius: var(--border-radius);

  h2 {
    margin-bottom: 0.5rem;
  }

  .description-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .code-toggle {
    font-size: 0.75rem;
    color: var(--icons-action, #3498db);
    cursor: pointer;
    display: flex;
    align-items: center;
    font-weight: var(--font-font-weight-medium, 500);
    font-family: var(--font-font-family-heading, inherit);
    background: transparent;
    border: 1px solid var(--surface-border, #e9ecef);
    border-radius: var(--border-radius-sm, 4px);
    padding: 0.5rem 0.75rem;
    transition: all 0.3s ease;

    &:hover {
      background: var(--surface-ground, #f8f9fa);
      border-color: var(--primary-color, #3498db);
      color: var(--primary-color, #3498db);
    }

    &:focus {
      outline: 2px solid var(--primary-color, #3498db);
      outline-offset: 2px;
    }

    span {
      margin-right: 0.5rem;
    }

    awe-icons {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      line-height: 0;
      padding: 0;
      margin: 0;
      vertical-align: middle;
      flex-shrink: 0;

      svg {
        width: 60%;
        height: 80%;
        display: block;
      }
    }
  }
}

/* Code example styles */
.code-example {
  margin-top: 1.5rem;

  .example-preview {
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    border: 1px solid var(--surface-border);
  }

  .code-block {
    position: relative;
    border-radius: 0.5rem;
    margin-top: 1rem;
    border: 1px solid var(--surface-border);
    background-color: var(--surface-ground);

    pre {
      margin: 0;
      padding: 1rem;
      border-radius: 0.25rem;
      overflow-x: auto;
    }

    .copy-button {
      position: absolute;
      top: 0.5rem;
      right: 0.5rem;
      padding: 0.5rem;
      background: transparent;
      border: none;
      cursor: pointer;
      color: var(--text-color-secondary);

      &:hover {
        color: var(--primary-color);
      }
    }
  }
}

/* API table styles */
.api-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;

  th,
  td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--surface-border);
  }

  th {
    background-color: var(--surface);
    font-weight: 600;
    color: var(--text-color-primary);
  }

  td {
    code {
      background-color: var(--surface);
      padding: 0.2rem 0.4rem;
      border-radius: var(--border-radius-sm);
      font-family: monospace;
    }
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .documentation {
    padding: 1rem;
  }
}
