import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  DropdownComponent,
  DropdownOption,
} from '../../../../../../play-comp-library/src/public-api';

interface SingleSelectExample {
  title: string;
  description: string;
  options: DropdownOption[];
  checkboxOptions: string[];
  config: {
    dropdownTitle: string;
    search?: boolean;
    selectedValue?: string;
  };
}

interface SelectionEvent {
  selectedValue: string;
  selectedOption?: DropdownOption;
  subOption?: DropdownOption;
}

@Component({
  selector: 'ava-dropdown-single-select',
  standalone: true,
  imports: [CommonModule, DropdownComponent],
  templateUrl: './dropdown-single-select.component.html',
  styleUrls: ['./dropdown-single-select.component.scss'],
})
export class DropdownSingleSelectComponent {
  singleSelectExamples: SingleSelectExample[] = [
    {
      title: 'Basic Single Select',
      description:
        'Dropdown with checkboxes but only single selection allowed (radio-style behavior).',
      options: [
        { name: 'Option 1', value: '1' },
        { name: 'Option 2', value: '2' },
        { name: 'Option 3', value: '3' },
        { name: 'Option 4', value: '4' },
      ],
      checkboxOptions: ['Option 1', 'Option 2', 'Option 3', 'Option 4'],
      config: {
        dropdownTitle: 'Select One Option',
      },
    },
    {
      title: 'Single Select with Search',
      description: 'Single-select dropdown with search functionality.',
      options: [
        { name: 'Red', value: 'red' },
        { name: 'Green', value: 'green' },
        { name: 'Blue', value: 'blue' },
        { name: 'Yellow', value: 'yellow' },
        { name: 'Purple', value: 'purple' },
        { name: 'Orange', value: 'orange' },
        { name: 'Pink', value: 'pink' },
        { name: 'Brown', value: 'brown' },
      ],
      checkboxOptions: [
        'Red',
        'Green',
        'Blue',
        'Yellow',
        'Purple',
        'Orange',
        'Pink',
        'Brown',
      ],
      config: {
        dropdownTitle: 'Select Color',
        search: true,
      },
    },
    {
      title: 'Single Select with Default',
      description: 'Single-select dropdown with a pre-selected option.',
      options: [
        { name: 'Small', value: 'small' },
        { name: 'Medium', value: 'medium' },
        { name: 'Large', value: 'large' },
        { name: 'Extra Large', value: 'xl' },
      ],
      checkboxOptions: ['Small', 'Medium', 'Large', 'Extra Large'],
      config: {
        dropdownTitle: 'Select Size',
        selectedValue: 'Medium',
      },
    },
  ];

  singleSelectUseCases = [
    {
      title: 'Theme Selection',
      description: 'Choose a single theme for the application.',
      options: [
        { name: 'Light Theme', value: 'light' },
        { name: 'Dark Theme', value: 'dark' },
        { name: 'Auto Theme', value: 'auto' },
        { name: 'High Contrast', value: 'high-contrast' },
      ],
      checkboxOptions: [
        'Light Theme',
        'Dark Theme',
        'Auto Theme',
        'High Contrast',
      ],
    },
    {
      title: 'Language Selection',
      description: 'Select a single language for the interface.',
      options: [
        { name: 'English', value: 'en' },
        { name: 'Spanish', value: 'es' },
        { name: 'French', value: 'fr' },
        { name: 'German', value: 'de' },
        { name: 'Chinese', value: 'zh' },
        { name: 'Japanese', value: 'ja' },
      ],
      checkboxOptions: [
        'English',
        'Spanish',
        'French',
        'German',
        'Chinese',
        'Japanese',
      ],
    },
  ];

  onSelectionChange(event: SelectionEvent, exampleTitle: string) {
    console.log(`${exampleTitle} selection changed:`, event);
  }

  getSingleSelectTips() {
    return [
      'Use singleSelect=true for radio-style behavior',
      'Only one option can be selected at a time',
      'Perfect for mutually exclusive choices',
      'Provides clear visual feedback for selection',
      'Maintains checkbox appearance with radio behavior',
    ];
  }
}
