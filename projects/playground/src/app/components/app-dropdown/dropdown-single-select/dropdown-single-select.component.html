<div class="demo-container">
  <div class="demo-section">
    <h3>Single Select Examples</h3>
    <p>
      Demonstrates dropdown behavior where only one option can be selected at a
      time, similar to radio buttons but with checkbox appearance.
    </p>

    <div class="single-select-examples">
      <div class="example-item" *ngFor="let example of singleSelectExamples">
        <h4>{{ example.title }}</h4>
        <p>{{ example.description }}</p>
        <div class="dropdown-demo">
          <ava-dropdown
            [dropdownTitle]="example.config.dropdownTitle"
            [options]="example.options"
            [singleSelect]="true"
            [search]="example.config.search ?? false"
            [selectedValue]="example.config.selectedValue ?? ''"
            (selectionChange)="onSelectionChange($event, example.title)"
            tabindex="0"
            role="button"
            [attr.aria-label]="example.title + ' dropdown'"
          >
          </ava-dropdown>
        </div>
        <div class="selection-info">
          <strong>Mode:</strong> Single Select
          <span *ngIf="example.config.search">
            | <strong>Search:</strong> Enabled</span
          >
          <span *ngIf="example.config.selectedValue">
            | <strong>Default:</strong> {{ example.config.selectedValue }}</span
          >
        </div>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h3>Single Select Use Cases</h3>
    <p>
      Practical examples of when single-select behavior is most appropriate.
    </p>

    <div class="use-cases">
      <div class="use-case" *ngFor="let useCase of singleSelectUseCases">
        <h4>{{ useCase.title }}</h4>
        <p>{{ useCase.description }}</p>
        <div class="dropdown-demo">
          <ava-dropdown
            [dropdownTitle]="useCase.title"
            [options]="useCase.options"
            [singleSelect]="true"
            (selectionChange)="onSelectionChange($event, useCase.title)"
            tabindex="0"
            role="button"
            [attr.aria-label]="useCase.title + ' dropdown'"
          >
          </ava-dropdown>
        </div>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h3>Implementation Guidelines</h3>
    <p>Best practices for implementing single-select dropdowns.</p>

    <div class="guidelines">
      <div class="guideline">
        <h4>✅ When to Use Single Select</h4>
        <ul>
          <li>Mutually exclusive choices</li>
          <li>Theme or preference selection</li>
          <li>Language or locale selection</li>
          <li>Size or category selection</li>
          <li>Configuration options</li>
        </ul>
      </div>

      <div class="guideline">
        <h4>🎯 Implementation Tips</h4>
        <ul>
          <li *ngFor="let tip of getSingleSelectTips()">{{ tip }}</li>
        </ul>
      </div>

      <div class="guideline">
        <h4>⚠️ Considerations</h4>
        <ul>
          <li>Ensure clear visual feedback for selection</li>
          <li>Provide meaningful default values when appropriate</li>
          <li>Consider search functionality for large option sets</li>
          <li>Maintain accessibility with proper ARIA attributes</li>
          <li>Test keyboard navigation thoroughly</li>
        </ul>
      </div>
    </div>
  </div>
</div>
