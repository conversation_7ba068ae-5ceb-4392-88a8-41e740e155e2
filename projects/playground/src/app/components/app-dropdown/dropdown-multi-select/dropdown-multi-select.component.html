<div class="demo-container">
  <div class="dropdown-demo">
    <ava-dropdown
      [dropdownTitle]="multiSelectExamples[1].config.dropdownTitle"
      [options]="multiSelectExamples[1].options"
      [checkboxOptions]="multiSelectExamples[1].checkboxOptions"
      [search]="multiSelectExamples[1].config.search ?? false"
      (selectionChange)="
        onSelectionChange($event, multiSelectExamples[1].title)
      "
      tabindex="0"
      role="button"
      [attr.aria-label]="multiSelectExamples[1].title + ' dropdown'"
    >
    </ava-dropdown>
  </div>
</div>
