import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  DropdownComponent,
  DropdownOption,
} from '../../../../../../play-comp-library/src/public-api';

interface MultiSelectExample {
  title: string;
  description: string;
  options: DropdownOption[];
  checkboxOptions: string[];
  config: {
    dropdownTitle: string;
    singleSelect?: boolean;
    search?: boolean;
  };
}

interface SelectionEvent {
  selectedValue: string;
  selectedOption?: DropdownOption;
  subOption?: DropdownOption;
}

@Component({
  selector: 'ava-dropdown-multi-select',
  standalone: true,
  imports: [CommonModule, DropdownComponent],
  templateUrl: './dropdown-multi-select.component.html',
  styleUrls: ['./dropdown-multi-select.component.scss'],
})
export class DropdownMultiSelectComponent {
  multiSelectExamples: MultiSelectExample[] = [
    {
      title: 'Basic Multi-Select',
      description: 'Dropdown with checkboxes allowing multiple selections.',
      options: [
        { name: 'Feature A', value: 'A' },
        { name: 'Feature B', value: 'B' },
        { name: 'Feature C', value: 'C' },
        { name: 'Feature D', value: 'D' },
      ],
      checkboxOptions: ['Feature A', 'Feature B', 'Feature C', 'Feature D'],
      config: {
        dropdownTitle: 'Select Features',
      },
    },
    {
      title: 'Multi-Select with Search',
      description:
        'Multi-select dropdown with search functionality to filter options.',
      options: [
        { name: 'Apple', value: 'apple' },
        { name: 'Banana', value: 'banana' },
        { name: 'Orange', value: 'orange' },
        { name: 'Grape', value: 'grape' },
        { name: 'Mango', value: 'mango' },
        { name: 'Pineapple', value: 'pineapple' },
        { name: 'Strawberry', value: 'strawberry' },
        { name: 'Blueberry', value: 'blueberry' },
      ],
      checkboxOptions: [
        'Apple',
        'Banana',
        'Orange',
        'Grape',
        'Mango',
        'Pineapple',
        'Strawberry',
        'Blueberry',
      ],
      config: {
        dropdownTitle: 'Select Fruits',
        search: true,
      },
    },
    {
      title: 'Single Select (Radio Style)',
      description:
        'Dropdown with checkboxes but only single selection allowed.',
      options: [
        { name: 'Option 1', value: '1' },
        { name: 'Option 2', value: '2' },
        { name: 'Option 3', value: '3' },
        { name: 'Option 4', value: '4' },
      ],
      checkboxOptions: ['Option 1', 'Option 2', 'Option 3', 'Option 4'],
      config: {
        dropdownTitle: 'Single Selection',
        singleSelect: true,
      },
    },
    {
      title: 'Large Dataset Multi-Select',
      description: 'Multi-select with many options and search functionality.',
      options: [
        { name: 'United States', value: 'us' },
        { name: 'Canada', value: 'ca' },
        { name: 'United Kingdom', value: 'uk' },
        { name: 'Australia', value: 'au' },
        { name: 'Germany', value: 'de' },
        { name: 'France', value: 'fr' },
        { name: 'Japan', value: 'jp' },
        { name: 'India', value: 'in' },
        { name: 'Brazil', value: 'br' },
        { name: 'Mexico', value: 'mx' },
        { name: 'Italy', value: 'it' },
        { name: 'Spain', value: 'es' },
      ],
      checkboxOptions: [
        'United States',
        'Canada',
        'United Kingdom',
        'Australia',
        'Germany',
        'France',
        'Japan',
        'India',
        'Brazil',
        'Mexico',
        'Italy',
        'Spain',
      ],
      config: {
        dropdownTitle: 'Select Countries',
        search: true,
      },
    },
  ];

  multiSelectUseCases = [
    {
      title: 'Product Features',
      description: 'Select multiple product features or capabilities.',
      options: [
        { name: 'Free Shipping', value: 'free-shipping' },
        { name: '24/7 Support', value: 'support' },
        { name: 'Warranty', value: 'warranty' },
        { name: 'Installation', value: 'installation' },
        { name: 'Training', value: 'training' },
      ],
      checkboxOptions: [
        'Free Shipping',
        '24/7 Support',
        'Warranty',
        'Installation',
        'Training',
      ],
    },
    {
      title: 'User Permissions',
      description: 'Assign multiple permissions to users or roles.',
      options: [
        { name: 'Read', value: 'read' },
        { name: 'Write', value: 'write' },
        { name: 'Delete', value: 'delete' },
        { name: 'Admin', value: 'admin' },
        { name: 'Moderate', value: 'moderate' },
      ],
      checkboxOptions: ['Read', 'Write', 'Delete', 'Admin', 'Moderate'],
    },
  ];

  onSelectionChange(event: SelectionEvent, exampleTitle: string) {
    console.log(`${exampleTitle} selection changed:`, event);
    // alert(`Demo: ${exampleTitle} - Selected: ${event.selectedValue}`);
  }

  getMultiSelectTips() {
    return [
      'Use checkboxes for multiple selections',
      'Enable search for large option lists',
      'Use singleSelect for radio-style behavior',
      'Provide clear visual feedback for selections',
      'Consider keyboard navigation for accessibility',
    ];
  }
}
