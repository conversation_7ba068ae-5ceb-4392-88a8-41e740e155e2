.demo-container {
  max-width: 870px;
  min-height: 60vh;
  margin: 0 auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  // justify-content: center;
  align-items: center;
  gap: 2rem;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.dropdown-demo {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.demo-section {
  margin-bottom: 4rem;

  h3 {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary, #1e293b);
    margin-bottom: 1rem;
  }

  > p {
    font-size: 1.1rem;
    color: var(--text-secondary, #64748b);
    margin-bottom: 2rem;
    line-height: 1.6;
  }
}

.basic-examples {
  display: grid;
  gap: 2rem;
  margin-bottom: 3rem;

  .example-item {
    padding: 2rem;  

    h4 {
      font-size: 1.3rem;
      font-weight: 600;
      color: var(--text-primary, #1e293b);
      margin-bottom: 0.5rem;
    }

    p {
      color: var(--text-secondary, #64748b);
      margin-bottom: 1.5rem;
      line-height: 1.5;
    }

    .dropdown-demo {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      min-height: 60px;
      padding: 1rem;
      border-radius: 8px;
    }
  }
}

.use-cases {
  display: grid;
  gap: 2rem;

  .use-case {
    padding: 2rem;

    h4 {
      font-size: 1.3rem;
      font-weight: 600;
      color: var(--text-primary, #1e293b);
      margin-bottom: 0.5rem;
    }

    p {
      color: var(--text-secondary, #64748b);
      margin-bottom: 1.5rem;
      line-height: 1.5;
    }

    .dropdown-group {
      display: flex;
      flex-wrap: wrap;
      gap: 1rem;
      align-items: flex-start;
    }
  }
}

.implementation-notes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;

  .note {
    padding: 1.5rem;

    h4 {
      font-size: 1.2rem;
      font-weight: 600;
      color: var(--text-primary, #1e293b);
      margin-bottom: 1rem;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        padding: 0.5rem 0;
        color: var(--text-secondary, #64748b);
        line-height: 1.5;
        position: relative;
        padding-left: 1.5rem;

        &::before {
          content: "•";
          position: absolute;
          left: 0;
          color: var(--primary-color, #3b82f6);
          font-weight: bold;
        }
      }
    }
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .demo-container {
    padding: 1rem;
  }

  .basic-examples .example-item,
  .use-cases .use-case {
    padding: 1.5rem;
  }

  .implementation-notes {
    grid-template-columns: 1fr;
  }

  .dropdown-group {
    flex-direction: column;
  }
}

/* Focus and hover states */
.example-item:hover,
.use-case:hover,
.note:hover { 
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

/* Accessibility improvements */
.dropdown-demo ava-dropdown:focus {
  outline: 2px solid var(--primary-color, #3b82f6);
  outline-offset: 2px;
}
