<div class="demo-container">
  <div class="dropdown-demo" *ngFor="let example of basicExamples">
    <ava-dropdown [dropdownTitle]="example.config.dropdownTitle" [options]="example.options"
      [suboptions]="example.suboptions ?? {}" [search]="example.config.search ?? false"
      [selectedValue]="example.config.selectedValue ?? ''" (selectionChange)="onSelectionChange($event, example.title)"
      tabindex="0" role="button" [attr.aria-label]="example.title + ' dropdown'">
    </ava-dropdown>
  </div>

  <!-- <div class="dropdown-demo" *ngFor="let example of basicExamples">
    <ava-dropdown
      [dropdownTitle]="example.config.dropdownTitle"
      [options]="example.options"
      [suboptions]="example.suboptions ?? {}"
      [search]="example.config.search ?? false"
      [selectedValue]="example.config.selectedValue ?? ''"
      (selectionChange)="onSelectionChange($event, example.title)"
      tabindex="0"
      role="button"
      [attr.aria-label]="example.title + ' dropdown'"
    >
      <ava-option>
        <div>Add more +</div>
      </ava-option>
    </ava-dropdown>
  </div> -->
</div>