import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  DropdownComponent,
  DropdownOption,
} from '../../../../../../play-comp-library/src/public-api';

interface BasicExample {
  title: string;
  description: string;
  options: DropdownOption[];
  suboptions?: Record<string, DropdownOption[]>;
  config: {
    dropdownTitle: string;
    search?: boolean;
    selectedValue?: string;
  };
}

interface SelectionEvent {
  selectedValue: string;
  selectedOption?: DropdownOption;
  subOption?: DropdownOption;
}

@Component({
  selector: 'ava-dropdown-basic-usage',
  standalone: true,
  imports: [CommonModule, DropdownComponent],
  templateUrl: './dropdown-basic-usage.component.html',
  styleUrls: ['./dropdown-basic-usage.component.scss'],
})
export class DropdownBasicUsageComponent {
  basicExamples: BasicExample[] = [
    {
      title: 'Simple Dropdown',
      description: 'Basic dropdown with simple options and no sub-options.',
      options: [
        { name: 'Option 1', value: '1' },
        { name: 'Option 2', value: '2' },
        { name: 'Option 3', value: '3' },
        { name: 'Option 4', value: '4' },
        { name: 'Option 5', value: '5' },
      ],
      config: {
        dropdownTitle: 'Select an option',
      },
    },
  ];

  useCases = [
    {
      title: 'Navigation Menus',
      description:
        'Use dropdowns for navigation menus with categories and subcategories.',
      examples: [
        {
          title: 'Main Navigation',
          options: [
            { name: 'Products', value: 'products' },
            { name: 'Services', value: 'services' },
            { name: 'Support', value: 'support' },
          ],
          suboptions: {
            Products: [
              { name: 'Software', value: 'software' },
              { name: 'Hardware', value: 'hardware' },
            ],
            Services: [
              { name: 'Consulting', value: 'consulting' },
              { name: 'Training', value: 'training' },
            ],
          },
        },
      ],
    },
    {
      title: 'Form Selections',
      description: 'Use dropdowns in forms for user input and data collection.',
      examples: [
        {
          title: 'Country Selection',
          options: [
            { name: 'United States', value: 'us' },
            { name: 'Canada', value: 'ca' },
            { name: 'United Kingdom', value: 'uk' },
            { name: 'Australia', value: 'au' },
          ],
        },
      ],
    },
  ];

  onSelectionChange(event: SelectionEvent, exampleTitle: string) {
    console.log(`${exampleTitle} selection changed:`, event);
    // Show a demo message
  }

  onDropdownKeyPress(event: KeyboardEvent, exampleTitle: string) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      console.log(`${exampleTitle} keyboard interaction:`, event.key);
    }
  }
}
