.demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
}

.demo-section {
  margin-bottom: 3rem;

  h3 {
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
  }

  p {
    color: #555;
    line-height: 1.6;
    margin-bottom: 1.5rem;
  }
}

.api-section {
  margin-bottom: 2rem;

  h4 {
    color: #2c3e50;
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }
}

.table-container {
  overflow-x: auto;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  background: white;
}

.api-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;

  th {
    background: #f8f9fa;
    color: #2c3e50;
    font-weight: 600;
    padding: 1rem;
    text-align: left;
    border-bottom: 2px solid #dee2e6;
  }

  td {
    padding: 1rem;
    border-bottom: 1px solid #e9ecef;
    vertical-align: top;

    &:first-child {
      font-weight: 500;
      color: #2c3e50;
    }

    &:nth-child(2) {
      color: #e74c3c;
    }

    &:nth-child(3) {
      color: #27ae60;
    }
  }

  tr:hover {
    background: #f8f9fa;
  }

  code {
    background: #f1f3f4;
    padding: 0.2rem 0.4rem;
    border-radius: 3px;
    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
    font-size: 0.85rem;
  }
}

.usage-examples {
  display: grid;
  gap: 2rem;
  margin-bottom: 2rem;
}

.example-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;

  h4 {
    color: #2c3e50;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }
}

.code-block {
  background: #2c3e50;
  border-radius: 6px;
  overflow: hidden;

  pre {
    margin: 0;
    padding: 1.5rem;
    overflow-x: auto;

    code {
      color: #ecf0f1;
      font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
      font-size: 0.9rem;
      line-height: 1.5;
      white-space: pre-wrap;
    }
  }
}

.implementation-notes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1.5rem;
}

.note {
  background: #f8f9fa;
  border-left: 4px solid #3498db;
  padding: 1.5rem;
  border-radius: 0 6px 6px 0;

  h4 {
    color: #2c3e50;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      color: #555;
      padding: 0.5rem 0;
      border-bottom: 1px solid #e9ecef;
      position: relative;
      padding-left: 1.5rem;

      &:before {
        content: "•";
        color: #3498db;
        font-weight: bold;
        position: absolute;
        left: 0;
      }

      &:last-child {
        border-bottom: none;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .demo-container {
    padding: 1rem;
  }

  .api-table {
    font-size: 0.8rem;

    th,
    td {
      padding: 0.75rem 0.5rem;
    }
  }

  .implementation-notes {
    grid-template-columns: 1fr;
  }

  .code-block pre {
    padding: 1rem;

    code {
      font-size: 0.8rem;
    }
  }
}

// Accessibility improvements
.demo-container:focus-within {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}

.api-table:focus-within {
  outline: 2px solid #3498db;
  outline-offset: 2px;
}
