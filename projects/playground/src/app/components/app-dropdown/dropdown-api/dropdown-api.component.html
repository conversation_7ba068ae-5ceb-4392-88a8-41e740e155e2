<div class="demo-container">
  <div class="demo-section">
    <h3>API Reference</h3>
    <p>
      Complete documentation of all properties, events, and interfaces for the
      dropdown component.
    </p>

    <div class="api-section">
      <h4>Properties</h4>
      <div class="table-container">
        <table class="api-table">
          <thead>
            <tr>
              <th>Property</th>
              <th>Type</th>
              <th>Default</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let prop of apiProps">
              <td>
                <code>{{ prop.name }}</code>
              </td>
              <td>
                <code>{{ prop.type }}</code>
              </td>
              <td>
                <code>{{ prop.default }}</code>
              </td>
              <td>{{ prop.description }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="api-section">
      <h4>DropdownOption Interface</h4>
      <div class="table-container">
        <table class="api-table">
          <thead>
            <tr>
              <th>Property</th>
              <th>Type</th>
              <th>Default</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let prop of interfaceProps">
              <td>
                <code>{{ prop.name }}</code>
              </td>
              <td>
                <code>{{ prop.type }}</code>
              </td>
              <td>
                <code>{{ prop.default }}</code>
              </td>
              <td>{{ prop.description }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div class="api-section">
      <h4>Events</h4>
      <div class="table-container">
        <table class="api-table">
          <thead>
            <tr>
              <th>Event</th>
              <th>Type</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let event of events">
              <td>
                <code>{{ event.name }}</code>
              </td>
              <td>
                <code>{{ event.type }}</code>
              </td>
              <td>{{ event.description }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h3>Usage Examples</h3>
    <p>Common usage patterns and code examples for the dropdown component.</p>

    <div class="usage-examples">
      <div class="example-item" *ngFor="let example of getUsageExamples()">
        <h4>{{ example.title }}</h4>
        <div class="code-block">
          <pre><code>{{ example.code }}</code></pre>
        </div>
      </div>
    </div>
  </div>

  <div class="demo-section">
    <h3>Implementation Notes</h3>
    <p>
      Important considerations and best practices for using the dropdown
      component.
    </p>

    <div class="implementation-notes">
      <div class="note">
        <h4>✅ Best Practices</h4>
        <ul>
          <li>Always provide meaningful dropdown titles</li>
          <li>Use search functionality for dropdowns with 10+ options</li>
          <li>Provide default selections when appropriate</li>
          <li>Handle selection changes properly in your component</li>
          <li>Use proper ARIA labels for accessibility</li>
        </ul>
      </div>

      <div class="note">
        <h4>⚠️ Common Pitfalls</h4>
        <ul>
          <li>Don't forget to handle the selectionChange event</li>
          <li>Avoid deeply nested sub-options (max 2 levels)</li>
          <li>Ensure option names are unique within the same level</li>
          <li>Don't use disabled state without clear indication</li>
          <li>Test keyboard navigation thoroughly</li>
        </ul>
      </div>

      <div class="note">
        <h4>🎯 Performance Tips</h4>
        <ul>
          <li>Use search for large option sets</li>
          <li>Consider lazy loading for very large datasets</li>
          <li>Optimize option rendering for complex hierarchies</li>
          <li>Use proper change detection strategies</li>
          <li>Monitor memory usage with large option lists</li>
        </ul>
      </div>
    </div>
  </div>
</div>
