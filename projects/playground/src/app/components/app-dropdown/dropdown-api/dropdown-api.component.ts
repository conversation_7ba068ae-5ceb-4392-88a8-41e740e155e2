import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

interface ApiEvent {
  name: string;
  type: string;
  description: string;
}

@Component({
  selector: 'ava-dropdown-api',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './dropdown-api.component.html',
  styleUrls: ['./dropdown-api.component.scss'],
})
export class DropdownApiComponent {
  apiProps: ApiProperty[] = [
    {
      name: 'dropdownTitle',
      type: 'string',
      default: 'Select a Category',
      description: 'The title displayed when no option is selected.',
    },
    {
      name: 'options',
      type: 'DropdownOption[]',
      default: '[]',
      description: 'The list of options to display in the dropdown.',
    },
    {
      name: 'suboptions',
      type: '{ [key: string]: DropdownOption[] }',
      default: '{}',
      description: 'A map of options to their respective sub-options.',
    },
    {
      name: 'selectedValue',
      type: 'string',
      default: "''",
      description: 'The name of the option to be selected by default.',
    },
    {
      name: 'dropdownIcon',
      type: 'string',
      default: 'chevron-down',
      description: 'The icon to display in the dropdown toggle button.',
    },
    {
      name: 'checkboxOptions',
      type: 'string[]',
      default: '[]',
      description: 'The list of options that will display a checkbox.',
    },
    {
      name: 'search',
      type: 'boolean',
      default: 'false',
      description: 'Whether to display the search functionality.',
    },
    {
      name: 'singleSelect',
      type: 'boolean',
      default: 'false',
      description: 'Whether checkboxes allow single selection only.',
    },
    {
      name: 'disabled',
      type: 'boolean',
      default: 'false',
      description: 'Disables the entire dropdown component.',
    },
    {
      name: 'label',
      type: 'string',
      default: "''",
      description: 'Label text to display above the dropdown.',
    },
    {
      name: 'required',
      type: 'boolean',
      default: 'false',
      description:
        'Whether the field is required. Shows validation error if opened/closed without selection.',
    },
    {
      name: 'error',
      type: 'string',
      default: "''",
      description:
        'Custom error message to display below the dropdown. Overrides default required validation message.',
    },
  ];

  interfaceProps: ApiProperty[] = [
    {
      name: 'DropdownOption.name',
      type: 'string',
      default: 'N/A',
      description: 'The display name of the option.',
    },
    {
      name: 'DropdownOption.value',
      type: 'string | number',
      default: 'N/A',
      description: 'The value of the option.',
    },
    {
      name: 'DropdownOption.icon',
      type: 'string (optional)',
      default: 'N/A',
      description: 'The Lucide icon name to display for this option.',
    },
    {
      name: 'DropdownOption.disabled',
      type: 'boolean (optional)',
      default: 'false',
      description: 'Whether this specific option is disabled.',
    },
  ];

  events: ApiEvent[] = [
    {
      name: 'selectionChange',
      type: 'EventEmitter<any>',
      description: 'Emitted when the selection changes.',
    },
  ];

  getUsageExamples() {
    return [
      {
        title: 'Basic Usage',
        code: `<ava-dropdown
  dropdownTitle="Select Category"
  [options]="options"
  (selectionChange)="onSelectionChange($event)">
</ava-dropdown>`,
      },
      {
        title: 'With Sub-options',
        code: `<ava-dropdown
  dropdownTitle="Select Category"
  [options]="options"
  [suboptions]="suboptions"
  (selectionChange)="onSelectionChange($event)">
</ava-dropdown>`,
      },
      {
        title: 'With Search',
        code: `<ava-dropdown
  dropdownTitle="Search Options"
  [options]="options"
  [search]="true"
  (selectionChange)="onSelectionChange($event)">
</ava-dropdown>`,
      },
      {
        title: 'Multi-Select',
        code: `<ava-dropdown
  dropdownTitle="Select Features"
  [options]="options"
  [checkboxOptions]="['Feature A', 'Feature B']"
  (selectionChange)="onSelectionChange($event)">
</ava-dropdown>`,
      },
    ];
  }
}
