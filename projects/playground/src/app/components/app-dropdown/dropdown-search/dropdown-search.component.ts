import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  DropdownComponent,
  DropdownOption,
} from '../../../../../../play-comp-library/src/public-api';

interface SearchExample {
  title: string;
  description: string;
  options: DropdownOption[];
  suboptions?: Record<string, DropdownOption[]>;
  config: {
    dropdownTitle: string;
    search: boolean;
    selectedValue?: string;
  };
}

interface SelectionEvent {
  selectedValue: string;
  selectedOption?: DropdownOption;
  subOption?: DropdownOption;
}

@Component({
  selector: 'ava-dropdown-search',
  standalone: true,
  imports: [CommonModule, DropdownComponent],
  templateUrl: './dropdown-search.component.html',
  styleUrls: ['./dropdown-search.component.scss'],
})
export class DropdownSearchComponent {
  searchExamples: SearchExample[] = [
    {
      title: 'Basic Search',
      description:
        'Simple dropdown with search functionality to filter options.',
      options: [
        { name: '<PERSON>', value: 'apple' },
        { name: '<PERSON><PERSON>', value: 'banana' },
        { name: 'Orange', value: 'orange' },
        { name: '<PERSON>rape', value: 'grape' },
        { name: 'Mango', value: 'mango' },
        { name: 'Pineapple', value: 'pineapple' },
        { name: 'Strawberry', value: 'strawberry' },
        { name: 'Blueberry', value: 'blueberry' },
      ],
      config: {
        dropdownTitle: 'Search fruits',
        search: true,
      },
    },
    {
      title: 'Search with Sub-options',
      description:
        'Dropdown with search that also filters through sub-options.',
      options: [
        { name: 'Electronics', value: 'electronics' },
        { name: 'Clothing', value: 'clothing' },
        { name: 'Books', value: 'books' },
      ],
      suboptions: {
        Electronics: [
          { name: 'Phones', value: 'phones' },
          { name: 'Laptops', value: 'laptops' },
          { name: 'Tablets', value: 'tablets' },
        ],
        Clothing: [
          { name: 'Shirts', value: 'shirts' },
          { name: 'Pants', value: 'pants' },
          { name: 'Shoes', value: 'shoes' },
        ],
        Books: [
          { name: 'Fiction', value: 'fiction' },
          { name: 'Non-Fiction', value: 'non-fiction' },
          { name: 'Science Fiction', value: 'sci-fi' },
        ],
      },
      config: {
        dropdownTitle: 'Search categories',
        search: true,
      },
    },
    {
      title: 'Large Dataset Search',
      description: 'Search through a large number of options efficiently.',
      options: [
        { name: 'United States', value: 'us' },
        { name: 'Canada', value: 'ca' },
        { name: 'United Kingdom', value: 'uk' },
        { name: 'Australia', value: 'au' },
        { name: 'Germany', value: 'de' },
        { name: 'France', value: 'fr' },
        { name: 'Japan', value: 'jp' },
        { name: 'India', value: 'in' },
        { name: 'Brazil', value: 'br' },
        { name: 'Mexico', value: 'mx' },
        { name: 'Italy', value: 'it' },
        { name: 'Spain', value: 'es' },
        { name: 'Netherlands', value: 'nl' },
        { name: 'Sweden', value: 'se' },
        { name: 'Norway', value: 'no' },
        { name: 'Denmark', value: 'dk' },
        { name: 'Finland', value: 'fi' },
        { name: 'Switzerland', value: 'ch' },
        { name: 'Austria', value: 'at' },
        { name: 'Belgium', value: 'be' },
      ],
      config: {
        dropdownTitle: 'Search countries',
        search: true,
      },
    },
    {
      title: 'Search with Default Selection',
      description: 'Dropdown with search and a pre-selected option.',
      options: [
        { name: 'Red', value: 'red' },
        { name: 'Green', value: 'green' },
        { name: 'Blue', value: 'blue' },
        { name: 'Yellow', value: 'yellow' },
        { name: 'Purple', value: 'purple' },
        { name: 'Orange', value: 'orange' },
        { name: 'Pink', value: 'pink' },
        { name: 'Brown', value: 'brown' },
      ],
      config: {
        dropdownTitle: 'Search colors',
        search: true,
        selectedValue: 'Blue',
      },
    },
  ];

  searchUseCases = [
    {
      title: 'Product Search',
      description: 'Search through product catalogs with categories.',
      options: [
        { name: 'Electronics', value: 'electronics' },
        { name: 'Home & Garden', value: 'home' },
        { name: 'Sports & Outdoors', value: 'sports' },
        { name: 'Books & Media', value: 'books' },
      ],
      suboptions: {
        Electronics: [
          { name: 'Smartphones', value: 'smartphones' },
          { name: 'Laptops', value: 'laptops' },
          { name: 'Headphones', value: 'headphones' },
        ],
        'Home & Garden': [
          { name: 'Furniture', value: 'furniture' },
          { name: 'Kitchen', value: 'kitchen' },
          { name: 'Garden Tools', value: 'garden' },
        ],
      },
    },
    {
      title: 'User Directory',
      description: 'Search through user directories or contact lists.',
      options: [
        { name: 'John Smith', value: 'john' },
        { name: 'Jane Doe', value: 'jane' },
        { name: 'Mike Johnson', value: 'mike' },
        { name: 'Sarah Wilson', value: 'sarah' },
        { name: 'David Brown', value: 'david' },
        { name: 'Lisa Davis', value: 'lisa' },
      ],
    },
  ];

  onSelectionChange(event: SelectionEvent, exampleTitle: string) {
    console.log(`${exampleTitle} selection changed:`, event);
    // Show a demo message
  }

  getSearchTips() {
    return [
      'Type to filter options in real-time',
      'Search works on both main options and sub-options',
      'Case-insensitive search for better user experience',
      'Clear search to see all options again',
      'Use keyboard navigation (arrow keys) to move through filtered results',
    ];
  }
}
