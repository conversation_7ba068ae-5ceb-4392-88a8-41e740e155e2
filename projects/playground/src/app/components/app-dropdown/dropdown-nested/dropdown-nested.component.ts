import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  DropdownComponent,
  DropdownOption,
} from '../../../../../../play-comp-library/src/public-api';

interface NestedExample {
  title: string;
  description: string;
  options: DropdownOption[];
  suboptions: Record<string, DropdownOption[]>;
  config: {
    dropdownTitle: string;
    search?: boolean;
    selectedValue?: string;
  };
}

interface SelectionEvent {
  selectedValue: string;
  selectedOption?: DropdownOption;
  subOption?: DropdownOption;
}

@Component({
  selector: 'ava-dropdown-nested',
  standalone: true,
  imports: [CommonModule, DropdownComponent],
  templateUrl: './dropdown-nested.component.html',
  styleUrls: ['./dropdown-nested.component.scss'],
})
export class DropdownNestedComponent {
  nestedExample: NestedExample = {
    title: 'Simple Nested Structure',
    description: 'Basic dropdown with categories and subcategories.',
    options: [
      { name: 'Electronics', value: 'electronics' },
      { name: 'Clothing', value: 'clothing' },
      { name: 'Books', value: 'books' },
    ],
    suboptions: {
      Electronics: [
        { name: 'Phones', value: 'phones' },
        { name: 'Laptops', value: 'laptops' },
        { name: 'Tablets', value: 'tablets' },
      ],
      Clothing: [
        { name: 'Shirts', value: 'shirts' },
        { name: 'Pants', value: 'pants' },
        { name: 'Shoes', value: 'shoes' },
      ],
      Books: [
        { name: 'Fiction', value: 'fiction' },
        { name: 'Non-Fiction', value: 'non-fiction' },
        { name: 'Textbooks', value: 'textbooks' },
      ],
    },
    config: {
      dropdownTitle: 'Select Category',
      search: false,
    },
  };

  onSelectionChange(event: SelectionEvent, exampleTitle: string) {
    console.log(`${exampleTitle} selection changed:`, event);
    // Show a demo message
    // alert(`Demo: ${exampleTitle} - Selected: ${event.selectedValue}`);
  }
}
