<div class="demo-container">
  <div class="dropdown-demo">
    <ava-dropdown
      [dropdownTitle]="nestedExample.config.dropdownTitle"
      [options]="nestedExample.options"
      [suboptions]="nestedExample.suboptions"
      [search]="nestedExample.config.search ?? false"
      [selectedValue]="nestedExample.config.selectedValue ?? ''"
      (selectionChange)="onSelectionChange($event, nestedExample.title)"
      tabindex="0"
      role="button"
      [attr.aria-label]="nestedExample.title + ' dropdown'"
    >
    </ava-dropdown>
  </div>
</div>
