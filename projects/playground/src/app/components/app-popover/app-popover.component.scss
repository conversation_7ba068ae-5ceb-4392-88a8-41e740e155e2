.popover-demo-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;

  h1 {
    color: var(--color-text-primary);
    margin-bottom: 2rem;
    text-align: center;
  }

  .demo-section {
    margin-bottom: 3rem;
    padding: 1.5rem;
    border: 1px solid var(--color-border-default);
    border-radius: var(--global-radius-md);
    background: var(--color-background-secondary);

    h2 {
      color: var(--color-text-primary);
      margin-bottom: 1.5rem;
      font-size: 1.25rem;
    }

    h3 {
      color: var(--color-text-secondary);
      margin-bottom: 0.75rem;
      font-size: 1rem;
      font-weight: 500;
    }
  }

  .demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }

  .demo-item {
    padding: 1rem;
    border: 1px solid var(--color-border-subtle);
    border-radius: var(--global-radius-sm);
    background: var(--color-background-primary);
    text-align: center;
  }

  .position-demo {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
    min-height: 200px;
  }

  .arrow-demo {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
    min-height: 150px;
  }

  .variant-demo {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
    min-height: 150px;
  }

  .trigger-demo {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
    min-height: 150px;
  }

  .tour-trigger {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: var(--color-background-accent);
    color: var(--color-text-on-accent);
    border-radius: var(--global-radius-sm);
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background: var(--color-background-accent-hover);
    }

    span {
      font-weight: 500;
    }
  }

  .setup-guide {
    display: inline-block;
    padding: 0.5rem 1rem;
    background: var(--color-background-info);
    color: var(--color-text-on-info);
    border-radius: var(--global-radius-sm);
    cursor: pointer;
    font-weight: 500;
    transition: background-color 0.2s ease;

    &:hover {
      background: var(--color-background-info-hover);
    }
  }
}
