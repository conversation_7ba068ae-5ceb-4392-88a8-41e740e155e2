<div class="popover-demo-container">
  <h1>Pop-over Component Demo</h1>

  <div class="demo-section">
    <h2>Position Examples</h2>
    
    <div class="position-demo">
      <!-- Top Position -->
      <ava-button
        avaPopover
        [avaPopoverData]="samplePopoverData"
        [avaPopoverPosition]="'top'"
        [avaPopoverArrow]="'center'"
        [label]="'Top'"
        [variant]="'primary'"
        [outlined]="true"
        pressedEffect="ripple">
      </ava-button>

      <!-- Bottom Position -->
      <ava-button
        avaPopover
        [avaPopoverData]="samplePopoverData"
        [avaPopoverPosition]="'bottom'"
        [avaPopoverArrow]="'center'"
        [label]="'Bottom'"
        [variant]="'primary'"
        [outlined]="true"
        pressedEffect="ripple">
      </ava-button>

      <!-- Left Position -->
      <ava-button
        avaPopover
        [avaPopoverData]="samplePopoverData"
        [avaPopoverPosition]="'left'"
        [avaPopoverArrow]="'center'"
        [label]="'Left'"
        [variant]="'primary'"
        [outlined]="true"
        pressedEffect="ripple">
      </ava-button>

      <!-- Right Position -->
      <ava-button
        avaPopover
        [avaPopoverData]="samplePopoverData"
        [avaPopoverPosition]="'right'"
        [avaPopoverArrow]="'center'"
        [label]="'Right'"
        [variant]="'primary'"
        [outlined]="true"
        pressedEffect="ripple">
      </ava-button>
    </div>
  </div>

  <div class="demo-section">
    <h2>Popover Variants</h2>

    <div class="variant-demo">

      <!-- Variant 1: Learn More with custom URL -->
      <ava-button
        avaPopover
        [avaPopoverData]="learnMoreExampleData"
        [avaPopoverPosition]="'bottom'"
        [avaPopoverArrow]="'center'"
        [avaPopoverShowLearnMore]="true"
        [label]="'Custom URL'"
        [variant]="'primary'"
        [outlined]="true"
        pressedEffect="ripple">
      </ava-button>

      <!-- Variant 2: Buttons only (showButtons = true) -->
      <ava-button
        avaPopover
        [avaPopoverData]="samplePopoverData"
        [avaPopoverPosition]="'bottom'"
        [avaPopoverArrow]="'center'"
        [avaPopoverShowButtons]="true"
        [label]="'Buttons Only'"
        [variant]="'primary'"
        [outlined]="true"
        pressedEffect="ripple">
      </ava-button>

      <!-- Variant 3: Pagination + Buttons (showPagination = true, showButtons = true) -->
      <ava-button
        avaPopover
        [avaPopoverData]="multiStepData"
        [avaPopoverPosition]="'bottom'"
        [avaPopoverArrow]="'center'"
        [avaPopoverShowPagination]="true"
        [avaPopoverShowButtons]="true"
        [label]="'Pagination + Buttons'"
        [variant]="'primary'"
        [outlined]="true"
        pressedEffect="ripple">
      </ava-button>

      <!-- Variant 4: Skip + Icon navigation (showSkip = true, showIcon = true) -->
      <ava-button
        avaPopover
        [avaPopoverData]="tourData"
        [avaPopoverPosition]="'bottom'"
        [avaPopoverArrow]="'center'"
        [avaPopoverShowSkip]="true"
        [avaPopoverShowIcon]="true"
        [label]="'Skip + Icon'"
        [variant]="'primary'"
        [outlined]="true"
        pressedEffect="ripple">
      </ava-button>
    </div>
  </div>

  <div class="demo-section">
    <h2>Arrow Position Examples</h2>

    <div class="arrow-demo">
      <!-- Start Arrow -->
      <ava-button
        avaPopover
        [avaPopoverData]="multiStepData"
        [avaPopoverPosition]="'bottom'"
        [avaPopoverArrow]="'start'"
        [label]="'Start Arrow'"
        [variant]="'primary'"
        [outlined]="true"
        pressedEffect="ripple">
      </ava-button>

      <!-- Center Arrow -->
      <ava-button
        avaPopover
        [avaPopoverData]="multiStepData"
        [avaPopoverPosition]="'bottom'"
        [avaPopoverArrow]="'center'"
        [label]="'Center Arrow'"
        [variant]="'primary'"
        [outlined]="true"
        pressedEffect="ripple">
      </ava-button>

      <!-- End Arrow -->
      <ava-button
        avaPopover
        [avaPopoverData]="multiStepData"
        [avaPopoverPosition]="'bottom'"
        [avaPopoverArrow]="'end'"
        [label]="'End Arrow'"
        [variant]="'primary'"
        [outlined]="true"
        pressedEffect="ripple">
      </ava-button>
    </div>


  </div>
</div>
