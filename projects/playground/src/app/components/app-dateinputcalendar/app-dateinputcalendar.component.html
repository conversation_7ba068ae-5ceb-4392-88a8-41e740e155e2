<div class="documentation">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Calendar Component</h1>
        <p class="description">
          A comprehensive calendar component that supports both single date and date range selection. Features an intuitive interface with month/year navigation, input field support, and hover effects for range selection. Users can input dates in the <strong>dd/mm/yyyy</strong> format.
        </p>
      </header>
    </div>
  </div>

  <!-- Installation -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} CalendarComponent {{ '}' }} from '&#64;ava/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section *ngFor="let section of sections; let i = index" class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <p><strong>Note:</strong> Users can input dates in the <strong>dd/mm/yyyy</strong> format, and the selected date will be automatically marked in the calendar.</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <ng-container *ngSwitchCase="'Single Date Selection'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-calendar (dateSelected)="onDateSelected($event)"></ava-calendar>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Date Range Selection'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-calendar [isRange]="true" (rangeSelected)="onRangeSelected($event)"></ava-calendar>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Pre-selected Date'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-calendar [selectedDate]="preSelectedDate" (dateSelected)="onDateSelected($event)"></ava-calendar>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Pre-selected Range'">
              <div class="row g-3">
                <div class="col-12 col-sm-auto">
                  <ava-calendar [isRange]="true" [dateRange]="preSelectedRange" (rangeSelected)="onRangeSelected($event)"></ava-calendar>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Weekday Format Options'">
              <div class="weekday-format-examples">
                <div class="format-example">
                  <h5>3 Letters (Mon, Tue, Wed...)</h5>
                  <ava-calendar [weekdayFormat]="3" [alwaysOpen]="true" (dateSelected)="onDateSelected($event)"></ava-calendar>
                </div>
                <div class="format-example">
                  <h5>2 Letters (Mo, Tu, We...)</h5>
                  <ava-calendar [weekdayFormat]="2" [alwaysOpen]="true" (dateSelected)="onDateSelected($event)"></ava-calendar>
                </div>
                <div class="format-example">
                  <h5>1 Letter (M, T, W...)</h5>
                  <ava-calendar [weekdayFormat]="1" [alwaysOpen]="true" (dateSelected)="onDateSelected($event)"></ava-calendar>
                </div>
              </div>
            </ng-container>

            <ng-container *ngSwitchCase="'Shape Variants Showcase'">
              <!-- Add new container for selector shape variants showcase below all main examples -->
              <div class="row" class="weekday-format-examples">
                <div class="col-12">
                  <section class="doc-section">
                    <div class="row g-3" style="margin-top: 1.5rem;">
                      <div class="col-12 col-sm-auto">
                        <h5>Square Selector (default)</h5>
                        <ava-calendar [alwaysOpen]="true" selectorShape="square" (dateSelected)="onDateSelected($event)" [ngStyle]="{width: '400px'}"></ava-calendar>
                      </div>
                      <div class="col-12 col-sm-auto">
                        <h5>Circle Selector</h5>
                        <ava-calendar [alwaysOpen]="true" selectorShape="circle" (dateSelected)="onDateSelected($event)" [ngStyle]="{width: '400px'}"></ava-calendar>
                      </div>
                    </div>
                  </section>
                </div>
              </div>
            </ng-container>
            <ng-container *ngSwitchCase="'Glassmorphism Surface Example'"   >
              <div class="row g-3" class="weekday-format-examples">
                <div class="col-12 col-sm-auto">
                  <h5>Glassmorphism (Medium)</h5>
                  <ava-calendar [alwaysOpen]="true" [surface]="true" surfaceStrength="medium" (dateSelected)="onDateSelected($event)"></ava-calendar>
                </div>
                <div class="col-12 col-sm-auto">
                  <h5>Glassmorphism (Strong)</h5>
                  <ava-calendar [alwaysOpen]="true" [surface]="true" surfaceStrength="strong" (dateSelected)="onDateSelected($event)"></ava-calendar>
                </div>
                <div class="col-12 col-sm-auto">
                  <h5>Glassmorphism (Max)</h5>
                  <ava-calendar [alwaysOpen]="true" [surface]="true" surfaceStrength="max" (dateSelected)="onDateSelected($event)"></ava-calendar>
                </div>
              </div>
            </ng-container>
          </ng-container>
        </div>
        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getCalendarCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            Copy Code
          </button>
        </div>
      </div>
    </section>
  </div>

  <!-- API Reference -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section api-reference">
        <h2>API Reference</h2>
        <table class="api-table">
          <thead>
            <tr>
              <th>Property</th>
              <th>Type</th>
              <th>Default</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let prop of apiProps">
              <td><code>{{ prop.name }}</code></td>
              <td><code>{{ prop.type }}</code></td>
              <td><code>{{ prop.default }}</code></td>
              <td>{{ prop.description }}</td>
            </tr>
          </tbody>
        </table>
      </section>
    </div>
  </div>

  <!-- Events -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Events</h2>
        <table class="api-table">
          <thead>
            <tr>
              <th>Event</th>
              <th>Type</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code>dateSelected</code></td>
              <td><code>EventEmitter&lt;Date&gt;</code></td>
              <td>Emitted when a single date is selected (only in single date mode).</td>
            </tr>
            <tr>
              <td><code>rangeSelected</code></td>
              <td><code>EventEmitter&lt;DateRange&gt;</code></td>
              <td>Emitted when a date range is selected (only in range mode).</td>
            </tr>
          </tbody>
        </table>
      </section>
    </div>
  </div>
</div>