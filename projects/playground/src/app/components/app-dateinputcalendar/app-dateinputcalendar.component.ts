import { Component, ElementRef, HostListener, ViewChild, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CalendarComponent, DateRange } from '../../../../../play-comp-library/src/lib/components/calendar/calendar.component';

@Component({
  selector: 'app-app-dateinputcalendar',
  imports: [CommonModule, CalendarComponent],
  templateUrl: './app-dateinputcalendar.component.html',
  styleUrl: './app-dateinputcalendar.component.scss',
  encapsulation: ViewEncapsulation.None
})
export class AppDateinputcalendarComponent {
  @ViewChild('codeBlock') codeBlock!: ElementRef;

  // Sample data for examples
  preSelectedDate = new Date(2024, 2, 15); // March 15, 2024
  preSelectedRange: DateRange = {
    start: new Date(2024, 2, 10), // March 10, 2024
    end: new Date(2024, 2, 20)    // March 20, 2024
  };

  showCalendarVariant = false;

  sections = [
    {
      title: 'Single Date Selection',
      description: 'Demonstrates the calendar component for selecting a single date.',
      showCode: false,
    },
    {
      title: 'Date Range Selection',
      description: 'Demonstrates the calendar component with range selection enabled.',
      showCode: false,
    },
    {
      title: 'Pre-selected Date',
      description: 'Demonstrates the calendar component with a pre-selected date.',
      showCode: false,
    },
    {
      title: 'Pre-selected Range',
      description: 'Demonstrates the calendar component with a pre-selected date range.',
      showCode: false,
    },
    {
      title: 'Weekday Format Options',
      description: 'Demonstrates the calendar component with different weekday format options (1, 2, or 3 letters).',
      showCode: false,
    },
        {
      title: 'Shape Variants Showcase',
      description: 'Demonstrates the calendar component with different Shape Variants Showcase.',
      showCode: false,
    },
    {
      title: 'Glassmorphism Surface Example',
      description: 'Demonstrates the calendar component with glassmorphism surface effect using the surface and surfaceStrength inputs.',
      showCode: false,
    },
  
  ];
  

  apiProps = [
    { name: 'isRange', type: 'boolean', default: 'false', description: 'Whether to enable date range selection mode.' },
    { name: 'selectedDate', type: 'Date | null', default: 'null', description: 'The currently selected date (for single date mode).' },
    { name: 'dateRange', type: 'DateRange', default: '{ start: null, end: null }', description: 'The selected date range (for range mode).' },
    { name: 'weekdayFormat', type: '1 | 2 | 3', default: '3', description: 'Format for weekday display: 1 = single letter (M), 2 = two letters (Mo), 3 = three letters (Mon).' },
    { name: 'alwaysOpen', type: 'boolean', default: 'false', description: 'Whether to keep the calendar always open (useful for demos and embedded calendars).' }
  ];
  
  toggleSection(index: number): void {
    this.sections.forEach((section, i) => {
      section.showCode = (i === index) ? !section.showCode : false;
    });
  }

  onDateSelected(date: Date) {
    console.log('Selected date:', date);
    // Handle the selected date as needed
  }

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation(); // Prevent the click event from bubbling up to the section header
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  onRangeSelected(range: DateRange) {
    console.log('Selected Range:', range.start, range.end);
  }
  

  @HostListener('document:click', ['$event'])
  clickOutside(event: MouseEvent) {
    if (this.codeBlock && !this.codeBlock.nativeElement.contains(event.target)) {
      this.sections.forEach(section => section.showCode = false);
    }
  }

  getCalendarCode(sectionTitle: string): string {
    const examples: Record<string, string> = {
      'single date selection': `
  import { Component } from '@angular/core';
  import { CalendarComponent } from '@ava/play-comp-library';

  @Component({
    selector: 'app-single-date-calendar',
    standalone: true,
    imports: [CalendarComponent],
    template: \`
        <ava-calendar (dateSelected)="onDateSelected($event)"></ava-calendar>
    \`
  })
  export class SingleDateCalendarComponent {
    onDateSelected(date: Date) {
      console.log('Selected date:', date);
      // Handle the selected date as needed
    }
  }`,
      'date range selection': `
  import { Component } from '@angular/core';
  import { CalendarComponent, DateRange } from '@ava/play-comp-library';

  @Component({
    selector: 'app-range-selection',
    standalone: true,
    imports: [CalendarComponent],
    template: \`
        <ava-calendar [isRange]="true" (rangeSelected)="onRangeSelected($event)"></ava-calendar>
    \`
  })
  export class RangeSelectionComponent {
    onRangeSelected(range: DateRange) {
      console.log('Selected Range:', range.start, range.end);
      // Handle the selected range as needed
    }
  }`,
      'pre-selected date': `
  import { Component } from '@angular/core';
  import { CalendarComponent } from '@ava/play-comp-library';

  @Component({
    selector: 'app-preselected-date',
    standalone: true,
    imports: [CalendarComponent],
    template: \`
        <ava-calendar [selectedDate]="preSelectedDate" (dateSelected)="onDateSelected($event)"></ava-calendar>
    \`
  })
  export class PreselectedDateComponent {
    preSelectedDate = new Date(2024, 2, 15); // March 15, 2024

    onDateSelected(date: Date) {
      console.log('Selected date:', date);
      // Handle the selected date as needed
    }
  }`,
      'pre-selected range': `
  import { Component } from '@angular/core';
  import { CalendarComponent, DateRange } from '@ava/play-comp-library';

  @Component({
    selector: 'app-preselected-range',
    standalone: true,
    imports: [CalendarComponent],
    template: \`
        <ava-calendar [isRange]="true" [dateRange]="preSelectedRange" (rangeSelected)="onRangeSelected($event)"></ava-calendar>
    \`
  })
  export class PreselectedRangeComponent {
    preSelectedRange: DateRange = {
      start: new Date(2024, 2, 10), // March 10, 2024
      end: new Date(2024, 2, 20)    // March 20, 2024
    };

    onRangeSelected(range: DateRange) {
      console.log('Selected Range:', range.start, range.end);
      // Handle the selected range as needed
    }
  }`,
      'weekday format options': `
  import { Component } from '@angular/core';
  import { CalendarComponent } from '@ava/play-comp-library';

  @Component({
    selector: 'app-weekday-format',
    standalone: true,
    imports: [CalendarComponent],
    template: \`
        <!-- Three Letters (Default) -->
        <ava-calendar [weekdayFormat]="3" [alwaysOpen]="true" (dateSelected)="onDateSelected($event)"></ava-calendar>

        <!-- Two Letters -->
        <ava-calendar [weekdayFormat]="2" [alwaysOpen]="true" (dateSelected)="onDateSelected($event)"></ava-calendar>

        <!-- Single Letter -->
        <ava-calendar [weekdayFormat]="1" [alwaysOpen]="true" (dateSelected)="onDateSelected($event)"></ava-calendar>
    \`
  })
  export class WeekdayFormatComponent {
    onDateSelected(date: Date) {
      console.log('Selected date:', date);
      // Handle the selected date as needed
    }
  }`,
      'shape variants showcase': `
  import { Component } from '@angular/core';
  import { CalendarComponent } from '@ava/play-comp-library';

  @Component({
    selector: 'app-shape-variants',
    standalone: true,
    imports: [CalendarComponent],
    template: \`
        <ava-calendar [alwaysOpen]="true" [surface]="true" surfaceStrength="medium" (dateSelected)="onDateSelected($event)"></ava-calendar>
    \`
  })
  export class ShapeVariantsComponent {
    onDateSelected(date: Date) {
      console.log('Selected date:', date);
      // Handle the selected date as needed
    }
  }`,
      'glassmorphism surface example': `
  import { Component } from '@angular/core';
  import { CalendarComponent } from '@ava/play-comp-library';

  @Component({
    selector: 'app-glass-calendar',
    standalone: true,
    imports: [CalendarComponent],
    template: \`
        <ava-calendar [alwaysOpen]="true" [surface]="true" surfaceStrength="medium" (dateSelected)="onDateSelected($event)"></ava-calendar>
    \`
  })
  export class GlassCalendarComponent {
    onDateSelected(date: Date) {
      console.log('Selected date:', date);
      // Handle the selected date as needed
    }
  }`,
    };

    return examples[sectionTitle.toLowerCase()] || '';
  }

  // Copy Code to Clipboard (for the code example)
  copyCode(section: string): void {
    const code = this.getCalendarCode(section);
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy code:', err);
    });
  }
}
