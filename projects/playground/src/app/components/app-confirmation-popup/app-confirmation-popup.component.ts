import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ConfirmationPopupComponent } from '../../../../../play-comp-library/src/lib/composite-components/confirmation-popup/confirmation-popup.component';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';

@Component({
  selector: 'ava-confirmation-popup-demo',
  standalone: true,
  imports: [CommonModule, ConfirmationPopupComponent, ButtonComponent],
  templateUrl: './app-confirmation-popup.component.html',
  styleUrl: './app-confirmation-popup.component.scss',
})
export class AppConfirmationPopupComponent {
  showPopup = false;
  popupData = {
    title: 'Confirm Action',
    message:
      'Are you sure you want to delete this item? This action cannot be undone.',
    confirmText: 'Confirm',
    cancelText: 'Cancel',
    type: 'danger',
    showForm: false,
    label: 'Comments',
  };

  showConfirmation() {
    this.popupData.showForm = false;
    this.showPopup = true;
  }

  showConfirmationWithForm() {
    this.popupData.showForm = true;
    this.showPopup = true;
  }

  onConfirm(feedback?: string) {
    if (feedback) {
      console.log('Action confirmed with feedback:', feedback);
    } else {
      console.log('Action confirmed!');
    }
    this.showPopup = false;
  }

  onCancel() {
    console.log('Action cancelled!');
    this.showPopup = false;
  }

  onClose() {
    console.log('Popup closed!');
    this.showPopup = false;
  }
}
