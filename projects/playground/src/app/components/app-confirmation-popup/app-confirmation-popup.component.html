<div class="confirmation-popup-demo">
  <div class="demo-section">
    <!-- <button (click)="showConfirmation()" class="demo-button">
      Show Basic Confirmation Popup
    </button> -->

    <ava-button
      (click)="showConfirmationWithForm()"
      label="Show Confirmation Popup with Form"
      variant="primary"
      size="large"
    >
    </ava-button>
  </div>

  <ava-confirmation-popup
    *ngIf="showPopup"
    [title]="popupData.title"
    [message]="popupData.message"
    [confirmationLabel]="popupData.confirmText"
    [show]="showPopup"
    [showForm]="popupData.showForm"
    [label]="popupData.label"
    (confirmAction)="onConfirm($event)"
    (cancelAction)="onCancel()"
    (popupClosed)="onClose()"
  >
  </ava-confirmation-popup>
</div>
