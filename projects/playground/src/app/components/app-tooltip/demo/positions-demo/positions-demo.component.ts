import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TooltipDirective } from '../../../../../../../play-comp-library/src/lib/directives/tooltip.directive';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';

@Component({
  selector: 'ava-tooltip-positions-demo',
  standalone: true,
  imports: [CommonModule, TooltipDirective, ButtonComponent],
  template: `
    <div class="demo-container">
      <div class="positions-grid">
        <ava-button
          label=" Start"
          variant="primary"
          size="small"
          state="default"
          avaTooltipDescription=" Start"
          avaTooltipType="simple"
          avaTooltipTrigger="hover"
          avaTooltipPosition="top"
          avaTooltipArrow="start"
        ></ava-button>
        <ava-button
          label=" Center"
          variant="primary"
          size="small"
          state="default"
          avaTooltipDescription=" Center"
          avaTooltipType="simple"
          avaTooltipTrigger="hover"
          avaTooltipPosition="top"
          avaTooltipArrow="center"
        ></ava-button>
        <ava-button
          label=" End"
          variant="primary"
          size="small"
          state="default"
          avaTooltipDescription=" End"
          avaTooltipType="simple"
          avaTooltipTrigger="hover"
          avaTooltipPosition="top"
          avaTooltipArrow="end"
        ></ava-button>
        <!-- <ava-button
          label="Left Start"
          variant="primary"
          size="medium"
          state="default"
          avaTooltipDescription="Left Start"
          avaTooltipType="simple"
          avaTooltipTrigger="hover"
          avaTooltipPosition="left"
          avaTooltipArrow="start"
        ></ava-button>
        <ava-button
          label="Left Center"
          variant="primary"
          size="medium"
          state="default"
          avaTooltipDescription="Left Center"
          avaTooltipType="simple"
          avaTooltipTrigger="hover"
          avaTooltipPosition="left"
          avaTooltipArrow="center"
        ></ava-button>
        <ava-button
          label="Left End"
          variant="primary"
          size="medium"
          state="default"
          avaTooltipDescription="Left End"
          avaTooltipType="simple"
          avaTooltipTrigger="hover"
          avaTooltipPosition="left"
          avaTooltipArrow="end"
        ></ava-button>
        <ava-button
          label="Right Start"
          variant="primary"
          size="medium"
          state="default"
          avaTooltipDescription="Right Start"
          avaTooltipType="simple"
          avaTooltipTrigger="hover"
          avaTooltipPosition="right"
          avaTooltipArrow="start"
        ></ava-button>
        <ava-button
          label="Right Center"
          variant="primary"
          size="medium"
          state="default"
          avaTooltipDescription="Right Center"
          avaTooltipType="simple"
          avaTooltipTrigger="hover"
          avaTooltipPosition="right"
          avaTooltipArrow="center"
        ></ava-button>
        <ava-button
          label="Right End"
          variant="primary"
          size="medium"
          state="default"
          avaTooltipDescription="Right End"
          avaTooltipType="simple"
          avaTooltipTrigger="hover"
          avaTooltipPosition="right"
          avaTooltipArrow="end"
        ></ava-button>
        <ava-button
          label="Bottom Start"
          variant="primary"
          size="medium"
          state="default"
          avaTooltipDescription="Bottom Start"
          avaTooltipType="simple"
          avaTooltipTrigger="hover"
          avaTooltipPosition="bottom"
          avaTooltipArrow="start"
        ></ava-button>
        <ava-button
          label="Bottom Center"
          variant="primary"
          size="medium"
          state="default"
          avaTooltipDescription="Bottom Center"
          avaTooltipType="simple"
          avaTooltipTrigger="hover"
          avaTooltipPosition="bottom"
          avaTooltipArrow="center"
        ></ava-button>
        <ava-button
          label="Bottom End"
          variant="primary"
          size="medium"
          state="default"
          avaTooltipDescription="Bottom End"
          avaTooltipType="simple"
          avaTooltipTrigger="hover"
          avaTooltipPosition="bottom"
          avaTooltipArrow="end"
        ></ava-button> -->
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        margin-top: 3rem;
      }
      .positions-grid {
        text-align: center;
        gap: 2rem;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
      }
    `,
  ],
})
export class TooltipPositionsDemoComponent {}
