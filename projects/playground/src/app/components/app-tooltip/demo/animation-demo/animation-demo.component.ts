import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TooltipDirective } from '../../../../../../../play-comp-library/src/lib/directives/tooltip.directive';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';
import { ToggleComponent } from '../../../../../../../play-comp-library/src/lib/components/toggle/toggle.component';

@Component({
  selector: 'ava-tooltip-animation-demo',
  standalone: true,
  imports: [CommonModule, TooltipDirective, ButtonComponent, ToggleComponent],
  template: `
    <div class="demo-container">
      <h2>Tooltip Animation</h2>
      <p class="demo-description">
        Enable or disable tooltip animation for appearance/disappearance. Toggle
        the setting below to see the difference in behavior.
      </p>

      <div class="demo-section">
        <h3>Animation Control</h3>
        <div class="animation-control">
          <ava-toggle
            [checked]="animationEnabled"
            (checkedChange)="toggleAnimation($event)"
            label="Enable Animation"
          >
          </ava-toggle>
          <span class="status-text">
            Animation:
            <strong>{{ animationEnabled ? 'Enabled' : 'Disabled' }}</strong>
          </span>
        </div>
      </div>

      <div class="demo-section">
        <h3>Test Animation</h3>
        <div class="animation-test-grid">
          <ava-button
            label="Hover Me"
            variant="primary"
            size="medium"
            avaTooltipDescription="This tooltip respects the animation setting"
            avaTooltipPosition="top"
          >
          </ava-button>

          <ava-button
            label="Test Here Too"
            variant="secondary"
            size="medium"
            avaTooltipDescription="Animation can make tooltips feel more polished and professional"
            avaTooltipPosition="bottom"
          >
          </ava-button>

          <ava-button
            label="And Here"
            variant="secondary"
            size="medium"
            avaTooltipDescription="Try toggling animation on and off to see the difference"
            avaTooltipPosition="right"
          >
          </ava-button>
        </div>
      </div>

      <div class="demo-section">
        <h3>Animation Comparison</h3>
        <div class="comparison-grid">
          <div class="comparison-item">
            <h4>With Animation</h4>
            <ava-button
              label="Animated"
              variant="success"
              size="medium"
              avaTooltipDescription="Smooth fade-in and fade-out animation"
              avaTooltipPosition="top"
            >
            </ava-button>
            <p class="comparison-description">
              Smooth transition creates a polished user experience
            </p>
          </div>

          <div class="comparison-item">
            <h4>Without Animation</h4>
            <ava-button
              label="Instant"
              variant="warning"
              size="medium"
              avaTooltipDescription="Immediate show and hide without transition"
              avaTooltipPosition="top"
            >
            </ava-button>
            <p class="comparison-description">
              Instant appearance for faster information access
            </p>
          </div>
        </div>
      </div>

      <div class="demo-section">
        <h3>Performance Considerations</h3>
        <div class="performance-info">
          <div class="info-card">
            <h4>When to Enable Animation</h4>
            <ul>
              <li>Modern devices with good performance</li>
              <li>Non-critical tooltip information</li>
              <li>Enhanced user experience is prioritized</li>
              <li>Tooltips appear infrequently</li>
            </ul>
          </div>
          <div class="info-card">
            <h4>When to Disable Animation</h4>
            <ul>
              <li>Performance-critical applications</li>
              <li>Reduced motion accessibility preference</li>
              <li>Frequent tooltip interactions</li>
              <li>Older or lower-powered devices</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="code-example">
        <h3>Code Example</h3>
        <pre><code>{{ codeExample }}</code></pre>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 2rem;
      }

      .demo-description {
        color: #666;
        margin-bottom: 30px;
        font-size: 16px;
      }

      .demo-section {
        margin-bottom: 40px;
      }

      .demo-section h3 {
        color: #333;
        margin-bottom: 20px;
        font-size: 18px;
      }

      .animation-control {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #dee2e6;
      }

      .status-text {
        color: #495057;
        font-size: 14px;
      }

      .animation-test-grid {
        display: flex;
        gap: 2rem;
        justify-content: center;
        flex-wrap: wrap;
        padding: 2rem;
      }

      .comparison-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-top: 1rem;
      }

      .comparison-item {
        text-align: center;
        padding: 1.5rem;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #dee2e6;
      }

      .comparison-item h4 {
        margin: 0 0 1rem 0;
        color: #333;
        font-size: 16px;
      }

      .comparison-description {
        margin-top: 1rem;
        color: #666;
        font-size: 14px;
        line-height: 1.4;
      }

      .performance-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        margin-top: 1rem;
      }

      .info-card {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
        border-left: 4px solid #007bff;
      }

      .info-card h4 {
        margin: 0 0 1rem 0;
        color: #333;
        font-size: 16px;
      }

      .info-card ul {
        margin: 0;
        padding-left: 1.5rem;
        color: #666;
      }

      .info-card li {
        margin-bottom: 0.5rem;
        line-height: 1.4;
      }

      .code-example {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid #007bff;
      }

      .code-example h3 {
        margin-top: 0;
        color: #333;
      }

      pre {
        background-color: #2d3748;
        color: #e2e8f0;
        padding: 16px;
        border-radius: 6px;
        overflow-x: auto;
        margin: 0;
      }

      code {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 14px;
      }

      @media (max-width: 768px) {
        .comparison-grid,
        .performance-info {
          grid-template-columns: 1fr;
        }
      }
    `,
  ],
})
export class TooltipAnimationDemoComponent {
  animationEnabled = true;

  toggleAnimation(enabled: boolean): void {
    this.animationEnabled = enabled;
  }

  codeExample = `<!-- Enable animation (default) -->
<ava-button
  label="Animated"
  avaTooltip="Smooth animation">
</ava-button>

<!-- Disable animation -->
<ava-button
  label="Instant"
  avaTooltip="No animation">
</ava-button>

<!-- Dynamic animation control -->
<ava-button
  label="Dynamic"
  avaTooltip="Animation controlled by variable">
</ava-button>`;
}
