import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TooltipDirective } from '../../../../../../../play-comp-library/src/lib/directives/tooltip.directive';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';

@Component({
  selector: 'ava-tooltip-sizes-demo',
  standalone: true,
  imports: [CommonModule, TooltipDirective, ButtonComponent],
  template: `
    <div class="demo-container">
      <h2>Tooltip Sizes</h2>
      <p class="demo-description">
        Tooltip can be sized via the size config: small, medium (default),
        large.
      </p>
      <div class="sizes-grid">
        <ava-button
          label="Small Tooltip"
          variant="primary"
          size="medium"
          state="default"
          avaTooltipDescription="Small Tooltip"
          avaTooltipType="simple"
          avaTooltipTrigger="hover"
          avaTooltipPosition="top"
          avaTooltipSize="small"
        ></ava-button>
        <ava-button
          label="Medium Tooltip"
          variant="primary"
          size="medium"
          state="default"
          avaTooltipDescription="Medium Tooltip"
          avaTooltipType="simple"
          avaTooltipTrigger="hover"
          avaTooltipPosition="top"
          avaTooltipSize="medium"
        ></ava-button>
        <ava-button
          label="Large Tooltip"
          variant="primary"
          size="medium"
          state="default"
          avaTooltipDescription="Large Tooltip"
          avaTooltipType="simple"
          avaTooltipTrigger="hover"
          avaTooltipPosition="top"
          avaTooltipSize="large"
        ></ava-button>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 2rem;
      }
      .demo-description {
        color: #666;
        margin-bottom: 30px;
        font-size: 16px;
      }
      .sizes-grid {
        display: flex;
        gap: 2rem;
        align-items: center;
        justify-content: flex-start;
        margin-top: 2rem;
      }
    `,
  ],
})
export class TooltipSizesDemoComponent {}
