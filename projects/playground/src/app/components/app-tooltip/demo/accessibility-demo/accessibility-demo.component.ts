import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TooltipDirective } from '../../../../../../../play-comp-library/src/lib/directives/tooltip.directive';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';
import { AvaTextboxComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-tooltip-accessibility-demo',
  standalone: true,
  imports: [CommonModule, TooltipDirective, ButtonComponent, AvaTextboxComponent],
  template: `
    <div class="demo-container">
      <h2>Tooltip Accessibility</h2>
      <p class="demo-description">
        Tooltip follows accessibility best practices with keyboard navigation,
        proper ARIA attributes, screen reader support, focus management, and
        high contrast support.
      </p>

      <div class="demo-section">
        <h3>Keyboard Navigation</h3>
        <div class="keyboard-demo">
          <p class="instruction">
            Use Tab key to navigate through these elements and see tooltips
            appear on focus:
          </p>
          <div class="keyboard-elements">
            <ava-textbox
              label="Username"
              placeholder="Enter username"
              avaTooltipDescription="Username must be 3-20 characters long"
              avaTooltipTrigger="focus"
              avaTooltipPosition="right"
            >
            </ava-textbox>

            <ava-textbox
              label="Email"
              placeholder="Enter email"
              type="email"
              avaTooltipDescription="We'll use this email for important notifications"
              avaTooltipTrigger="focus"
              avaTooltipPosition="right"
            >
            </ava-textbox>

            <ava-button
              label="Submit"
              variant="primary"
              size="medium"
              avaTooltipDescription="Submit the form data (Keyboard: Enter or Space)"
              avaTooltipTrigger="focus"
              avaTooltipPosition="top"
            >
            </ava-button>

            <ava-button
              label="Cancel"
              variant="secondary"
              size="medium"
              avaTooltipDescription="Cancel and return to previous page (Keyboard: Enter or Space)"
              avaTooltipTrigger="focus"
              avaTooltipPosition="top"
            >
            </ava-button>
          </div>
        </div>
      </div>

      <div class="demo-section">
        <h3>ARIA Attributes & Screen Reader Support</h3>
        <div class="aria-demo">
          <p class="instruction">
            These elements have proper ARIA attributes for screen readers:
          </p>
          <div class="aria-elements">
            <button
              class="accessible-button"
              avaTooltipDescription="This button has proper ARIA labeling and description"
              avaTooltipTrigger="focus"
              avaTooltipPosition="top"
              aria-label="Save document"
              aria-describedby="save-tooltip"
            >
              💾 Save
            </button>

            <button
              class="accessible-button"
              avaTooltipDescription="Delete permanently removes the item - this action cannot be undone"
              avaTooltipTrigger="focus"
              avaTooltipPosition="top"
              aria-label="Delete item"
              aria-describedby="delete-tooltip"
            >
              🗑️ Delete
            </button>

            <div class="form-field">
              <label for="accessible-input">Required Field</label>
              <input
                id="accessible-input"
                type="text"
                class="accessible-input"
                aria-required="true"
                aria-describedby="field-help"
                avaTooltipDescription="This field is required and must contain at least 5 characters"
                avaTooltipTrigger="focus"
                avaTooltipPosition="right"
                placeholder="Minimum 5 characters"
              />
            </div>
          </div>
        </div>
      </div>

      <div class="demo-section">
        <h3>Focus Management</h3>
        <div class="focus-demo">
          <p class="instruction">
            Focus management ensures tooltips don't interfere with navigation:
          </p>
          <div class="focus-elements">
            <button
              class="focus-button"
              avaTooltipDescription="Focus remains on this button while tooltip is visible"
              avaTooltipTrigger="focus"
              avaTooltipPosition="top"
            >
              Focus stays here
            </button>

            <button
              class="focus-button"
              avaTooltipDescription="Tab to move focus to next element"
              avaTooltipTrigger="focus"
              avaTooltipPosition="top"
            >
              Tab continues here
            </button>

            <button
              class="focus-button"
              avaTooltipDescription="Shift+Tab moves focus backward"
              avaTooltipTrigger="focus"
              avaTooltipPosition="top"
            >
              And continues here
            </button>
          </div>
        </div>
      </div>

      <div class="demo-section">
        <h3>Accessibility Checklist</h3>
        <div class="checklist">
          <div class="checklist-item">
            <span class="checkmark">✅</span>
            <span>Keyboard accessible (focus trigger)</span>
          </div>
          <div class="checklist-item">
            <span class="checkmark">✅</span>
            <span>Proper ARIA attributes (aria-describedby, role)</span>
          </div>
          <div class="checklist-item">
            <span class="checkmark">✅</span>
            <span>Screen reader compatible</span>
          </div>
          <div class="checklist-item">
            <span class="checkmark">✅</span>
            <span>Focus management maintained</span>
          </div>
          <div class="checklist-item">
            <span class="checkmark">✅</span>
            <span>High contrast support</span>
          </div>
          <div class="checklist-item">
            <span class="checkmark">✅</span>
            <span>Reduced motion support</span>
          </div>
          <div class="checklist-item">
            <span class="checkmark">✅</span>
            <span>Color contrast compliance</span>
          </div>
          <div class="checklist-item">
            <span class="checkmark">✅</span>
            <span>Touch-friendly on mobile</span>
          </div>
        </div>
      </div>

      <div class="demo-section">
        <h3>Best Practices</h3>
        <div class="best-practices">
          <div class="practice-card">
            <h4>Do</h4>
            <ul>
              <li>Use focus trigger for form elements</li>
              <li>Provide alternative ways to access information</li>
              <li>Keep tooltip content concise</li>
              <li>Test with screen readers</li>
              <li>Support keyboard navigation</li>
              <li>Respect user motion preferences</li>
            </ul>
          </div>
          <div class="practice-card">
            <h4>Don't</h4>
            <ul>
              <li>Put critical information only in tooltips</li>
              <li>Use hover-only tooltips for important content</li>
              <li>Make tooltips too long or complex</li>
              <li>Ignore high contrast mode</li>
              <li>Prevent focus from moving naturally</li>
              <li>Force animations on users who prefer reduced motion</li>
            </ul>
          </div>
        </div>
      </div>

      <div class="code-example">
        <h3>Code Example</h3>
        <pre><code>{{ codeExample }}</code></pre>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 2rem;
      }

      .demo-description {
        color: #666;
        margin-bottom: 30px;
        font-size: 16px;
      }

      .demo-section {
        margin-bottom: 40px;
      }

      .demo-section h3 {
        color: #333;
        margin-bottom: 20px;
        font-size: 18px;
      }

      .instruction {
        color: #666;
        font-style: italic;
        margin-bottom: 1rem;
        padding: 0.75rem;
        background-color: #e3f2fd;
        border-radius: 6px;
        border-left: 4px solid #2196f3;
      }

      .keyboard-demo,
      .aria-demo,
      .contrast-demo,
      .focus-demo {
        padding: 1.5rem;
        background-color: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #007bff;
      }

      .keyboard-elements,
      .aria-elements,
      .focus-elements {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
        align-items: end;
      }

      .accessible-button {
        padding: 8px 16px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 0.5rem;
      }

      .accessible-button:focus {
        outline: 2px solid #0056b3;
        outline-offset: 2px;
      }

      .form-field {
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
      }

      .form-field label {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }

      .accessible-input {
        padding: 8px 12px;
        border: 2px solid #dee2e6;
        border-radius: 4px;
        font-size: 14px;
      }

      .accessible-input:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
      }

      .accessibility-options {
        display: flex;
        gap: 2rem;
        margin-bottom: 1.5rem;
        flex-wrap: wrap;
      }

      .contrast-examples {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
      }

      .contrast-examples.high-contrast {
        filter: contrast(150%) brightness(110%);
      }

      .focus-button {
        padding: 12px 20px;
        background-color: #6c757d;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.2s ease;
      }

      .focus-button:focus {
        outline: 3px solid #007bff;
        outline-offset: 2px;
        background-color: #5a6268;
      }

      .focus-button:hover {
        background-color: #5a6268;
      }

      .reduce-motion * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
      }

      .checklist {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 0.75rem;
      }

      .checklist-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem;
        background-color: #e8f5e8;
        border-radius: 6px;
        border-left: 4px solid #4caf50;
      }

      .checkmark {
        font-size: 16px;
        color: #4caf50;
        font-weight: bold;
      }

      .best-practices {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
      }

      .practice-card {
        padding: 1.5rem;
        border-radius: 8px;
        border-left: 4px solid;
      }

      .practice-card:first-child {
        background-color: #e8f5e8;
        border-left-color: #4caf50;
      }

      .practice-card:last-child {
        background-color: #ffebee;
        border-left-color: #f44336;
      }

      .practice-card h4 {
        margin: 0 0 1rem 0;
        color: #333;
        font-size: 16px;
      }

      .practice-card ul {
        margin: 0;
        padding-left: 1.5rem;
        color: #666;
      }

      .practice-card li {
        margin-bottom: 0.5rem;
        line-height: 1.4;
      }

      .code-example {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid #007bff;
      }

      .code-example h3 {
        margin-top: 0;
        color: #333;
      }

      pre {
        background-color: #2d3748;
        color: #e2e8f0;
        padding: 16px;
        border-radius: 6px;
        overflow-x: auto;
        margin: 0;
      }

      code {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 14px;
      }

      @media (max-width: 768px) {
        .keyboard-elements,
        .aria-elements,
        .focus-elements,
        .contrast-examples {
          flex-direction: column;
          align-items: stretch;
        }

        .best-practices {
          grid-template-columns: 1fr;
        }

        .accessibility-options {
          flex-direction: column;
          gap: 1rem;
        }
      }

      @media (prefers-contrast: high) {
        .demo-container {
          filter: contrast(150%);
        }
      }

      @media (prefers-reduced-motion: reduce) {
        .reduce-motion * {
          animation-duration: 0.01ms !important;
          animation-iteration-count: 1 !important;
          transition-duration: 0.01ms !important;
        }
      }
    `,
  ],
})
export class TooltipAccessibilityDemoComponent {
  highContrastMode = false;
  reduceMotion = false;

  toggleHighContrast(enabled: boolean): void {
    this.highContrastMode = enabled;
  }

  toggleReduceMotion(enabled: boolean): void {
    this.reduceMotion = enabled;
  }

  codeExample = `<!-- Keyboard accessible tooltip -->
<ava-textbox
  label="Username"
  avaTooltipDescription="Username requirements"
  avaTooltipTrigger="focus"
  aria-describedby="username-help">
</ava-textbox>

<!-- Button with proper ARIA -->
<button
  avaTooltipDescription="Save document"
  avaTooltipTrigger="focus"
  aria-label="Save document"
  aria-describedby="save-tooltip">
  Save
</button>

<!-- Respect motion preferences -->
<ava-button
  avaTooltipDescription="Motion-aware tooltip"
  [avaTooltipEnableAnimation]="!reduceMotion">
</ava-button>

<!-- High contrast support -->
<div class="high-contrast-container">
  <ava-button
    avaTooltipDescription="Adapts to contrast settings"
    avaTooltipVariant="info">
  </ava-button>
</div>`;
}
