import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TooltipDirective } from '../../../../../../../play-comp-library/src/lib/directives/tooltip.directive';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';

@Component({
  selector: 'ava-tooltip-variants-demo',
  standalone: true,
  imports: [CommonModule, TooltipDirective, ButtonComponent],
  template: `
    <div class="demo-container">
      <ava-button
        label="Title Only"
        variant="secondary"
        size="small"
        avaTooltipTitle="Heading"
        avaTooltipPosition="top"
        avaTooltipArrow="center"
      >
      </ava-button>
      <ava-button
        label="Desc Only"
        variant="secondary"
        size="small"
        avaTooltipDescription="This description should start from beginning"
        avaTooltipPosition="top"
        avaTooltipArrow="center"
      >
      </ava-button>
      <ava-button
        label="Desc+Icon Only"
        variant="secondary"
        size="small"
        avaTooltipDescription="This description should start from beginning"
        avaTooltipIcon="info"
        avaTooltipIconColor="var(--color-text-primary)"
        avaTooltipPosition="top"
        avaTooltipArrow="center"
      >
      </ava-button>
      <ava-button
        label="Title+Icon+Desc"
        variant="secondary"
        size="small"
        avaTooltipTitle="Save Feature"
        avaTooltipDescription="This description should align with title text"
        avaTooltipIcon="save"
        avaTooltipIconColor="var(--color-text-primary)"
        avaTooltipPosition="top"
        avaTooltipArrow="center"
      >
      </ava-button>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        margin-top: 3rem;
        text-align: center;
      }

      .demo-container ava-button {
        display: inline-block;
        margin: 0.5rem;
      }

      .demo-description {
        color: #666;
        margin-bottom: 30px;
        font-size: 16px;
      }

      .demo-section {
        margin-bottom: 40px;
      }

      .demo-section h3 {
        color: #333;
        margin-bottom: 20px;
        font-size: 18px;
      }

      .variants-grid {
        gap: 2rem;
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
        padding: 2rem;
      }

      .variant-demo {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
      }

      .variant-label {
        font-size: 12px;
        color: #666;
        text-align: center;
        font-weight: 500;
      }

      .use-cases {
        display: grid;
        grid-template-columns: 1fr;
        gap: 2rem;
      }

      .use-case {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 8px;
        border-left: 4px solid #007bff;
      }

      .use-case h4 {
        margin: 0 0 1rem 0;
        color: #333;
        font-size: 16px;
      }

      .form-demo {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
      }

      .demo-input {
        padding: 8px 12px;
        border: 2px solid #dee2e6;
        border-radius: 4px;
        font-size: 14px;
        max-width: 250px;
      }

      .demo-input.valid {
        border-color: #28a745;
      }

      .demo-input.warning {
        border-color: #ffc107;
      }

      .demo-input.error {
        border-color: #dc3545;
      }

      .status-demo {
        display: flex;
        gap: 1.5rem;
        align-items: center;
      }

      .status-icon {
        font-size: 24px;
        cursor: pointer;
        padding: 4px;
        border-radius: 50%;
        display: inline-flex;
        align-items: center;
        justify-content: center;
      }

      .action-demo {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
      }

      .guidelines-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1rem;
      }

      .guideline-card {
        padding: 1.5rem;
        border-radius: 8px;
        border-left: 4px solid;
      }

      .guideline-card.default {
        background-color: #f8f9fa;
        border-left-color: #6c757d;
      }

      .guideline-card.info {
        background-color: #e3f2fd;
        border-left-color: #2196f3;
      }

      .guideline-card.success {
        background-color: #e8f5e8;
        border-left-color: #4caf50;
      }

      .guideline-card.warning {
        background-color: #fff8e1;
        border-left-color: #ff9800;
      }

      .guideline-card.error {
        background-color: #ffebee;
        border-left-color: #f44336;
      }

      .guideline-card h4 {
        margin: 0 0 1rem 0;
        color: #333;
        font-size: 16px;
      }

      .guideline-card p {
        margin: 0.5rem 0;
        color: #666;
        font-size: 14px;
        line-height: 1.4;
      }

      .code-example {
        background-color: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        border-left: 4px solid #007bff;
      }

      .code-example h3 {
        margin-top: 0;
        color: #333;
      }

      pre {
        background-color: #2d3748;
        color: #e2e8f0;
        padding: 16px;
        border-radius: 6px;
        overflow-x: auto;
        margin: 0;
      }

      code {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 14px;
      }

      @media (max-width: 768px) {
        .variants-grid,
        .action-demo {
          flex-direction: column;
          align-items: center;
        }

        .status-demo {
          justify-content: center;
        }
      }
    `,
  ],
})
export class TooltipVariantsDemoComponent {
  codeExample = `<!-- Default variant -->
<ava-button
  label="Default"
  avaTooltipDescription="General information"
  avaTooltipVariant="default">
</ava-button>`;
}
