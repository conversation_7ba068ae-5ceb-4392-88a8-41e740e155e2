// .documentation {
//   max-width: 1200px;
//   margin: 0 auto;
//   padding: 2rem;
//   font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;

//   .doc-header {
//     margin-bottom: 3rem;
//     padding-bottom: 2rem;
//     border-bottom: 1px solid #eaeaea;

//     h1 {
//       font-size: 2.5rem;
//       font-weight: 600;
//       color: #333;
//       margin-bottom: 1rem;
//     }

//     .description {
//       font-size: 1.1rem;
//       color: #666;
//       line-height: 1.6;
//     }
//   }

// .doc-section {
//   margin-bottom: 3rem;

//   h2 {
//     font-size: 1.8rem;
//     font-weight: 500;
//     color: #333;
//     margin-bottom: 1.5rem;
//   }

//   .section-header {
//     display: flex;
//     flex-direction: column;
//     position: relative;
//     cursor: pointer;
//     padding: 1rem;
//     background-color: var(--surface);
//     border-radius: var(--border-radius);

//     h2 {
//       margin-bottom: 0.5rem;
//     }

//     .description-container {
//       display: flex;
//       justify-content: space-between;
//       align-items: center;
//       flex-wrap: wrap;
//       gap: 0.5rem;
//     }

// .code-toggle {
//   font-size: 0.75rem;
//   color: var(--icons-action);
//   cursor: pointer;
//   display: flex;
//   align-items: center;
//   font-weight: var(--font-font-weight-medium);
//   font-family: var(--font-font-family-heading);

//   &:hover {
//     text-decoration: underline;
//   }

//   span {
//     margin-right: 0.5rem;
//   }

//   awe-icons {
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     width: 24px;
//     height: 24px;

//     svg {
//       width: 60%;
//       height: 80%;
//       display: block;
//     }
//   }
// }

.ava-text {
  width: 50%;
}

.code-example {
  margin-top: 1.5rem;
  // border: 1px solid #eaeaea;
  // border-radius: 8px;
  overflow: hidden;

  .example-preview {
    padding: 2rem;
    // background-color: #fff;
    display: flow;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
  }

  .code-block {
    padding: 1.5rem;
    // background-color: var(--surface);
    border-top: 1px solid var(--border-color);
    position: relative;
    text-align: justify;

    .code-content {
      overflow-x: auto;

      pre {
        margin: 0;
        overflow-x: auto;

        code {
          font-family: "Fira Code", monospace;
          font-size: 0.9rem;
          line-height: 1.5;
        }
      }
    }
  }
}

//     .copy-button {
//       position: absolute;
//       top: 0.5rem;
//       right: 0.5rem;
//       padding: 0.5rem;
//       background: transparent;
//       border: none;
//       cursor: pointer;
//       color: var(--text-color-secondary);

//       &:hover {
//         color: var(--primary-color);
//       }
//     }
//   }

//   &.expanded {
//     .code-block {
//       display: block;
//     }
//   }
// }

// .api-table {
//   width: 100%;
//   border-collapse: collapse;
//   margin-top: 1rem;

//   th, td {
//     padding: 1rem;
//     text-align: left;
//     border-bottom: 1px solid #eaeaea;
//   }

//   th {
//     font-weight: 600;
//     background-color: #f8f9fa;
//   }

//   td {
//     code {
//       background-color: #f1f3f5;
//       padding: 0.2rem 0.4rem;
//       border-radius: 4px;
//       font-family: 'Fira Code', monospace;
//       font-size: 0.9rem;
//     }
//   }

//   tr:last-child td {
//     border-bottom: none;
//   }
// }

:host {
  display: block;
  width: 100%;
}

/* Prevent horizontal scroll */
html,
body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
}

* {
  box-sizing: border-box;
}

/* Main layout */
.documentation {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
  overflow-x: hidden;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

/* Header */
.doc-header {
  // border-bottom: 1px solid var(--neutral-200);

  h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
  }

  .description {
    font-size: 1.1rem;
    // color: var(--text-color-secondary);
    line-height: 1.6;
  }
}

/* Sections */
.doc-sections {
  margin-top: 4rem;
}

.doc-section {
  margin-bottom: 1rem;

  h2 {
    font-size: 1.8rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
  }

  p {
    // color: var(--text-color-secondary);
    font-size: 0.95rem;
    line-height: 1.5;
  }
}

/* Section header with toggle */
.section-header {
  display: flex;
  flex-direction: column;
  position: relative;
  cursor: pointer;
  padding: 1rem;
  background-color: var(--surface);
  border-radius: var(--border-radius);

  h2 {
    margin-bottom: 0.5rem;
  }

  .description-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .code-toggle {
    font-size: 0.75rem;
    color: var(--icons-action);
    cursor: pointer;
    display: flex;
    align-items: center;
    font-weight: var(--font-font-weight-medium);
    font-family: var(--font-font-family-heading);

    &:hover {
      text-decoration: underline;
    }

    span {
      margin-right: 0.5rem;
    }

    awe-icons {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      line-height: 0;
      padding: 0;
      margin: 0;
      vertical-align: middle;
      flex-shrink: 0;

      svg {
        width: 60%;
        height: 80%;
        display: block;
      }
    }
  }
}

/* Code example styles */
.code-example {
  margin-top: 1.5rem;

  .example-preview {
    padding: 1.5rem;
    border: 1px solid var(--surface-border);
    border-radius: var(--border-radius);
    // background-color: var(--surface-section);
    margin-bottom: 1rem;
  }

  .code-block {
    position: relative;
    // background-color: var(--surface-section);
    border-radius: 0.5rem;
    padding: 1rem;
    margin-top: 1rem;

    pre {
      margin: 0;
      // padding: 1rem;
      background-color: var(--surface-ground);
      border-radius: 0.25rem;
      overflow-x: auto;
      // border: 1px solid var(--border-color); /* Add border to code blocks */
    }

    .copy-button {
      position: absolute;
      top: 0.5rem;
      right: 0.5rem;
      padding: 0.5rem;
      background: transparent;
      border: none;
      cursor: pointer;
      color: var(--text-color-secondary);

      &:hover {
        color: var(--primary-color);
      }
    }
  }
}

/* API table styles */
.api-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;

  th,
  td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--surface-border);
  }

  th {
    background-color: var(--surface);
    font-weight: 600;
    color: var(--text-color-primary);
  }

  td {
    // color: var(--text-color-secondary);

    code {
      background-color: var(--surface);
      padding: 0.2rem 0.4rem;
      border-radius: var(--border-radius-sm);
      font-family: monospace;
    }
  }
}

/* Viewport Controls */
.viewport-tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  border-bottom: 1px solid var(--neutral-200);
  padding-bottom: 0.5rem;

  .viewport-tab {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background: none;
    color: var(--neutral-600);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      color: var(--primary-600);
    }

    &.active {
      color: var(--primary-600);
      border-bottom: 2px solid var(--primary-600);
    }

    awe-icons {
      font-size: 1rem;
    }
  }
}

/* Viewport Preview */
.viewport-preview {
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: 1rem;
  margin: 1rem auto;
  background: var(--surface);

  .preview-section {
    margin-bottom: 1rem;

    h4 {
      font-size: 1rem;
      color: #000000;
      margin-bottom: 1.5rem;
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}

/* Grid Examples */
.grid-examples {
  margin-top: 2rem;

  .grid-section {
    margin-bottom: 2rem;

    h3 {
      font-size: 1.25rem;
      color: #000000;
      margin-bottom: 0.5rem;
    }

    .example-description {
      font-size: 0.9rem;
      color: #000000;
      margin-bottom: 1.5rem;
    }
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .documentation {
    padding: 1rem;
  }

  .viewport-preview {
    padding: 1rem;
    margin: 1rem auto;
  }

  .preview-section {
    margin-bottom: 1rem;
  }

  .viewport-controls {
    flex-wrap: wrap;
  }
}

/* Demo Navigation Styles */
.tooltip-demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.tooltip-demo-container h1 {
  text-align: center;
  color: #333;
  margin-bottom: 1rem;
  font-size: 2.5rem;
  font-weight: 700;
}

.demo-intro {
  text-align: center;
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 3rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
}

.demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.demo-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e1e5e9;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
  display: block;
}

.demo-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #007bff;
}

.demo-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.demo-card h3 {
  margin: 0;
  color: #333;
  font-size: 1.25rem;
  font-weight: 600;
}

.demo-badge {
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.demo-description {
  color: #666;
  line-height: 1.5;
  margin-bottom: 1.5rem;
  font-size: 0.95rem;
}

.demo-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-top: auto;
}

/* Preview Specific Styles */
.preview-tooltip {
  font-size: 2rem;
  opacity: 0.7;
}

.preview-positions {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr;
  gap: 8px;
  width: 60px;
  height: 60px;
}

.pos-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #007bff;
}

.pos-indicator.top {
  grid-column: 2;
  grid-row: 1;
}
.pos-indicator.right {
  grid-column: 3;
  grid-row: 2;
}
.pos-indicator.bottom {
  grid-column: 2;
  grid-row: 3;
}
.pos-indicator.left {
  grid-column: 1;
  grid-row: 2;
}

.preview-sizes {
  display: flex;
  gap: 8px;
  align-items: center;
}

.size-demo {
  background: #007bff;
  color: white;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.size-demo.small {
  width: 20px;
  height: 20px;
  font-size: 10px;
}
.size-demo.medium {
  width: 30px;
  height: 30px;
  font-size: 14px;
}
.size-demo.large {
  width: 40px;
  height: 40px;
  font-size: 18px;
}

.preview-animation {
  font-size: 2rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
}

.preview-behaviors {
  display: flex;
  gap: 12px;
}

.behavior-icon {
  font-size: 1.5rem;
  opacity: 0.7;
}

.preview-variants {
  display: flex;
  gap: 8px;
}

.variant-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.variant-dot.default {
  background: #6c757d;
}



.preview-accessibility {
  font-size: 2rem;
  opacity: 0.7;
}

/* Code Preview Section */
.code-preview-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 2rem;
  border-left: 4px solid #007bff;
}

.code-preview-section h2 {
  margin: 0 0 1rem 0;
  color: #333;
  font-size: 1.5rem;
}

.code-preview {
  background: #2d3748;
  border-radius: 8px;
  overflow: hidden;
}

.code-preview pre {
  margin: 0;
  padding: 1.5rem;
  overflow-x: auto;
}

.code-preview code {
  color: #e2e8f0;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 14px;
  line-height: 1.5;
}

/* Original Content Spacing */
.original-content {
  margin-top: 4rem;
  border-top: 2px solid #e1e5e9;
  padding-top: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tooltip-demo-container {
    padding: 1rem;
  }

  .tooltip-demo-container h1 {
    font-size: 2rem;
  }

  .demo-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .demo-card {
    padding: 1.25rem;
  }

  .preview-positions {
    width: 50px;
    height: 50px;
  }
}

@media (max-width: 480px) {
  .demo-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .demo-badge {
    align-self: flex-start;
  }
}
