<div class="tooltip-demo-container">
  <h1>Tooltip Component Demos</h1>
  <p class="demo-intro">
    Explore different aspects of the Tooltip component with interactive demos.
    Each demo showcases specific features and usage patterns.
  </p>

  <div class="demo-grid">
    <div class="demo-card" routerLink="/tooltip/basic-usage">
      <div class="demo-card-header">
        <h3>Basic Usage</h3>
        <span class="demo-badge">Fundamental</span>
      </div>
      <p class="demo-description">
        Simple tooltip attached to any element with hover interactions and basic
        functionality.
      </p>
      <div class="demo-preview">
        <div class="preview-tooltip">💬</div>
      </div>
    </div>

    <div class="demo-card" routerLink="/tooltip/positions">
      <div class="demo-card-header">
        <h3>Positions</h3>
        <span class="demo-badge">Layout</span>
      </div>
      <p class="demo-description">
        Tooltips positioned relative to target: top, bottom, left, right with
        arrow alignment options.
      </p>
      <div class="demo-preview">
        <div class="preview-positions">
          <span class="pos-indicator top">▲</span>
          <span class="pos-indicator right">▶</span>
          <span class="pos-indicator bottom">▼</span>
          <span class="pos-indicator left">◀</span>
        </div>
      </div>
    </div>

    <div class="demo-card" routerLink="/tooltip/sizes">
      <div class="demo-card-header">
        <h3>Sizes</h3>
        <span class="demo-badge">Styling</span>
      </div>
      <p class="demo-description">
        Different tooltip sizes: small, medium, large to accommodate various
        content lengths.
      </p>
      <div class="demo-preview">
        <div class="preview-sizes">
          <div class="size-demo small">S</div>
          <div class="size-demo medium">M</div>
          <div class="size-demo large">L</div>
        </div>
      </div>
    </div>

    <div class="demo-card" routerLink="/tooltip/animation">
      <div class="demo-card-header">
        <h3>Animation</h3>
        <span class="demo-badge">Interaction</span>
      </div>
      <p class="demo-description">
        Enable or disable tooltip animations with smooth fade-in and fade-out
        effects.
      </p>
      <div class="demo-preview">
        <div class="preview-animation">✨</div>
      </div>
    </div>

    <div class="demo-card" routerLink="/tooltip/behaviors">
      <div class="demo-card-header">
        <h3>Behaviors</h3>
        <span class="demo-badge">Interaction</span>
      </div>
      <p class="demo-description">
        Different trigger behaviors: hover, focus, click, and manual control for
        various use cases.
      </p>
      <div class="demo-preview">
        <div class="preview-behaviors">
          <span class="behavior-icon">🖱️</span>
          <span class="behavior-icon">⌨️</span>
        </div>
      </div>
    </div>

    <div class="demo-card" routerLink="/tooltip/variants">
      <div class="demo-card-header">
        <h3>Variants</h3>
        <span class="demo-badge">Semantic</span>
      </div>
      <p class="demo-description">
        Default tooltip variant with standard styling.
      </p>
      <div class="demo-preview">
        <div class="preview-variants">
          <div class="variant-dot default"></div>
        </div>
      </div>
    </div>

    <div class="demo-card" routerLink="/tooltip/accessibility">
      <div class="demo-card-header">
        <h3>Accessibility</h3>
        <span class="demo-badge">A11y</span>
      </div>
      <p class="demo-description">
        Keyboard navigation, ARIA attributes, screen reader support, and
        accessibility best practices.
      </p>
      <div class="demo-preview">
        <div class="preview-accessibility">♿</div>
      </div>
    </div>
  </div>

  <!-- Code snippet preview -->
  <div class="code-preview-section">
    <h2>Quick Start</h2>
    <div class="code-preview">
      <pre><code>{{ quickStartCode }}</code></pre>
    </div>
  </div>
</div>

<!-- Original tooltip demos (keep existing content) -->
<div class="original-content">
  <div class="documentation">
    <!-- Header -->
    <header class="doc-header">
      <h1>Tooltip Component</h1>
      <p class="description">
        The Tooltip component provides a versatile way to display contextual
        hints or information when users hover over or focus on elements. It
        supports various positions, sizes, animations, and behaviors.
      </p>
    </header>

    <!-- Installation -->
    <section class="doc-section">
      <h2>Installation</h2>
      <div class="code-block">
        <pre><code>import {{ '{' }} TooltipComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
      </div>
    </section>

    <!-- Documentation Sections -->
    <div class="doc-sections">
      <section
        *ngFor="let section of sections; let i = index"
        class="doc-section"
      >
        <div class="row">
          <div class="col-12">
            <div class="section-header" tabindex="0" role="button">
              <h2>{{ section.title }}</h2>
              <div class="description-container">
                <p>{{ section.description }}</p>
                <div
                  class="code-toggle"
                  (click)="toggleCodeVisibility(i, $event)"
                >
                  <span *ngIf="!section.showCode">View Code</span>
                  <span *ngIf="section.showCode">Hide Code</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Code Example -->
        <div class="code-example" [class.expanded]="section.showCode">
          <div class="example-preview">
            <ng-container [ngSwitch]="section.title">
              <!-- Basic Usage -->
              <ng-container *ngSwitchCase="'Basic Usage'">
                <ava-button
                  label="Tooltip with Hover"
                  variant="primary"
                  size="medium"
                  state="default"
                  avaTooltipTitle="Hover Tooltip"
                  avaTooltipDescription="This tooltip appears when you hover over the button"
                  avaTooltipType="simple"
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="top"
                  avaTooltipArrow="center"
                >
                </ava-button>
                <ava-button
                  label="Tooltip with Click"
                  variant="primary"
                  size="medium"
                  state="default"
                  avaTooltipTitle="Click Tooltip"
                  avaTooltipDescription="This tooltip appears when you click the button"
                  avaTooltipType="simple"
                  avaTooltipTrigger="click"
                  avaTooltipPosition="top"
                  avaTooltipArrow="center"
                >
                </ava-button>
              </ng-container>

              <!-- Tooltip Positions -->
              <ng-container *ngSwitchCase="'Tooltip Positions'">
                <ava-button
                  label="Top Start"
                  variant="primary"
                  size="medium"
                  state="default"
                  avaTooltipTitle="Top Start Position"
                  avaTooltipDescription="Tooltip positioned at the top with arrow aligned to start"
                  avaTooltipType="simple"
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="top"
                  avaTooltipArrow="start"
                >
                </ava-button>
                <ava-button
                  label="Top Center"
                  variant="primary"
                  size="medium"
                  state="default"
                  avaTooltipTitle="Top Center Position"
                  avaTooltipDescription="Tooltip positioned at the top with arrow centered"
                  avaTooltipType="simple"
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="top"
                  avaTooltipArrow="center"
                >
                </ava-button>
                <ava-button
                  label="Top End"
                  variant="primary"
                  size="medium"
                  state="default"
                  avaTooltipTitle="Top End Position"
                  avaTooltipDescription="Tooltip positioned at the top with arrow aligned to end"
                  avaTooltipType="simple"
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="top"
                  avaTooltipArrow="end"
                >
                </ava-button>

                <ava-button
                  label="Left Start"
                  variant="primary"
                  size="medium"
                  state="default"
                  avaTooltipTitle="Left Start Position"
                  avaTooltipDescription="Tooltip positioned to the left with arrow aligned to start"
                  avaTooltipType="simple"
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="left"
                  avaTooltipArrow="start"
                >
                </ava-button>
                <ava-button
                  label="Left Center"
                  variant="primary"
                  size="medium"
                  state="default"
                  avaTooltipTitle="Left Center Position"
                  avaTooltipDescription="Tooltip positioned to the left with arrow centered"
                  avaTooltipType="simple"
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="left"
                  avaTooltipArrow="center"
                >
                </ava-button>
                <ava-button
                  label="Left End"
                  variant="primary"
                  size="medium"
                  state="default"
                  avaTooltipTitle="Left End Position"
                  avaTooltipDescription="Tooltip positioned to the left with arrow aligned to end"
                  avaTooltipType="simple"
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="left"
                  avaTooltipArrow="end"
                >
                </ava-button>

                <ava-button
                  label="Right Start"
                  variant="primary"
                  size="medium"
                  state="default"
                  avaTooltipTitle="Right Start Position"
                  avaTooltipDescription="Tooltip positioned to the right with arrow aligned to start"
                  avaTooltipType="simple"
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="right"
                  avaTooltipArrow="start"
                >
                </ava-button>
                <ava-button
                  label="Right Center"
                  variant="primary"
                  size="medium"
                  state="default"
                  avaTooltipTitle="Right Center Position"
                  avaTooltipDescription="Tooltip positioned to the right with arrow centered"
                  avaTooltipType="simple"
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="right"
                  avaTooltipArrow="center"
                >
                </ava-button>
                <ava-button
                  label="Right End"
                  variant="primary"
                  size="medium"
                  state="default"
                  avaTooltipTitle="Right End Position"
                  avaTooltipDescription="Tooltip positioned to the right with arrow aligned to end"
                  avaTooltipType="simple"
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="right"
                  avaTooltipArrow="end"
                >
                </ava-button>

                <ava-button
                  label="Bottom Start"
                  variant="primary"
                  size="medium"
                  state="default"
                  avaTooltipTitle="Bottom Start Position"
                  avaTooltipDescription="Tooltip positioned at the bottom with arrow aligned to start"
                  avaTooltipType="simple"
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="bottom"
                  avaTooltipArrow="start"
                >
                </ava-button>
                <ava-button
                  label="Bottom Center"
                  variant="primary"
                  size="medium"
                  state="default"
                  avaTooltipTitle="Bottom Center Position"
                  avaTooltipDescription="Tooltip positioned at the bottom with arrow centered"
                  avaTooltipType="simple"
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="bottom"
                  avaTooltipArrow="center"
                >
                </ava-button>
                <ava-button
                  label="Bottom End"
                  variant="primary"
                  size="medium"
                  state="default"
                  avaTooltipTitle="Bottom End Position"
                  avaTooltipDescription="Tooltip positioned at the bottom with arrow aligned to end"
                  avaTooltipType="simple"
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="bottom"
                  avaTooltipArrow="end"
                >
                </ava-button>
              </ng-container>

              <!-- Tooltip Sizes -->
              <!-- <ng-container *ngSwitchCase="'Tooltip Sizes'">
              <ava-button label="Small Tooltip" variant="primary" size="medium" state="default"
                avaTooltipDescription="Small Tooltip" avaTooltipType="simple" avaTooltipTrigger="hover" avaTooltipPosition="top"
                avaTooltipSize="small" avaTooltipArrow="center">
              </ava-button>
              <ava-button label="Medium Tooltip" variant="primary" size="medium" state="default"
                avaTooltipDescription="Medium Tooltip" avaTooltipType="simple" avaTooltipTrigger="hover" avaTooltipPosition="top"
                avaTooltipSize="medium" avaTooltipArrow="center">
              </ava-button>
              <ava-button label="Large Tooltip" variant="primary" size="medium" state="default"
                avaTooltipDescription="Large Tooltip" avaTooltipType="simple" avaTooltipTrigger="hover" avaTooltipPosition="top"
                avaTooltipSize="large" avaTooltipArrow="center">
              </ava-button>
            </ng-container> -->

              <!-- Tooltip Variants -->
              <ng-container *ngSwitchCase="'Tooltip Variants'">
                <ava-button
                  label="Default Tooltip"
                  variant="primary"
                  size="medium"
                  state="default"
                  avaTooltipTitle="Default Variant"
                  avaTooltipDescription="This is the default tooltip variant with standard styling"
                  avaTooltipType="simple"
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="top"
                  avaTooltipVariant="default"
                  avaTooltipArrow="center"
                >
                </ava-button>

              </ng-container>

              <!-- Tooltip with Heading and Body -->
              <ng-container *ngSwitchCase="'Tooltip with Heading and Body'">
                <ava-button
                  label="Title Only"
                  variant="primary"
                  size="medium"
                  state="default"
                  avaTooltipTitle="Save Feature"
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="top"
                  avaTooltipArrow="center">
                </ava-button>

                <ava-button
                  label="Description Only"
                  variant="secondary"
                  size="medium"
                  state="default"
                  avaTooltipDescription="This will store your progress and allow you to continue later."
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="top"
                  avaTooltipArrow="center">
                </ava-button>

                <ava-button
                  label="Title + Description"
                  variant="info"
                  size="medium"
                  state="default"
                  avaTooltipTitle="Save Feature"
                  avaTooltipDescription="This will store your progress and allow you to continue later."
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="top"
                  avaTooltipArrow="center">
                </ava-button>
              </ng-container>

              <!-- Tooltip with Icons -->
              <ng-container *ngSwitchCase="'Tooltip with Icons'">
                <ava-button
                  label="Save with Icon"
                  variant="primary"
                  size="medium"
                  state="default"
                  avaTooltipTitle="Save Feature"
                  avaTooltipDescription="This will store your progress and allow you to continue later."
                  avaTooltipIcon="save"
                  avaTooltipIconColor="#000"
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="top"
                  avaTooltipArrow="center">
                </ava-button>

                <ava-button
                  label="Warning"
                  variant="warning"
                  size="medium"
                  state="default"
                  avaTooltipDescription="This action cannot be undone. Please proceed with caution."
                  avaTooltipIcon="alert-triangle"
                  avaTooltipIconColor="#ffd43b"
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="top"
                  avaTooltipArrow="center">
                </ava-button>

                <ava-button
                  label="Info"
                  variant="primary"
                  size="medium"
                  state="default"
                  avaTooltipDescription="Additional information about this feature."
                  avaTooltipIcon="info"
                  avaTooltipIconColor="#339af0"
                  avaTooltipVariant="default"
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="top"
                  avaTooltipArrow="center">
                </ava-button>

                <ava-button
                  label="Custom Color"
                  variant="secondary"
                  size="medium"
                  state="default"
                  avaTooltipTitle="Custom Icon Color"
                  avaTooltipDescription="You can specify any color for the icon."
                  avaTooltipIcon="palette"
                  avaTooltipIconColor="#e64980"
                  avaTooltipTrigger="hover"
                  avaTooltipPosition="top"
                  avaTooltipArrow="center">
                </ava-button>
              </ng-container>

              <!-- Tooltip with Guide and Buttons -->
              <!-- <ng-container *ngSwitchCase="'Tooltip with Guide and Buttons'">

              <ava-button label="Tooltip with Guide and Buttons" variant="primary" size="medium" state="default"
                [avaTooltip]="{
                title: 'Step 1/3',
                description: 'Click here to start guided tour.',
                actions: [
                { label: 'Skip' },
                { label: 'Next', primary: true }
                ]
              }" avaTooltipType="guided" avaTooltipTrigger="click" avaTooltipPosition="top"
                avaTooltipArrow="center"></ava-button>
            </ng-container> -->
            </ng-container>
          </div>

          <div class="code-block" *ngIf="section.showCode">
            <div class="code-content">
              <pre><code [innerText]="getExampleCode(section.title.toLowerCase())"></code></pre>
            </div>
            <button
              class="copy-button"
              (click)="copyCode(section.title.toLowerCase())"
            >
              <!-- <awe-icons iconName="awe_copy"></awe-icons> -->
            </button>
          </div>
        </div>
      </section>
    </div>

    <!-- API Reference -->
    <section class="doc-section api-reference">
      <h2>API Reference</h2>
      <table class="api-table">
        <thead>
          <tr>
            <th>Property</th>
            <th>Type</th>
            <th>Default</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let prop of apiProps">
            <td>
              <code>{{ prop.name }}</code>
            </td>
            <td>
              <code>{{ prop.type }}</code>
            </td>
            <td>
              <code>{{ prop.default }}</code>
            </td>
            <td>{{ prop.description }}</td>
          </tr>
        </tbody>
      </table>
    </section>

    <!-- Events -->
    <section class="doc-section">
      <h2>Events</h2>
      <table class="api-table">
        <thead>
          <tr>
            <th>Event</th>
            <th>Type</th>
            <th>Description</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let event of events">
            <td>
              <code>{{ event.name }}</code>
            </td>
            <td>
              <code>{{ event.type }}</code>
            </td>
            <td>{{ event.description }}</td>
          </tr>
        </tbody>
      </table>
    </section>
  </div>
</div>
