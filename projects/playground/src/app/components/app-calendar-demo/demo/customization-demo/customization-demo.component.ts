import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CalendarComponent } from '../../../../../../../play-comp-library/src/lib/components/calendar/calendar.component';

@Component({
  selector: 'ava-calendar-customization-demo',
  standalone: true,
  imports: [CommonModule, CalendarComponent],
  template: `
    <div class="demo-container">
      <div class="customization-grid">
        <div class="customization-item">
          <h3 style="color: var(--text-primary)">Square Selector (Default)</h3>
          <div class="calendar-wrapper">
            <ava-calendar
              [alwaysOpen]="true"
              selectorShape="square"
              (dateSelected)="onDateSelected($event)"
            >
            </ava-calendar>
          </div>
        </div>

        <div class="customization-item">
          <h3 style="color: var(--text-primary)">Circle Selector</h3>
          <div class="calendar-wrapper">
            <ava-calendar
              [alwaysOpen]="true"
              selectorShape="circle"
              (dateSelected)="onDateSelected($event)"
            >
            </ava-calendar>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        padding: 2rem;
        max-width: 800px;
        margin: 0 auto;
      }

      h2 {
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        text-align: center;
      }

      p {
        color: var(--text-color-secondary);
        margin-bottom: 2rem;
        text-align: center;
      }

      .customization-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
      }

      .customization-item {
        background: var(--surface);
        border: 1px solid var(--surface-border);
        border-radius: var(--border-radius);
        padding: 1.5rem;
      }

      .customization-item h3 {
        color: var(--text-primary);
        margin-bottom: 1rem;
        text-align: center;
        font-size: 1.1rem;
      }

      .calendar-wrapper {
        display: flex;
        justify-content: center;
      }

      .selected-info {
        background: var(--surface);
        border: 1px solid var(--surface-border);
        border-radius: var(--border-radius);
        padding: 1rem;
        text-align: center;
        margin-bottom: 2rem;
      }

      .selected-info p {
        margin: 0;
        color: var(--text-primary);
      }

      .info-box {
        background: var(--surface);
        border: 1px solid var(--surface-border);
        border-radius: var(--border-radius);
        padding: 1.5rem;
      }

      .info-box h3 {
        color: var(--text-primary);
        margin-bottom: 1rem;
        font-size: 1.1rem;
      }

      .info-box ul {
        margin: 0;
        padding-left: 1.5rem;
        color: var(--text-color-secondary);
      }

      .info-box li {
        margin-bottom: 0.5rem;
      }

      @media (max-width: 768px) {
        .customization-grid {
          grid-template-columns: 1fr;
        }

        .demo-container {
          padding: 1rem;
        }
      }
    `,
  ],
})
export class CustomizationDemoComponent {
  selectedDate: Date | null = null;

  onDateSelected(date: Date) {
    this.selectedDate = date;
    console.log('Selected date:', date);
  }
}
