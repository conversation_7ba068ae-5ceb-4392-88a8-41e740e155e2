.demo-navigation {
  margin-bottom: 40px;
}

.demo-navigation h2 {
  color: #333;
  margin-bottom: 20px;
  font-size: 24px;
}

.demo-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.demo-card {
  display: block;
  padding: 20px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
}

.demo-card:hover {
  background-color: #e9ecef;
  border-color: #007bff;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.1);
}

.demo-card h3 {
  margin: 0 0 10px 0;
  color: #007bff;
  font-size: 18px;
  font-weight: 600;
}

.demo-card p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .demo-grid {
    grid-template-columns: 1fr;
  }
}
