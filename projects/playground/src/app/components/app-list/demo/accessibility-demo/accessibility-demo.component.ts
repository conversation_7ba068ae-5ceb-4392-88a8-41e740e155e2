import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ListComponent,
  ListItem,
  ListSelectionEvent,
  ListButtonClickEvent,
} from '../../../../../../../play-comp-library/src/lib/components/list/list.component';

@Component({
  selector: 'ava-list-accessibility-demo',
  standalone: true,
  imports: [CommonModule, ListComponent],
  template: `
    <div class="demo-container">
      <h2>Accessibility Features</h2>
      <p class="demo-description">
        The List component is designed with accessibility in mind, supporting
        keyboard navigation, screen readers, and WCAG 2.1 AA guidelines.
      </p>

      <div class="accessibility-info">
        <h3>Keyboard Navigation</h3>
        <ul>
          <li><strong>Tab:</strong> Navigate between interactive elements</li>
          <li>
            <strong>Enter/Space:</strong> Select items or activate buttons
          </li>
          <li>
            <strong>Arrow Keys:</strong> Navigate between list items (when
            focused)
          </li>
          <li>
            <strong>Escape:</strong> Clear selection or close any open states
          </li>
        </ul>
      </div>

      <div class="demo-section">
        <h3>Keyboard Navigation Demo</h3>
        <p class="demo-instructions">
          Use Tab to focus on the list, then use arrow keys to navigate and
          Enter/Space to select items.
        </p>
        <ava-list
          [items]="keyboardItems"
          [title]="'Keyboard Navigation Test'"
          [height]="'250px'"
          [width]="'400px'"
          (onOptionSelected)="onKeyboardSelection($event)"
        ></ava-list>

        <div class="demo-info" *ngIf="keyboardSelectedItem">
          <p>
            <strong>Selected via keyboard:</strong>
            {{ keyboardSelectedItem.title }}
          </p>
        </div>
      </div>

      <div class="demo-section">
        <h3>Screen Reader Support</h3>
        <p class="demo-instructions">
          This list includes proper ARIA labels and roles for screen reader
          compatibility.
        </p>
        <ava-list
          [items]="screenReaderItems"
          [title]="'Screen Reader Friendly List'"
          [height]="'250px'"
          [width]="'400px'"
          (onOptionSelected)="onScreenReaderSelection($event)"
        ></ava-list>

        <div class="demo-info" *ngIf="screenReaderSelectedItem">
          <p><strong>Selected:</strong> {{ screenReaderSelectedItem.title }}</p>
          <p>
            <strong>Description:</strong>
            {{ screenReaderSelectedItem.subtitle }}
          </p>
        </div>
      </div>

      <div class="demo-section">
        <h3>High Contrast Support</h3>
        <p class="demo-instructions">
          The list maintains proper contrast ratios and provides clear visual
          indicators for selection states.
        </p>
        <ava-list
          [items]="highContrastItems"
          [title]="'High Contrast List'"
          [height]="'250px'"
          [width]="'400px'"
          [multiSelect]="true"
          [showCheckboxes]="true"
          [selectedItemIds]="highContrastSelectedIds"
          (onSelectionChanged)="onHighContrastSelection($event)"
        ></ava-list>

        <div class="demo-info" *ngIf="highContrastSelectedItems.length > 0">
          <p>
            <strong>Selected items:</strong>
            {{ getSelectedTitles(highContrastSelectedItems) }}
          </p>
        </div>
      </div>

      <div class="demo-section">
        <h3>Focus Management</h3>
        <p class="demo-instructions">
          Clear visual focus indicators and logical tab order through all
          interactive elements.
        </p>
        <ava-list
          [items]="focusItems"
          [title]="'Focus Management Test'"
          [height]="'250px'"
          [width]="'400px'"
          (onButtonClick)="onFocusButtonClick($event)"
        ></ava-list>
      </div>

      <div class="accessibility-checklist">
        <h3>Accessibility Checklist</h3>
        <div class="checklist-item">
          <span class="checkmark">✓</span>
          <span>Proper ARIA labels and roles</span>
        </div>
        <div class="checklist-item">
          <span class="checkmark">✓</span>
          <span>Keyboard navigation support</span>
        </div>
        <div class="checklist-item">
          <span class="checkmark">✓</span>
          <span>Screen reader announcements</span>
        </div>
        <div class="checklist-item">
          <span class="checkmark">✓</span>
          <span>High contrast support</span>
        </div>
        <div class="checklist-item">
          <span class="checkmark">✓</span>
          <span>Focus management</span>
        </div>
        <div class="checklist-item">
          <span class="checkmark">✓</span>
          <span>WCAG 2.1 AA compliant</span>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .demo-description {
        color: #666;
        margin-bottom: 30px;
        font-size: 16px;
      }

      .accessibility-info {
        background-color: #e3f2fd;
        padding: 20px;
        border-radius: 6px;
        margin-bottom: 30px;
        border-left: 4px solid #2196f3;
      }

      .accessibility-info h3 {
        margin: 0 0 15px 0;
        color: #1976d2;
        font-size: 18px;
      }

      .accessibility-info ul {
        margin: 0;
        padding-left: 20px;
      }

      .accessibility-info li {
        margin-bottom: 8px;
        color: #424242;
      }

      .demo-section {
        margin-bottom: 40px;
      }

      .demo-section h3 {
        color: #333;
        margin-bottom: 15px;
        font-size: 18px;
      }

      .demo-instructions {
        color: #666;
        margin-bottom: 15px;
        font-style: italic;
        font-size: 14px;
      }

      .demo-info {
        margin-top: 15px;
        padding: 10px;
        background-color: #f8f9fa;
        border-radius: 6px;
        border-left: 4px solid #007bff;
      }

      .demo-info p {
        margin: 0 0 5px 0;
        color: #495057;
      }

      .demo-info p:last-child {
        margin-bottom: 0;
      }

      .accessibility-checklist {
        background-color: #f1f8e9;
        padding: 20px;
        border-radius: 6px;
        border-left: 4px solid #4caf50;
        margin-top: 30px;
      }

      .accessibility-checklist h3 {
        margin: 0 0 15px 0;
        color: #2e7d32;
        font-size: 18px;
      }

      .checklist-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        color: #424242;
      }

      .checklist-item:last-child {
        margin-bottom: 0;
      }

      .checkmark {
        color: #4caf50;
        font-weight: bold;
        margin-right: 10px;
        font-size: 16px;
      }
    `,
  ],
})
export class AccessibilityDemoComponent {
  keyboardSelectedItem: ListItem | null = null;
  screenReaderSelectedItem: ListItem | null = null;
  highContrastSelectedIds: string[] = [];
  highContrastSelectedItems: ListItem[] = [];

  keyboardItems: ListItem[] = [
    { id: '1', title: 'First Item', subtitle: 'Use arrow keys to navigate' },
    {
      id: '2',
      title: 'Second Item',
      subtitle: 'Press Enter or Space to select',
    },
    { id: '3', title: 'Third Item', subtitle: 'Tab to move between elements' },
    { id: '4', title: 'Fourth Item', subtitle: 'Escape to clear selection' },
  ];

  screenReaderItems: ListItem[] = [
    {
      id: '1',
      title: 'John Doe',
      subtitle:
        'Software Engineer with 5 years experience in Angular development',
    },
    {
      id: '2',
      title: 'Jane Smith',
      subtitle:
        'Product Manager specializing in user experience and accessibility',
    },
    {
      id: '3',
      title: 'Bob Johnson',
      subtitle: 'UX Designer with expertise in inclusive design principles',
    },
  ];

  highContrastItems: ListItem[] = [
    {
      id: '1',
      title: 'High Contrast Item 1',
      subtitle: 'Clear visual indicators for selection',
    },
    {
      id: '2',
      title: 'High Contrast Item 2',
      subtitle: 'Proper contrast ratios maintained',
    },
    {
      id: '3',
      title: 'High Contrast Item 3',
      subtitle: 'Accessible color combinations',
    },
  ];

  focusItems: ListItem[] = [
    {
      id: '1',
      title: 'Focus Test Item 1',
      subtitle: 'Clear focus indicators',
      buttons: [
        {
          label: 'Action 1',
          variant: 'primary',
          size: 'small',
          id: 'action1',
          pill: true,
        },
      ],
    },
    {
      id: '2',
      title: 'Focus Test Item 2',
      subtitle: 'Logical tab order',
      buttons: [
        {
          label: 'Action 2',
          variant: 'default',
          size: 'small',
          id: 'action2',
          pill: true,
        },
      ],
    },
  ];

  onKeyboardSelection(item: ListItem): void {
    this.keyboardSelectedItem = item;
    console.log('Keyboard selection:', item);
  }

  onScreenReaderSelection(item: ListItem): void {
    this.screenReaderSelectedItem = item;
    console.log('Screen reader selection:', item);
  }

  onHighContrastSelection(event: ListSelectionEvent): void {
    this.highContrastSelectedIds = event.selectedIds;
    this.highContrastSelectedItems = event.selectedItems;
    console.log('High contrast selection:', event);
  }

  onFocusButtonClick(event: ListButtonClickEvent): void {
    console.log('Focus button click:', event);
  }

  getSelectedTitles(items: ListItem[]): string {
    return items.map((item) => item.title).join(', ');
  }
}
