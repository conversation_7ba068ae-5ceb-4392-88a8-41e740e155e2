import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ListComponent,
  ListItem,
  ListButtonClickEvent,
} from '../../../../../../../play-comp-library/src/lib/components/list/list.component';

@Component({
  selector: 'ava-list-action-buttons-demo',
  standalone: true,
  imports: [CommonModule, ListComponent],
  template: `
    <div class="demo-container">
      <div class="demo-section">
        <ava-list
          [items]="simpleButtonItems"
          [title]="'Task List'"
          [height]="'300px'"
          [width]="'400px'"
          (onButtonClick)="onButtonClick($event)"
        ></ava-list>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        display: flex;
        justify-content: center;
        min-height: 100vh;
        margin-top: 3rem;
        padding-top: 0;
      }

      .demo-description {
        color: #666;
        margin-bottom: 30px;
        font-size: 16px;
      }

      .demo-section {
        margin-bottom: 40px;
      }

      .demo-section h3 {
        color: #333;
        margin-bottom: 15px;
        font-size: 18px;
      }

      .demo-info {
        margin-top: 30px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 6px;
        border-left: 4px solid #28a745;
      }

      .demo-info h4 {
        margin: 0 0 10px 0;
        color: #333;
        font-size: 16px;
      }

      .demo-info p {
        margin: 0 0 5px 0;
        color: #495057;
      }

      .demo-info p:last-child {
        margin-bottom: 0;
      }
    `,
  ],
})
export class ActionButtonsDemoComponent {
  lastAction: ListButtonClickEvent | null = null;

  simpleButtonItems: ListItem[] = [
    {
      id: '1',
      title: 'Complete Documentation',
      subtitle: 'Due: Sep 15, 2025',
      buttons: [
        {
          label: 'Edit',
          variant: 'warning',
          size: 'small',
          id: 'edit',
          pill: true,
        },
      ],
    },
    {
      id: '2',
      title: 'Review Code Changes',
      subtitle: 'Due: Sep 20, 2025',
      buttons: [
        {
          label: 'View',
          variant: 'primary',
          size: 'small',
          id: 'view',
          pill: true,
        },
      ],
    },
  ];

  multipleButtonItems: ListItem[] = [
    {
      id: '1',
      title: 'John Doe',
      subtitle: '<EMAIL>',
      buttons: [
        {
          label: 'Edit',
          variant: 'default',
          size: 'small',
          id: 'edit',
          pill: true,
        },
        {
          label: 'Delete',
          variant: 'danger',
          size: 'small',
          id: 'delete',
          pill: true,
        },
      ],
    },
    {
      id: '2',
      title: 'Jane Smith',
      subtitle: '<EMAIL>',
      buttons: [
        {
          label: 'Edit',
          variant: 'default',
          size: 'small',
          id: 'edit',
          pill: true,
        },
        {
          label: 'Delete',
          variant: 'danger',
          size: 'small',
          id: 'delete',
          pill: true,
        },
      ],
    },
  ];

  iconButtonItems: ListItem[] = [
    {
      id: '1',
      title: 'document.pdf',
      subtitle: '2.5 MB • Updated 2 hours ago',
      buttons: [
        {
          iconName: 'Download',
          variant: 'primary',
          size: 'small',
          id: 'download',
          iconPosition: 'only',
        },
        {
          iconName: 'Edit',
          variant: 'default',
          size: 'small',
          id: 'edit',
          iconPosition: 'only',
        },
        {
          iconName: 'Trash',
          variant: 'danger',
          size: 'small',
          id: 'delete',
          iconPosition: 'only',
        },
      ],
    },
    {
      id: '2',
      title: 'presentation.pptx',
      subtitle: '15.2 MB • Updated 1 day ago',
      buttons: [
        {
          iconName: 'Download',
          variant: 'primary',
          size: 'small',
          id: 'download',
          iconPosition: 'only',
        },
        {
          iconName: 'Edit',
          variant: 'default',
          size: 'small',
          id: 'edit',
          iconPosition: 'only',
        },
        {
          iconName: 'Trash',
          variant: 'danger',
          size: 'small',
          id: 'delete',
          iconPosition: 'only',
        },
      ],
    },
  ];

  richButtonItems: ListItem[] = [
    {
      id: '1',
      title: 'John Doe',
      subtitle: 'Software Engineer',
      avatar: {
        profileText: 'JD',
        size: 'medium',
        shape: 'pill',
        active: true,
      },
      icon: {
        iconName: 'User',
        iconColor: '#007bff',
        iconSize: 20,
      },
      buttons: [
        {
          label: 'Message',
          variant: 'primary',
          size: 'small',
          iconName: 'Message',
          iconPosition: 'left',
          id: 'message',
          pill: true,
        },
        {
          label: 'Profile',
          variant: 'default',
          size: 'small',
          iconName: 'User',
          iconPosition: 'left',
          id: 'profile',
          pill: true,
        },
      ],
    },
    {
      id: '2',
      title: 'Jane Smith',
      subtitle: 'Product Manager',
      avatar: {
        profileText: 'JS',
        size: 'medium',
        shape: 'pill',
        badgeState: 'high-priority',
        badgeCount: 2,
      },
      icon: {
        iconName: 'Settings',
        iconColor: '#28a745',
        iconSize: 20,
      },
      buttons: [
        {
          label: 'Message',
          variant: 'primary',
          size: 'small',
          iconName: 'Message',
          iconPosition: 'left',
          id: 'message',
          pill: true,
        },
        {
          label: 'Profile',
          variant: 'default',
          size: 'small',
          iconName: 'User',
          iconPosition: 'left',
          id: 'profile',
          pill: true,
        },
      ],
    },
  ];

  onButtonClick(event: ListButtonClickEvent): void {
    this.lastAction = event;
    console.log('Button clicked:', event);

    // Simulate different actions based on button type
    switch (event.button.id) {
      case 'edit':
        console.log('Edit action for:', event.item.title);
        break;
      case 'delete':
        console.log('Delete action for:', event.item.title);
        break;
      case 'view':
        console.log('View action for:', event.item.title);
        break;
      case 'download':
        console.log('Download action for:', event.item.title);
        break;
      case 'message':
        console.log('Message action for:', event.item.title);
        break;
      case 'profile':
        console.log('Profile action for:', event.item.title);
        break;
      default:
        console.log('Unknown action:', event.button.id);
    }
  }

  onIconClick(event: { item: ListItem; event: Event }): void {
    console.log('Icon clicked for:', event.item.title);
  }
}
