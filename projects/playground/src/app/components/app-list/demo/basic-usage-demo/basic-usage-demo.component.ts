import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  ListComponent,
  ListItem,
} from '../../../../../../../play-comp-library/src/lib/components/list/list.component';

@Component({
  selector: 'ava-list-basic-usage-demo',
  standalone: true,
  imports: [CommonModule, ListComponent],
  template: `
    <div class="demo-container">
      <div class="demo-section">
        <ava-list
          [items]="itemsWithSubtitles"
          [title]="'Team Members'"
          [height]="'300px'"
          [width]="'400px'"
          (onOptionSelected)="onItemSelected($event)"
        ></ava-list>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        // display: flex;
        justify-content: center;
        min-height: 100vh;
        margin-top: 3rem;
        padding-top: 0;
      }

      .demo-description {
        color: #666;
        margin-bottom: 30px;
        font-size: 16px;
      }

      .demo-section {
        margin-bottom: 40px;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .demo-section h3 {
        color: #333;
        margin-bottom: 15px;
        font-size: 18px;
      }

      .demo-info {
        margin-top: 15px;
        padding: 10px;
        // background-color: #f8f9fa;
        border-radius: 6px;
        border-left: 4px solid #007bff;
      }

      .demo-info p {
        margin: 0;
        color: #495057;
      }
    `,
  ],
})
export class BasicUsageDemoComponent {
  selectedItem: ListItem | null = null;

  simpleItems: ListItem[] = [
    { id: '1', title: 'John Doe' },
    { id: '2', title: 'Jane Smith' },
    { id: '3', title: 'Bob Johnson' },
    { id: '4', title: 'Alice Brown' },
    { id: '5', title: 'Charlie Wilson' },
  ];

  itemsWithSubtitles: ListItem[] = [
    {
      id: '1',
      title: 'John Doe',
      subtitle: 'Software Engineer',
    },
    {
      id: '2',
      title: 'Jane Smith',
      subtitle: 'Product Manager',
    },
    {
      id: '3',
      title: 'Bob Johnson',
      subtitle: 'UX Designer',
    },
    {
      id: '4',
      title: 'Alice Brown',
      subtitle: 'Data Scientist',
    },
  ];

  itemsWithAvatars: ListItem[] = [
    {
      id: '1',
      title: 'John Doe',
      subtitle: 'Software Engineer',
      avatar: {
        profileText: 'JD',
        size: 'medium',
        shape: 'pill',
        active: true,
      },
    },
    {
      id: '2',
      title: 'Jane Smith',
      subtitle: 'Product Manager',
      avatar: {
        profileText: 'JS',
        size: 'medium',
        shape: 'pill',
        badgeState: 'high-priority',
        badgeCount: 2,
      },
    },
    {
      id: '3',
      title: 'Bob Johnson',
      subtitle: 'UX Designer',
      avatar: {
        profileText: 'BJ',
        size: 'medium',
        shape: 'pill',
        badgeState: 'medium-priority',
        badgeCount: 1,
      },
    },
  ];

  onItemSelected(item: ListItem): void {
    this.selectedItem = item;
    console.log('Selected item:', item);
  }
}
