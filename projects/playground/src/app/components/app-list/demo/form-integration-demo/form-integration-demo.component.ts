import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  ListComponent,
  ListItem,
} from '../../../../../../../play-comp-library/src/lib/components/list/list.component';

@Component({
  selector: 'ava-list-form-integration-demo',
  standalone: true,
  imports: [CommonModule, ListComponent, ReactiveFormsModule],
  template: `
    <div class="demo-container">
      <h2>Form Integration</h2>
      <p class="demo-description">
        Lists integrated with Angular reactive forms for validation and data
        binding.
      </p>

      <div class="demo-section">
        <h3>Single Selection Form</h3>
        <form [formGroup]="singleSelectForm" (ngSubmit)="onSingleSubmit()">
          <ava-list
            formControlName="selectedUser"
            [items]="singleSelectUsers"
            [title]="'Select a User'"
            [height]="'250px'"
            [width]="'400px'"
            [required]="true"
            [errorMessage]="'Please select a user'"
          ></ava-list>

          <div class="form-actions">
            <button
              type="submit"
              [disabled]="singleSelectForm.invalid"
              class="submit-btn"
            >
              Submit Selection
            </button>
            <button type="button" (click)="resetSingleForm()" class="reset-btn">
              Reset
            </button>
          </div>
        </form>

        <div class="form-info" *ngIf="singleFormValue">
          <h4>Form Value:</h4>
          <p><strong>Selected User ID:</strong> {{ singleFormValue }}</p>
        </div>
      </div>

      <div class="demo-section">
        <h3>Multi-Selection Form</h3>
        <form [formGroup]="multiSelectForm" (ngSubmit)="onMultiSubmit()">
          <ava-list
            formControlName="selectedProducts"
            [items]="multiSelectProducts"
            [title]="'Select Products (Max 3)'"
            [height]="'250px'"
            [width]="'400px'"
            [multiSelect]="true"
            [showCheckboxes]="true"
            [maxSelections]="3"
            [required]="true"
            [errorMessage]="'Please select at least one product'"
          ></ava-list>

          <div class="form-actions">
            <button
              type="submit"
              [disabled]="multiSelectForm.invalid"
              class="submit-btn"
            >
              Submit Selection
            </button>
            <button type="button" (click)="resetMultiForm()" class="reset-btn">
              Reset
            </button>
          </div>
        </form>

        <div class="form-info" *ngIf="multiFormValue.length > 0">
          <h4>Form Value:</h4>
          <p>
            <strong>Selected Product IDs:</strong>
            {{ multiFormValue.join(', ') }}
          </p>
          <p><strong>Count:</strong> {{ multiFormValue.length }}/3</p>
        </div>
      </div>

      <div class="demo-section">
        <h3>Complex Form with Multiple Lists</h3>
        <form [formGroup]="complexForm" (ngSubmit)="onComplexSubmit()">
          <div class="form-row">
            <div class="form-column">
              <ava-list
                formControlName="selectedTeam"
                [items]="teamMembers"
                [title]="'Select Team Members'"
                [height]="'200px'"
                [width]="'300px'"
                [multiSelect]="true"
                [showCheckboxes]="true"
                [maxSelections]="5"
                [required]="true"
                [errorMessage]="'Please select team members'"
              ></ava-list>
            </div>

            <div class="form-column">
              <ava-list
                formControlName="selectedTools"
                [items]="developmentTools"
                [title]="'Select Development Tools'"
                [height]="'200px'"
                [width]="'300px'"
                [multiSelect]="true"
                [showCheckboxes]="true"
                [maxSelections]="3"
                [required]="true"
                [errorMessage]="'Please select development tools'"
              ></ava-list>
            </div>
          </div>

          <div class="form-actions">
            <button
              type="submit"
              [disabled]="complexForm.invalid"
              class="submit-btn"
            >
              Submit Project Setup
            </button>
            <button
              type="button"
              (click)="resetComplexForm()"
              class="reset-btn"
            >
              Reset
            </button>
          </div>
        </form>

        <div class="form-info" *ngIf="complexFormValue">
          <h4>Form Value:</h4>
          <p>
            <strong>Team Members:</strong>
            {{ complexFormValue.team?.join(', ') || 'None' }}
          </p>
          <p>
            <strong>Development Tools:</strong>
            {{ complexFormValue.tools?.join(', ') || 'None' }}
          </p>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
      }

      .demo-description {
        color: #666;
        margin-bottom: 30px;
        font-size: 16px;
      }

      .demo-section {
        margin-bottom: 50px;
      }

      .demo-section h3 {
        color: #333;
        margin-bottom: 20px;
        font-size: 18px;
      }

      .form-actions {
        margin-top: 20px;
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
      }

      .submit-btn {
        padding: 10px 20px;
        background-color: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }

      .submit-btn:hover:not(:disabled) {
        background-color: #0056b3;
      }

      .submit-btn:disabled {
        background-color: #6c757d;
        cursor: not-allowed;
      }

      .reset-btn {
        padding: 10px 20px;
        background-color: #6c757d;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }

      .reset-btn:hover {
        background-color: #545b62;
      }

      .form-info {
        margin-top: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 6px;
        border-left: 4px solid #28a745;
      }

      .form-info h4 {
        margin: 0 0 10px 0;
        color: #333;
        font-size: 16px;
      }

      .form-info p {
        margin: 0 0 5px 0;
        color: #495057;
      }

      .form-info p:last-child {
        margin-bottom: 0;
      }

      .form-row {
        display: flex;
        gap: 20px;
        margin-bottom: 20px;
      }

      .form-column {
        flex: 1;
      }

      @media (max-width: 768px) {
        .form-row {
          flex-direction: column;
        }
      }
    `,
  ],
})
export class FormIntegrationDemoComponent {
  singleSelectForm: FormGroup;
  multiSelectForm: FormGroup;
  complexForm: FormGroup;

  singleFormValue: string | null = null;
  multiFormValue: string[] = [];
  complexFormValue: { team?: string[]; tools?: string[] } | null = null;

  singleSelectUsers: ListItem[] = [
    { id: '1', title: 'John Doe', subtitle: 'Software Engineer' },
    { id: '2', title: 'Jane Smith', subtitle: 'Product Manager' },
    { id: '3', title: 'Bob Johnson', subtitle: 'UX Designer' },
    { id: '4', title: 'Alice Brown', subtitle: 'Data Scientist' },
  ];

  multiSelectProducts: ListItem[] = [
    { id: '1', title: 'Product A', subtitle: '$99.99' },
    { id: '2', title: 'Product B', subtitle: '$149.99' },
    { id: '3', title: 'Product C', subtitle: '$199.99' },
    { id: '4', title: 'Product D', subtitle: '$299.99' },
    { id: '5', title: 'Product E', subtitle: '$399.99' },
  ];

  teamMembers: ListItem[] = [
    { id: '1', title: 'John Doe', subtitle: 'Software Engineer' },
    { id: '2', title: 'Jane Smith', subtitle: 'Product Manager' },
    { id: '3', title: 'Bob Johnson', subtitle: 'UX Designer' },
    { id: '4', title: 'Alice Brown', subtitle: 'Data Scientist' },
    { id: '5', title: 'Charlie Wilson', subtitle: 'DevOps Engineer' },
    { id: '6', title: 'Diana Davis', subtitle: 'QA Engineer' },
  ];

  developmentTools: ListItem[] = [
    { id: '1', title: 'Visual Studio Code', subtitle: 'Code Editor' },
    { id: '2', title: 'Git', subtitle: 'Version Control' },
    { id: '3', title: 'Docker', subtitle: 'Containerization' },
    { id: '4', title: 'Jenkins', subtitle: 'CI/CD' },
    { id: '5', title: 'PostgreSQL', subtitle: 'Database' },
  ];

  constructor(private fb: FormBuilder) {
    this.singleSelectForm = this.fb.group({
      selectedUser: [null, Validators.required],
    });

    this.multiSelectForm = this.fb.group({
      selectedProducts: [[], Validators.required],
    });

    this.complexForm = this.fb.group({
      selectedTeam: [[], Validators.required],
      selectedTools: [[], Validators.required],
    });

    // Subscribe to form value changes
    this.singleSelectForm.valueChanges.subscribe((value) => {
      this.singleFormValue = value.selectedUser;
    });

    this.multiSelectForm.valueChanges.subscribe((value) => {
      this.multiFormValue = value.selectedProducts || [];
    });

    this.complexForm.valueChanges.subscribe((value) => {
      this.complexFormValue = value;
    });
  }

  onSingleSubmit(): void {
    if (this.singleSelectForm.valid) {
      console.log('Single select form submitted:', this.singleSelectForm.value);
      alert(`Selected user: ${this.singleFormValue}`);
    }
  }

  onMultiSubmit(): void {
    if (this.multiSelectForm.valid) {
      console.log('Multi select form submitted:', this.multiSelectForm.value);
      alert(`Selected products: ${this.multiFormValue.join(', ')}`);
    }
  }

  onComplexSubmit(): void {
    if (this.complexForm.valid) {
      console.log('Complex form submitted:', this.complexForm.value);
      alert('Project setup submitted successfully!');
    }
  }

  resetSingleForm(): void {
    this.singleSelectForm.reset();
  }

  resetMultiForm(): void {
    this.multiSelectForm.reset();
  }

  resetComplexForm(): void {
    this.complexForm.reset();
  }
}
