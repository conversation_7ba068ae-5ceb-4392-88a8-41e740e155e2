import { Component } from '@angular/core';
import { HttpClientModule } from '@angular/common/http';
import {
  ListComponent,
  ListItem,
  ListSelectionEvent,
} from '../../../../../play-comp-library/src/lib/components/list/list.component';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

interface ListDocSection {
  title: string;
  description: string;
  showCode: boolean;
}

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

@Component({
  selector: 'app-list-documentation',
  standalone: true,
  imports: [ListComponent, CommonModule, HttpClientModule, RouterModule],
  templateUrl: './app-list.component.html',
  styleUrls: ['./app-list.component.scss'],
})
export class AppListComponent {
  // Demo data (keep your existing demo arrays)
  simpleList: ListItem[] = [
    { id: '1', title: 'Azure OpenAI' },
    { id: '2', title: 'Amazon Bedrock' },
    { id: '3', title: 'Google AI' },
    { id: '4', title: 'DA Open Source' },
    { id: '5', title: 'Anthropic Claude' },
    { id: '6', title: 'Hugging Face' },
  ];

  // Variant 2: List with subtitles
  listWithSubtitles: ListItem[] = [
    { id: '1', title: 'Azure OpenAI', subtitle: 'Performance Check' },
    { id: '2', title: 'Amazon Bedrock', subtitle: 'Performance Check' },
    { id: '3', title: 'Google AI', subtitle: 'Performance Check' },
    { id: '4', title: 'DA Open Source', subtitle: 'Performance Check' },
    { id: '5', title: 'Anthropic Claude', subtitle: 'Performance Check' },
    { id: '6', title: 'Hugging Face', subtitle: 'Performance Check' },
  ];

  // Variant 3: List with avatars
  listWithAvatars: ListItem[] = [
    {
      id: '1',
      title: 'John Doe',
      avatar: {
        imageUrl: '/assets/1.png',
        size: 'medium',
        shape: 'pill',
        active: true,
      },
    },
    {
      id: '2',
      title: 'Jane Smith',
      avatar: {
        imageUrl: '/assets/1.png',
        size: 'medium',
        shape: 'pill',
        badgeState: 'low-priority',
        badgeCount: 3,
      },
    },
    {
      id: '3',
      title: 'Sam Lee',
      avatar: { imageUrl: '/assets/1.png', size: 'medium', shape: 'pill' },
    },
  ];

  // Variant 4: List with icons only
  listWithIcons: ListItem[] = [
    {
      id: '1',
      title: 'Settings',
      icon: {
        iconName: 'Settings',
        iconColor: '#6b7280',
        iconSize: 24,
        cursor: true,
      },
    },
    {
      id: '2',
      title: 'Dashboard',
      icon: {
        iconName: 'home',
        iconColor: '#3b82f6',
        iconSize: 24,
        cursor: true,
      },
    },
    {
      id: '3',
      title: 'Profile',
      icon: {
        iconName: 'user',
        iconColor: '#10b981',
        iconSize: 24,
        cursor: true,
      },
    },
  ];

  // Variant 5: List with buttons
  listWithButtons: ListItem[] = [
    {
      id: '1',
      title: 'Invite',
      buttons: [
        {
          label: 'Send',
          variant: 'primary',
          size: 'small',
          id: 'send',
          pill: true,
        },
      ],
    },
    {
      id: '2',
      title: 'Remove',
      buttons: [
        {
          label: 'Delete',
          variant: 'secondary',
          size: 'small',
          id: 'delete',
          pill: true,
        },
      ],
    },
  ];

  // Variant 6: List with all fields
  listWithAllFields: ListItem[] = [
    {
      id: '1',
      title: 'Azure OpenAI',
      subtitle: 'Performance Check',
      avatar: { imageUrl: '/assets/1.png', size: 'medium', shape: 'pill' },
      buttons: [
        {
          label: 'Edit',
          variant: 'primary',
          size: 'small',
          id: 'edit',
          pill: true,
        },
        {
          label: 'Delete',
          variant: 'secondary',
          size: 'small',
          id: 'delete',
          pill: true,
        },
      ],
    },
    {
      id: '2',
      title: 'Amazon Bedrock',
      subtitle: 'Performance Check',
      avatar: { imageUrl: '/assets/1.png', size: 'medium', shape: 'pill' },
      buttons: [
        {
          label: 'Edit',
          variant: 'primary',
          size: 'small',
          id: 'edit',
          pill: true,
        },
        {
          label: 'Delete',
          variant: 'secondary',
          size: 'small',
          id: 'delete',
          pill: true,
        },
      ],
    },
  ];

  // Variant 7: Multi-select with checkboxes
  selectedUserIds: string[] = [];
  multiSelectUsers: ListItem[] = [
    {
      id: '1',
      title: 'John Doe',
      subtitle: 'Software Engineer',
      avatar: {
        imageUrl: '/assets/1.png',
        size: 'medium',
        shape: 'pill',
        active: true,
      },
    },
    {
      id: '2',
      title: 'Jane Smith',
      subtitle: 'Product Manager',
      avatar: { imageUrl: '/assets/1.png', size: 'medium' },
    },
  ];

  // Variant 8: Empty state
  emptyList: ListItem[] = [];

  // Error state demos
  errorList: ListItem[] = [
    { id: '1', title: 'Item 1' },
    { id: '2', title: 'Item 2' },
    { id: '3', title: 'Item 3' },
  ];
  errorSelectedId: string | null = null;
  errorMessage = 'You must select an item!';
  errorPosition: 'top' | 'bottom' = 'top';

  // Error: showErrorImmediately true (default)
  errorImmediateId: string | null = null;
  // Error: error at bottom
  errorBottomId: string | null = null;
  // Multi-select error
  errorMultiIds: string[] = [];
  errorMultiList: ListItem[] = [
    { id: '1', title: 'User 1' },
    { id: '2', title: 'User 2' },
    { id: '3', title: 'User 3' },
  ];

  // Event handlers with source switch
  selectedEngineId: string | null = null;
  onOptionSelected(selectedItem: ListItem, source: string): void {
    switch (source) {
      case 'simple':
        console.log('[Simple List] Selected:', selectedItem);
        this.selectedEngineId = selectedItem.id;
        break;
      case 'subtitles':
        console.log('[Subtitles List] Selected:', selectedItem);
        break;
      case 'avatars':
        console.log('[Avatars List] Selected:', selectedItem);
        break;
      case 'allFields':
        console.log('[All Fields List] Selected:', selectedItem);
        break;
      default:
        console.log('[Other List] Selected:', selectedItem);
    }
  }
  onSelectionChanged(event: ListSelectionEvent, source: string): void {
    switch (source) {
      case 'multiSelect':
        console.log('[Multi-Select List] Selection changed:', event);
        this.selectedUserIds = event.selectedIds;
        break;
      default:
        console.log('[Other List] Selection changed:', event);
    }
  }
  onIconClick(event: { item: ListItem; event: Event }, source: string): void {
    switch (source) {
      case 'icons':
        console.log('[Icons List] Icon clicked:', event.item.title);
        break;
      case 'allFields':
        console.log('[All Fields List] Icon clicked:', event.item.title);
        break;
      default:
        console.log('[Other List] Icon clicked:', event.item.title);
    }
  }
  onButtonClick(event: { item: ListItem; event: Event }, source: string): void {
    switch (source) {
      case 'buttons':
        console.log('[Buttons List] Button clicked:', event.item.title);
        break;
      case 'allFields':
        console.log('[All Fields List] Button clicked:', event.item.title);
        break;
      default:
        console.log('[Other List] Button clicked:', event.item.title);
    }
  }

  sections: ListDocSection[] = [
    {
      title: 'Simple List',
      description: 'A basic list with only titles.',
      showCode: false,
    },
    {
      title: 'List with Subtitles',
      description: 'List items with subtitles.',
      showCode: false,
    },
    {
      title: 'List with Avatars',
      description: 'List items with avatars.',
      showCode: false,
    },
    {
      title: 'List with Icons Only',
      description: 'List items with icons only.',
      showCode: false,
    },
    {
      title: 'List with Buttons',
      description: 'List items with action buttons.',
      showCode: false,
    },
    {
      title: 'List with All Fields',
      description:
        'List items with all features (avatar, icon, buttons, subtitle).',
      showCode: false,
    },
    {
      title: 'Multi-Select with Checkboxes',
      description: 'List with checkboxes and multi-selection.',
      showCode: false,
    },
    {
      title: 'Empty State',
      description: 'List with no items (empty state).',
      showCode: false,
    },
    {
      title: 'Error State',
      description:
        'Demonstrates error handling and validation in the list component.',
      showCode: false,
    },
    {
      title: 'Error State (Bottom)',
      description: 'Error message shown at the bottom.',
      showCode: false,
    },
    {
      title: 'Error State (Multi-Select)',
      description: 'Error handling in multi-select mode.',
      showCode: false,
    },
  ];

  apiProps: ApiProperty[] = [
    {
      name: 'title',
      type: 'string',
      default: "''",
      description: 'The title of the list.',
    },
    {
      name: 'items',
      type: 'ListItem[]',
      default: '[]',
      description: 'Array of items to display.',
    },
    {
      name: 'height',
      type: 'string',
      default: "'400px'",
      description: 'Height of the list container.',
    },
    {
      name: 'width',
      type: 'string',
      default: "'100%'",
      description: 'Width of the list container.',
    },
    {
      name: 'emptyLabel',
      type: 'string',
      default: 'No items available',
      description: 'Label to display when the list is empty.',
    },
    {
      name: 'multiSelect',
      type: 'boolean',
      default: 'false',
      description: 'Enable multi-select mode.',
    },
    {
      name: 'maxSelections',
      type: 'number',
      default: 'undefined',
      description: 'Maximum number of selections allowed (multi-select).',
    },
    {
      name: 'selectedItemId',
      type: 'string | null',
      default: 'null',
      description: 'ID of the currently selected item.',
    },
    {
      name: 'selectedItemIds',
      type: 'string[]',
      default: '[]',
      description: 'IDs of currently selected items (multi-select).',
    },
    {
      name: 'showCheckboxes',
      type: 'boolean',
      default: 'false',
      description: 'Show checkboxes for selection.',
    },
    {
      name: 'selectionMode',
      type: "'click' | 'checkbox'",
      default: "'click'",
      description: 'Selection mode for the list.',
    },
    {
      name: 'onOptionSelected',
      type: 'EventEmitter<ListItem>',
      default: '',
      description: 'Emits when an item is selected.',
    },
    {
      name: 'onSelectionChanged',
      type: 'EventEmitter<ListSelectionEvent>',
      default: '',
      description: 'Emits when selection changes (multi-select).',
    },
    {
      name: 'onButtonClick',
      type: 'EventEmitter<{ item: ListItem, event: Event }>',
      default: '',
      description: 'Emits when a button in a list item is clicked.',
    },
    {
      name: 'onIconClick',
      type: 'EventEmitter<{ item: ListItem, event: Event }>',
      default: '',
      description: 'Emits when an icon in a list item is clicked.',
    },
    {
      name: 'required',
      type: 'boolean',
      default: 'false',
      description:
        'Whether selection is required (shows error if not selected).',
    },
    {
      name: 'errorMessage',
      type: 'string',
      default: 'Please select at least one item',
      description: 'Custom error message to display.',
    },
    {
      name: 'errorPosition',
      type: "'top' | 'bottom'",
      default: "'bottom'",
      description: 'Position of the error message.',
    },
    {
      name: 'showErrorImmediately',
      type: 'boolean',
      default: 'true',
      description: 'Show error immediately or only after touch.',
    },
  ];

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation();
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  getListCode(sectionTitle: string): string {
    const codeMap: Record<string, string> = {
      'simple list': `
<ava-list
  [items]="simpleList"
  [title]="'Simple List'"
  [height]="'336px'"
  [width]="'607px'"
  (onOptionSelected)="onOptionSelected($event, 'simple')"
></ava-list>`,
      'list with subtitles': `
<ava-list
  [items]="listWithSubtitles"
  [title]="'List with Subtitles'"
  [height]="'336px'"
  [width]="'607px'"
  (onOptionSelected)="onOptionSelected($event, 'subtitles')"
></ava-list>`,
      'list with avatars': `
<ava-list
  [items]="listWithAvatars"
  [title]="'List with Avatars'"
  [height]="'336px'"
  [width]="'607px'"
  (onOptionSelected)="onOptionSelected($event, 'avatars')"
></ava-list>`,
      'list with icons only': `
<ava-list
  [items]="listWithIcons"
  [title]="'List with Icons Only'"
  [height]="'336px'"
  [width]="'607px'"
  (onIconClick)="onIconClick($event, 'icons')"
></ava-list>`,
      'list with buttons': `
<ava-list
  [items]="listWithButtons"
  [title]="'List with Buttons'"
  [height]="'336px'"
  [width]="'607px'"
  (onButtonClick)="onButtonClick($event, 'buttons')"
></ava-list>`,
      'list with all fields': `
<ava-list
  [items]="listWithAllFields"
  [title]="'List with All Fields'"
  [height]="'336px'"
  [width]="'607px'"
  (onOptionSelected)="onOptionSelected($event, 'allFields')"
  (onIconClick)="onIconClick($event, 'allFields')"
  (onButtonClick)="onButtonClick($event, 'allFields')"
></ava-list>`,
      'multi-select with checkboxes': `
<ava-list
  [items]="multiSelectUsers"
  [title]="'Multi-Select List'"
  [height]="'336px'"
  [width]="'607px'"
  [multiSelect]="true"
  [showCheckboxes]="true"
  [maxSelections]="5"
  [selectedItemIds]="selectedUserIds"
  (onSelectionChanged)="onSelectionChanged($event, 'multiSelect')"
></ava-list>`,
      'empty state': `
<ava-list
  [items]="emptyList"
  [height]="'336px'"
  [width]="'607px'"
  emptyLabel="No data available"
></ava-list>`,
      'error state': `
<ava-list
  [title]="'List with Error'"
  [items]="errorList"
  [height]="'336px'"
  [width]="'400px'"
  [required]="true"
  [errorMessage]="errorMessage"
  [errorPosition]="errorPosition"
  [selectedItemId]="errorSelectedId"
  (onOptionSelected)="onErrorOptionSelected($event)"
></ava-list>`,
      'error state (bottom)': `
<ava-list
  [title]="'List with Error (Bottom)'"
  [items]="errorList"
  [height]="'336px'"
  [width]="'400px'"
  [required]="true"
  [errorMessage]="'Error at the bottom.'"
  [errorPosition]="'bottom'"
  [showErrorImmediately]="true"
  [selectedItemId]="errorBottomId"
  (onOptionSelected)="onErrorBottom($event)"
></ava-list>`,
      'error state (multi-select)': `
<ava-list
  [title]="'Multi-Select with Error'"
  [items]="errorMultiList"
  [height]="'336px'"
  [width]="'400px'"
  [multiSelect]="true"
  [showCheckboxes]="true"
  [required]="true"
  [errorMessage]="'Select at least one user.'"
  [showErrorImmediately]="false"
  [selectedItemIds]="errorMultiIds"
  (onSelectionChanged)="onErrorMulti($event)"
></ava-list>`,
    };

    return codeMap[sectionTitle.toLowerCase()] || 'Code not available';
  }

  copyCode(sectionTitle: string): void {
    const code = this.getListCode(sectionTitle.toLowerCase());
    navigator.clipboard
      .writeText(code)
      .then(() => {
        console.log('Code copied to clipboard');
      })
      .catch((err) => {
        console.error('Failed to copy code: ', err);
      });
  }

  onErrorOptionSelected(selectedItem: ListItem): void {
    this.errorSelectedId = selectedItem.id;
    console.log('Error option selected:', selectedItem);
  }

  onErrorBottom(selectedItem: ListItem): void {
    this.errorBottomId = selectedItem.id;
    console.log('Error bottom option selected:', selectedItem);
  }

  onErrorMulti(event: ListSelectionEvent): void {
    this.errorMultiIds = event.selectedIds;
    console.log('Error multi selection changed:', event);
  }
}
