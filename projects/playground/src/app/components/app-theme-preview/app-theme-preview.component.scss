.theme-preview {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  font-family: var(
    --font-family-display,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    sans-serif
  );
  color: var(--color-text-primary);
  background: var(--color-background-primary);
  min-height: 100vh;

  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400px;

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid var(--color-border-subtle);
      border-top: 4px solid var(--color-brand-primary);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 1rem;
    }

    p {
      color: var(--color-text-secondary);
      font-size: 1.1rem;
    }
  }

  .theme-header {
    text-align: center;
    margin-bottom: 3rem;

    h1 {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--color-text-primary);
      margin-bottom: 1rem;
    }

    p {
      font-size: 1.2rem;
      color: var(--color-text-secondary);
      max-width: 600px;
      margin: 0 auto 2rem;
    }

    .theme-stats {
      display: flex;
      justify-content: center;
      gap: 3rem;
      margin-top: 2rem;

      .stat {
        text-align: center;

        .stat-number {
          display: block;
          font-size: 2rem;
          font-weight: 700;
          color: var(--color-brand-primary);
        }

        .stat-label {
          color: var(--color-text-secondary);
          font-size: 0.9rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }
    }
  }

  .current-theme-info {
    background: var(--color-background-secondary);
    border: 1px solid var(--color-border-default);
    border-radius: var(--global-radius-lg);
    padding: 2rem;
    margin-bottom: 3rem;

    .current-theme-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1.5rem;

      h2 {
        color: var(--color-text-primary);
        margin: 0;
        font-size: 1.5rem;
      }

      .theme-category-badge {
        padding: 0.5rem 1rem;
        border-radius: var(--global-radius-md);
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        &.default {
          background: var(--color-brand-primary);
          color: var(--color-text-on-primary);
        }

        &.dark {
          background: var(--color-brand-primary);
          color: var(--color-text-on-primary);
        }

        &.acme {
          background: var(--color-brand-primary);
          color: var(--color-text-on-primary);
        }
      }
    }

    .theme-details {
      .theme-description {
        color: var(--color-text-secondary);
        font-size: 1.1rem;
        margin-bottom: 1.5rem;
        line-height: 1.6;
      }

      .color-preview {
        display: flex;
        gap: 2rem;
        margin-bottom: 1.5rem;

        .color-preview-item {
          display: flex;
          align-items: center;
          gap: 1rem;

          .color-swatch {
            width: 60px;
            height: 60px;
            border-radius: var(--global-radius-md);
            border: 2px solid var(--color-border-default);
          }

          .color-info {
            display: flex;
            flex-direction: column;

            .color-name {
              font-weight: 600;
              color: var(--color-text-primary);
              font-size: 0.9rem;
            }

            .color-value {
              color: var(--color-text-secondary);
              font-family: monospace;
              font-size: 0.8rem;
            }
          }
        }
      }

      .characteristics {
        h4 {
          color: var(--color-text-primary);
          margin-bottom: 1rem;
          font-size: 1rem;
        }

        .characteristics-grid {
          display: flex;
          flex-wrap: wrap;
          gap: 0.75rem;

          .characteristic {
            background: var(--color-surface-subtle-hover);
            color: var(--color-text-secondary);
            padding: 0.5rem 1rem;
            border-radius: var(--global-radius-md);
            font-size: 0.9rem;
            border: 1px solid var(--color-border-subtle);
          }
        }
      }
    }
  }

  .theme-categories {
    margin-bottom: 3rem;

    .theme-category {
      margin-bottom: 3rem;

      .category-header {
        margin-bottom: 1.5rem;

        h2 {
          font-size: 1.8rem;
          color: var(--color-text-primary);
          margin-bottom: 0.5rem;
        }

        p {
          color: var(--color-text-secondary);
          margin-bottom: 0;
          font-size: 1.1rem;
        }
      }

      .theme-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;

        .theme-card {
          background: var(--color-background-secondary);
          border: 2px solid var(--color-border-subtle);
          border-radius: var(--global-radius-lg);
          padding: 1.5rem;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: var(--color-border-interactive);
            transform: translateY(-2px);
            box-shadow: var(--global-elevation-02);
          }

          &.active {
            border-color: var(--color-brand-primary);
            background: var(--color-surface-subtle-hover);
          }

          .theme-preview-colors {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 1rem;

            .color-swatch {
              width: 40px;
              height: 40px;
              border-radius: var(--global-radius-md);
              border: 2px solid var(--color-border-default);

              &.primary {
                flex: 2;
              }

              &.secondary {
                flex: 1;
              }
            }
          }

          .theme-info {
            h3 {
              color: var(--color-text-primary);
              margin-bottom: 0.5rem;
              font-size: 1.2rem;
            }

            p {
              color: var(--color-text-secondary);
              margin-bottom: 1rem;
              font-size: 0.9rem;
              line-height: 1.4;
            }

            .theme-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 0.5rem;

              .tag {
                background: var(--color-surface-subtle-hover);
                color: var(--color-text-secondary);
                padding: 0.25rem 0.75rem;
                border-radius: var(--global-radius-sm);
                font-size: 0.8rem;
                font-weight: 500;
                border: 1px solid var(--color-border-subtle);
              }
            }
          }
        }
      }
    }
  }

  .component-preview {
    margin-bottom: 3rem;

    h2 {
      font-size: 1.8rem;
      color: var(--color-text-primary);
      margin-bottom: 0.5rem;
    }

    > p {
      color: var(--color-text-secondary);
      margin-bottom: 2rem;
    }

    .preview-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;

      .preview-item {
        background: var(--color-background-secondary);
        border: 1px solid var(--color-border-default);
        border-radius: var(--global-radius-lg);
        padding: 1.5rem;

        h3 {
          color: var(--color-text-primary);
          margin-bottom: 1rem;
          font-size: 1.2rem;
        }

        .button-showcase {
          display: flex;
          flex-wrap: wrap;
          gap: 0.75rem;

          .demo-button {
            background: var(--color-brand-primary);
            color: var(--color-text-on-primary);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: var(--global-radius-md);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background: var(--color-brand-primary-hover);
              transform: translateY(-1px);
            }

            &.secondary {
              background: var(--color-brand-secondary);

              &:hover {
                background: var(--color-brand-secondary-hover);
              }
            }

            &.tertiary {
              background: var(--color-brand-tertiary);

              &:hover {
                background: var(--color-brand-tertiary);
                opacity: 0.9;
              }
            }

            &.quaternary {
              background: var(--color-brand-quaternary);

              &:hover {
                background: var(--color-brand-quaternary);
                opacity: 0.9;
              }
            }
          }
        }

        .color-palette {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 0.75rem;

          .color-swatch {
            height: 60px;
            border-radius: var(--global-radius-md);
            border: 2px solid var(--color-border-default);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;

            &.primary {
              background: var(--color-brand-primary);
            }

            &.secondary {
              background: var(--color-brand-secondary);
            }

            &.tertiary {
              background: var(--color-brand-tertiary);
            }

            &.quaternary {
              background: var(--color-brand-quaternary);
            }

            &.quinary {
              background: var(--color-brand-quinary);
            }

            &.senary {
              background: var(--color-brand-senary);
            }

            .color-label {
              color: white;
              font-size: 0.8rem;
              font-weight: 600;
              text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            }
          }
        }

        .text-showcase {
          display: flex;
          flex-direction: column;
          gap: 1rem;

          .text-sample {
            padding: 1rem;
            border-radius: var(--global-radius-md);
            border: 1px solid var(--color-border-default);

            &.primary {
              background: var(--color-brand-primary);
              color: var(--color-text-on-primary);
            }

            &.secondary {
              background: var(--color-brand-secondary);
              color: var(--color-text-on-secondary);
            }

            h4 {
              margin-bottom: 0.5rem;
              font-size: 1rem;
            }

            p {
              font-size: 0.9rem;
              margin: 0;
            }
          }

          .background-sample {
            padding: 1rem;
            border-radius: var(--global-radius-md);
            border: 1px solid var(--color-border-default);
            text-align: center;
            font-weight: 500;

            &.primary {
              background: var(--color-background-primary);
              color: var(--color-text-primary);
            }

            &.secondary {
              background: var(--color-background-secondary);
              color: var(--color-text-secondary);
            }
          }
        }

        .interactive-showcase {
          display: flex;
          flex-direction: column;
          gap: 1rem;

          .demo-button {
            background: var(--color-brand-primary);
            color: var(--color-text-on-primary);
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: var(--global-radius-md);
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background: var(--color-brand-primary-hover);
            }
          }

          .demo-input {
            input {
              width: 100%;
              padding: 0.75rem;
              border: 1px solid var(--color-border-default);
              border-radius: var(--global-radius-md);
              background: var(--color-background-primary);
              color: var(--color-text-primary);
              font-size: 1rem;

              &:focus {
                outline: none;
                border-color: var(--color-border-focus);
                box-shadow: 0 0 0 3px rgba(var(--rgb-brand-primary), 0.1);
              }

              &::placeholder {
                color: var(--color-text-placeholder);
              }
            }
          }

          .demo-link {
            a {
              color: var(--color-text-interactive);
              text-decoration: none;
              font-weight: 500;

              &:hover {
                color: var(--color-text-interactive-hover);
                text-decoration: underline;
              }
            }
          }
        }

        .spacing-showcase {
          display: flex;
          flex-direction: column;
          gap: 1rem;

          .spacing-demo {
            padding: 1rem;
            background: var(--color-background-secondary);
            border: 1px solid var(--color-border-default);
            text-align: center;
            font-weight: 500;

            &.small {
              border-radius: var(--global-radius-sm);
            }

            &.medium {
              border-radius: var(--global-radius-md);
            }

            &.large {
              border-radius: var(--global-radius-lg);
            }
          }
        }

        .navigation-showcase {
          display: flex;
          flex-direction: column;
          gap: 2rem;

          h4 {
            color: var(--color-text-primary);
            margin: 0 0 1rem 0;
            font-size: 1.1rem;
            font-weight: 600;
          }

          ava-tabs {
            margin-bottom: 1rem;
          }

          // Style tab content panels
          ::ng-deep .ava-tabs__panel {
            margin-top: 1rem;
            padding: 1.5rem;
            border: 1px solid var(--color-border-default);
            border-radius: var(--global-radius-lg);
            background: var(--color-background-secondary);
            min-height: 80px;
            display: flex;
            align-items: center;

            p {
              margin: 0;
              color: var(--color-text-secondary);
              line-height: 1.6;
            }
          }
        }

        .feedback-showcase {
          display: flex;
          flex-direction: column;
          gap: 2rem;

          .spinner-section,
          .progress-section {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            h4 {
              color: var(--color-text-primary);
              margin: 0 0 0.5rem 0;
              font-size: 1rem;
              font-weight: 600;
            }
          }

          .spinner-section {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            ava-spinner {
              margin-right: 1rem;
            }

            // Create a row of spinners
            display: flex;
            flex-direction: row;
            align-items: center;
            flex-wrap: wrap;
            gap: 1.5rem;
          }

          .progress-section {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;

            ava-progressbar {
              margin-bottom: 0.5rem;
            }
          }
        }
        .data-showcase {
          display: flex;
          flex-direction: column;
          gap: 2rem;

          .badges-section,
          .avatars-section {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            h4 {
              color: var(--color-text-primary);
              margin: 0 0 0.5rem 0;
              font-size: 1rem;
              font-weight: 600;
            }
          }

          .badges-section {
            display: flex;
            flex-direction: row;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;

            ava-badges {
              margin-right: 0.5rem;
            }
          }

          .avatars-section {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 1.5rem;

            ava-avatars {
              margin-right: 0.5rem;
            }
          }
        }
        .form-showcase {
          display: flex;
          flex-direction: column;
          gap: 2rem;

          .input-section,
          .selection-section,
          .range-section,
          .dropdown-section {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            h4 {
              color: var(--color-text-primary);
              margin: 0 0 0.5rem 0;
              font-size: 1rem;
              font-weight: 600;
            }
          }

          .input-section {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;

            ava-textbox,
            ava-textarea {
              margin-bottom: 0.5rem;
            }
          }

          .selection-section {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            ava-checkbox,
            ava-radio-button,
            ava-toggle {
              margin-bottom: 0.5rem;
            }
          }

          .range-section {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;

            ava-slider {
              margin-bottom: 0.5rem;
            }
          }

          .dropdown-section {
            display: flex;
            flex-direction: column;
            gap: 1rem;

            ava-dropdown {
              margin-bottom: 0.5rem;
            }
          }
        }
      }
    }
  }

  .theme-features {
    margin-bottom: 3rem;

    h2 {
      font-size: 1.8rem;
      color: var(--color-text-primary);
      margin-bottom: 2rem;
      text-align: center;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 2rem;

      .feature {
        background: var(--color-background-secondary);
        border: 1px solid var(--color-border-default);
        border-radius: var(--global-radius-lg);
        padding: 2rem;
        text-align: center;

        h3 {
          color: var(--color-text-primary);
          margin-bottom: 1rem;
          font-size: 1.3rem;
        }

        p {
          color: var(--color-text-secondary);
          line-height: 1.6;
          margin: 0;
        }
      }
    }
  }

  .theme-comparison {
    h2 {
      font-size: 1.8rem;
      color: var(--color-text-primary);
      margin-bottom: 2rem;
      text-align: center;
    }

    .comparison-table {
      background: var(--color-background-secondary);
      border: 1px solid var(--color-border-default);
      border-radius: var(--global-radius-lg);
      overflow: hidden;

      table {
        width: 100%;
        border-collapse: collapse;

        th,
        td {
          padding: 1rem;
          text-align: left;
          border-bottom: 1px solid var(--color-border-subtle);
        }

        th {
          background: var(--color-surface-subtle-hover);
          color: var(--color-text-primary);
          font-weight: 600;
          font-size: 0.9rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        td {
          color: var(--color-text-secondary);
          vertical-align: middle;

          .color-indicator {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: var(--global-radius-sm);
            border: 1px solid var(--color-border-default);
            margin-right: 0.5rem;
            vertical-align: middle;
          }

          .category-badge {
            padding: 0.25rem 0.75rem;
            border-radius: var(--global-radius-sm);
            font-size: 0.8rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;

            &.default {
              background: var(--color-brand-primary);
              color: var(--color-text-on-primary);
            }

            &.dark {
              background: var(--color-brand-primary);
              color: var(--color-text-on-primary);
            }

            &.acme {
              background: var(--color-brand-primary);
              color: var(--color-text-on-primary);
            }
          }
        }

        tr:hover {
          background: var(--color-surface-subtle-hover);
        }
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .theme-preview {
    padding: 1rem;

    .theme-header {
      h1 {
        font-size: 2rem;
      }

      p {
        font-size: 1rem;
      }

      .theme-stats {
        flex-direction: column;
        gap: 1rem;
      }
    }

    .current-theme-info {
      .current-theme-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
      }

      .color-preview {
        flex-direction: column;
        gap: 1rem;
      }
    }

    .theme-categories .theme-category .theme-grid {
      grid-template-columns: 1fr;
    }

    .component-preview .preview-grid {
      grid-template-columns: 1fr;
    }

    .theme-features .features-grid {
      grid-template-columns: 1fr;
    }

    .theme-comparison .comparison-table {
      overflow-x: auto;

      table {
        min-width: 600px;
      }
    }
  }
}
