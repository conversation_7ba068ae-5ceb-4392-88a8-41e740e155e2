import { Component } from '@angular/core';
import { TableContentComponent } from "../../../../../play-comp-library/src/lib/components/table-content/table-content.component";
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-app-tablecontentcell',
  imports: [CommonModule],
  templateUrl: './app-tablecontentcell.component.html',
  styleUrls: ['./app-tablecontentcell.component.scss']
})
export class AppTablecontentcellComponent {

  // table columns definition
  columns = [
    { header: 'Project Name', field: 'projectName', isSortable: true, ariaSort: null },
    { header: 'Req ID', field: 'reqId', isSortable: false, ariaSort: null },
    { header: 'Document Type', field: 'documentType', isSortable: true, ariaSort: null },
    { header: 'Created On', field: 'createdOn', isSortable: true, ariaSort: null },
    { header: 'Source', field: 'source', isSortable: true, ariaSort: null },
    { header: 'Action', field: 'action', isSortable: true, ariaSort: null },
    { header: 'Artifacts ID', field: 'artifactsId', isSortable: true, ariaSort: null },
    { header: 'Status', field: 'status', isSortable: true, ariaSort: null },
    { header: 'App Domain', field: 'appDomain', isSortable: true, ariaSort: null },
    { header: 'Project ID', field: 'projectId', isSortable: true, ariaSort: null },
    { header: 'App Name', field: 'appName', isSortable: true, ariaSort: null },
    { header: 'Request Type', field: 'requestType', isSortable: true, ariaSort: null },
  ];

  // // sample rows
  // rows = [
  //   { projectName: 'projectName', reqId: '01', documentType: 'DocumentABC', createdOn: 'DD/MM/YYYY', source: 'Project Name', action: '', artifactsId: 'Artifacts ID', status: 'Completed', appDomain: 'App domain', projectId: 'P Id', appName: 'App Name', requestType: 'Request Type' },
  //   { projectName: 'projectName', reqId: '01', documentType: 'DocumentABC', createdOn: 'DD/MM/YYYY', source: 'Project Name', action: '', artifactsId: 'Artifacts ID', status: 'Error', appDomain: 'App domain', projectId: 'P Id', appName: 'App Name', requestType: 'Request Type' },
  //   { projectName: 'projectName', reqId: '01', documentType: 'DocumentABC', createdOn: 'DD/MM/YYYY', source: 'Project Name', action: '', artifactsId: 'Artifacts ID', status: 'Processing', appDomain: 'App domain', projectId: 'P Id', appName: 'App Name', requestType: 'Request Type' },
  // ];

  // // Dynamic status passed to the child
  // statuses = ['Completed', 'Error', 'Processing']; // Example statuses
}
