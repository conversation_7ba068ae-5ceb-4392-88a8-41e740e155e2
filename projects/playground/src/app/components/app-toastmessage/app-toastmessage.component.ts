import { Component, ElementRef, HostListener, ViewChild, ViewEncapsulation, On<PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { ToastMessagesComponent } from "../../../../../play-comp-library/src/lib/components/toast-messages/toast-messages.component";
import { CommonModule } from '@angular/common';
import { IconsComponent } from '../../../../../play-comp-library/src/lib/components/icons/icons.component';

@Component({
  selector: 'app-toastmessage-documentation',
  imports: [ToastMessagesComponent, CommonModule, IconsComponent],
  templateUrl: './app-toastmessage.component.html',
  styleUrls: ['./app-toastmessage.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class AppToastmessageComponent implements OnDestroy {
  @ViewChild('codeBlock') codeBlock!: ElementRef;

  constructor(private cdr: ChangeDetectorRef) {}

  // Cycling animation properties
  currentToastIndex = 0;
  cyclingInterval: any;
  cyclingTimeout: any;
  showToast = false;
  cyclingActive = false;

  // Toast configurations for cycling
  animatedToasts = [
    {
      title: "Generated Successfully",
      message: "Everything went smoothly. You're good to go!",
      type: "success" as const,
      color: "#00DF80"
    },
    {
      title: "Action Required",
      message: "Incomplete fields. Please fill in all required information now",
      type: "warning" as const,
      color: "#FFD21E"
    },
    {
      title: "Error Occurred",
      message: "Connection error. Unable to connect to the server at present",
      type: "error" as const,
      color: "#F04248"
    }
  ];

  sections = [
    {
      title: 'Success Toast',
      description: 'Demonstrates a toast message with the success type.',
      showCode: false,
    },
    {
      title: 'Warning Toast',
      description: 'Demonstrates a toast message with the warning type.',
      showCode: false,
    },
    {
      title: 'Error Toast',
      description: 'Demonstrates a toast message with the error type.',
      showCode: false,
    },
    {
      title: 'Info Toast',
      description: 'Demonstrates a toast message with the info type.',
      showCode: false,
    },
    {
      title: 'Animated Toast',
      description: 'Displays toast messages cycling through different types automatically for 2 minutes.',
      showCode: false,
    },
    {
      title: 'Large Toast',
      description: 'Demonstrates a toast message with the large size variant.',
      showCode: false,
    }
  ];

  apiProps = [
    { name: 'title', type: 'string', default: '\'Notification\'', description: 'The title of the toast message.' },
    { name: 'message', type: 'string', default: '\'This is a toast message.\'', description: 'The message content of the toast.' },
    { name: 'type', type: '\'success\' | \'error\' | \'info\' | \'warning\'', default: '\'info\'', description: 'The type of the toast message.' },
    { name: 'href', type: 'string', default: '\'#\'', description: 'The URL for the action link.' },
    { name: 'actionLinkLabel', type: 'string', default: '\'\'', description: 'The label for the action link.' },
    { name: 'animation', type: 'boolean', default: 'false', description: 'Whether to enable animation for the toast message.' },
    { name: 'showCloseButton', type: 'boolean', default: 'false', description: 'Whether to show the close button.' },
    { name: 'size', type: '\'small\' | \'large\'', default: '\'large\'', description: 'The size of the toast message.' },
    { name: 'color', type: 'string', default: '\'\'', description: 'The color of the toast message.' },
    { name: 'theme', type: '\'light\' | \'dark\'', default: '\'light\'', description: 'The theme of the toast message.' }
  ];

  ngOnInit() {
    this.startAnimatedToastCycling();
  }

  ngOnDestroy() {
    this.stopAnimatedToastCycling();
  }

  startAnimatedToastCycling() {
    if (this.cyclingActive) return;
    
    this.cyclingActive = true;
    this.currentToastIndex = 0;
    this.showNextToast();

    // Stop cycling after 2 minutes (120000ms)
    this.cyclingTimeout = setTimeout(() => {
      this.stopAnimatedToastCycling();
    }, 240000);
  }

  showNextToast() {
    if (!this.cyclingActive) return;

    // Show current toast
    this.showToast = true;
    this.cdr.detectChanges();

    // Schedule next toast after 9 seconds (8s animation + 1s buffer)
    setTimeout(() => {
      if (!this.cyclingActive) return;
      
      // Hide current toast
      this.showToast = false;
      
      // Move to next toast after brief delay
      setTimeout(() => {
        if (!this.cyclingActive) return;
        
        this.currentToastIndex = (this.currentToastIndex + 1) % this.animatedToasts.length;
        this.showNextToast(); // Recursively show next toast
      }, 100);
    }, 9000);
  }

  stopAnimatedToastCycling() {
    this.cyclingActive = false;
    this.showToast = false;
    
    if (this.cyclingInterval) {
      clearInterval(this.cyclingInterval);
      this.cyclingInterval = null;
    }
    if (this.cyclingTimeout) {
      clearTimeout(this.cyclingTimeout);
      this.cyclingTimeout = null;
    }
  }

  getCurrentAnimatedToast() {
    return this.animatedToasts[this.currentToastIndex];
  }

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation();
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  @HostListener('document:click', ['$event'])
  clickOutside(event: MouseEvent) {
    if (this.codeBlock && !this.codeBlock.nativeElement.contains(event.target)) {
      this.sections.forEach(section => section.showCode = false);
    }
  }

  getToastCode(sectionTitle: string): string {
    const examples: Record<string, string> = {
      'success toast': `
import { Component } from '@angular/core';
import { ToastMessagesComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-success-toast',
  standalone: true,
  imports: [ToastMessagesComponent],
  template: \`
    <section class="comp-container">
    <awe-toast-messages title="Generated Successfully" message="Everything went smoothly. You're good to go!" theme="dark" size="small" type="success"  color="#01B48D" ></awe-toast-messages>
    <awe-toast-messages title="Generated Successfully"  theme="dark" size="small" type="success" color="#01B48D" ></awe-toast-messages>
    <awe-toast-messages message="Everything went smoothly. You're good to go!" theme="dark" size="small" type="success" color="#01B48D" ></awe-toast-messages>
    <awe-toast-messages title="Header" message="This is a long line description of information message." type="success" [showCloseButton]="true" actionLinkLabel="Click Here" type="success" color="#01B48D" ></awe-toast-messages>
    <awe-toast-messages title="Header" message="This is a long line description of information message." type="success" [showCloseButton]="true" type="success" color="#01B48D" ></awe-toast-messages>
    <awe-toast-messages title="Header" message="This is a long line description of information message."  [showCloseButton]="true"></awe-toast-messages>

    </section>
  \`
})
export class SuccessToastComponent {}`,
      'warning toast': `
import { Component } from '@angular/core';
import { ToastMessagesComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-warning-toast',
  standalone: true,  
  imports: [ToastMessagesComponent],
  template: \`
    <section class="comp-container">
      <awe-toast-messages  title="Action Required" message="Incomplete fields. Please fill in all required information now" theme="dark" size="small" type="warning" color="#FFD21E"></awe-toast-messages>
      <awe-toast-messages title="Header" message="This is a long line description of information message." type="warning" href="" actionLinkLabel="Click Here"></awe-toast-messages>
    </section>
  \`
})
export class WarningToastComponent {}`,
      'error toast': `
import { Component } from '@angular/core';
import { ToastMessagesComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-error-toast',
  standalone: true,
  imports: [ToastMessagesComponent],
  template: \`
    <section class="comp-container">
      <awe-toast-messages title="Error Occurred" message="Connection error. Unable to connect to the server at present" theme="dark" size="small" type="error" color="#F04248"></awe-toast-messages>
      <awe-toast-messages
        title="Header"
        message="This is a long line description of information message." type="error"
        href="" actionLinkLabel="Click Here"
        ></awe-toast-messages>
    </section>
  \`
})
export class ErrorToastComponent {}`,
      'info toast': `
import { Component } from '@angular/core';
import { ToastMessagesComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-info-toast',
  standalone: true,
  imports: [ToastMessagesComponent],
  template: \`
    <section class="comp-container">
      <awe-toast-messages title="Header" message="This is a long line description of information message." type="info" href="" actionLinkLabel="Click Here"></awe-toast-messages>
    </section>
  \`
})
export class InfoToastComponent {}`,
      'animated toast': `
import { Component, OnInit, OnDestroy, ChangeDetectorRef } from '@angular/core';
import { ToastMessagesComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-animated-toast',
  standalone: true,
  imports: [ToastMessagesComponent],
  template: \`
    <section class="comp-container">
      <awe-toast-messages title="Generated Successfully" message="Everything went smoothly. You're good to go!" theme="dark" size="small" type="success"  color="#01B48D" ></awe-toast-messages>
      <awe-toast-messages  title="Action Required" message="Incomplete fields. Please fill in all required information now" theme="dark" size="small" type="warning" color="#FFD21E"></awe-toast-messages>
      <awe-toast-messages title="Error Occurred" message="Connection error. Unable to connect to the server at present" theme="dark" size="small" type="error" color="#F04248"></awe-toast-messages>
      <awe-toast-messages title="Header" [animation]="true" message="This is a long line description of information message." type="success" href="" actionLinkLabel="Click Here"></awe-toast-messages>
    </section>
  \`
})
export class AnimatedToastComponent {}
 
}`,
    'large toast': `
import { Component } from '@angular/core';
import { ToastMessagesComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-large-toast',
  standalone: true,
  imports: [ToastMessagesComponent],
  template: \`
    <section class="comp-container">
      <awe-toast-messages
        title="Notification"
        message="This is a large toast message."
        type="info"
        size="large">
      </awe-toast-messages>
    </section>
  \`
})
export class LargeToastComponent {}`
    };

    return examples[sectionTitle.toLowerCase()] || '';
  }

  copyCode(section: string): void {
    const code = this.getToastCode(section);
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy code:', err);
    });
  }
}