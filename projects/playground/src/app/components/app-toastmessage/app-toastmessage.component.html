<div class="documentation">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Toast Messages Component</h1>
        <p class="description">
          A versatile toast message component that displays notifications with different types and animations. Built with accessibility and user experience in mind.
        </p>
      </header>
    </div>
  </div>

  <!-- Installation -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} ToastMessagesComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section
      *ngFor="let section of sections; let i = index"
      class="doc-section"
    >
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'" iconColor="action"></awe-icons>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <ng-container *ngSwitchCase="'Success Toast'">
              <awe-toast-messages header="Generated Successfully" message="Everything went smoothly. You're good to go!" theme="dark" size="small" type="success"></awe-toast-messages>
              <awe-toast-messages header="Generated Successfully" theme="dark" size="small" type="success"></awe-toast-messages>
              <awe-toast-messages message="Everything went smoothly. You're good to go!" theme="dark" size="small" type="success"></awe-toast-messages>

              <awe-toast-messages header="Header" message="This is a long line description of information message." type="success" [showCloseButton]="true" actionLinkLabel="Click Here"></awe-toast-messages>
              <awe-toast-messages header="Header" message="This is a long line description of information message." type="success" [showCloseButton]="true"></awe-toast-messages>
              <awe-toast-messages header="Header" message="This is a long line description of information message." [showCloseButton]="true"></awe-toast-messages>
            </ng-container>

            <ng-container *ngSwitchCase="'Warning Toast'">
              <awe-toast-messages header="Action Required" message="Incomplete fields. Please fill in all required information now" theme="dark" size="small" type="warning"></awe-toast-messages>
              <awe-toast-messages header="Header" message="This is a long line description of information message." type="warning" actionLinkLabel="Click Here"></awe-toast-messages>
            </ng-container>

            <ng-container *ngSwitchCase="'Error Toast'">
              <awe-toast-messages header="Error Occurred" message="Connection error. Unable to connect to the server at present" theme="dark" size="small" type="error"></awe-toast-messages>
              <awe-toast-messages header="Header" message="This is a long line description of information message." type="error" href="" actionLinkLabel="Click Here"></awe-toast-messages>
            </ng-container>

            <ng-container *ngSwitchCase="'Info Toast'">
              <awe-toast-messages header="Header" message="This is a long line description of information message." type="info" href="" actionLinkLabel="Click Here"></awe-toast-messages>
            </ng-container>

            <ng-container *ngSwitchCase="'Animated Toast'" class="animated">
              <div class="cycling-toast-container">
                <!-- Cycling small animated toast - completely destroy and recreate component -->
                <div *ngIf="showToast">
                  <awe-toast-messages 
                    [header]="getCurrentAnimatedToast().title" 
                    [message]="getCurrentAnimatedToast().message"
                    [animation]="true" 
                    theme="dark" 
                    size="small" 
                    [type]="getCurrentAnimatedToast().type">
                  </awe-toast-messages>
                </div>
              </div>
              
              <!-- Static large animated toast -->
              <awe-toast-messages 
                header="Header" 
                [animation]="true" 
                message="This is a long line description of information message." 
                type="success" 
                href="" 
                actionLinkLabel="Click Here">
              </awe-toast-messages>
            </ng-container>

            <ng-container *ngSwitchCase="'Large Toast'">
              <awe-toast-messages header="Notification" message="This is a large toast message." type="info" size="large"></awe-toast-messages>
            </ng-container>
          </ng-container>
        </div>

        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getToastCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <awe-icons iconName="awe_copy" *ngIf="section.title!=='Available Icons'"></awe-icons>
          </button>
        </div>
      </div>
    </section>
  </div>

  <!-- API Reference -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section api-reference">
        <h2>API Reference</h2>
        <table class="api-table">
          <thead>
            <tr>
              <th>Property</th>
              <th>Type</th>
              <th>Default</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let prop of apiProps">
              <td><code>{{ prop.name }}</code></td>
              <td><code>{{ prop.type }}</code></td>
              <td><code>{{ prop.default }}</code></td>
              <td>{{ prop.description }}</td>
            </tr>
          </tbody>
        </table>
      </section>
    </div>
  </div>
</div>