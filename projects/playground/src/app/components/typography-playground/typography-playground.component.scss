.typography-playground {
    padding: 2rem;
    background: #f8fafc;
    min-height: 100vh;
    font-family: 'Inter', sans-serif;

    .container {
        max-width: 1400px;
        margin: 0 auto;
    }

    .playground-title {
        font-size: 3rem;
        font-weight: 700;
        color: #1e293b;
        margin-bottom: 1rem;
        text-align: center;
        font-family: 'Mulish', sans-serif;
    }

    .playground-description {
        font-size: 1.125rem;
        color: #64748b;
        text-align: center;
        margin-bottom: 3rem;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
        line-height: 1.6;
    }

    .font-section {
        background: white;
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border: 1px solid #e2e8f0;

        .section-title {
            font-size: 2rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 0.5rem;
            font-family: 'Mulish', sans-serif;
        }

        .section-description {
            font-size: 1rem;
            color: #64748b;
            margin-bottom: 2rem;
            line-height: 1.5;
        }
    }

    .weight-section {
        margin-bottom: 2.5rem;

        .weight-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #334155;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e2e8f0;
            font-family: 'Mulish', sans-serif;
        }
    }

    .class-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1rem;

        &.variants-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        }
    }

    .class-example {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 1.5rem;
        transition: all 0.2s ease;

        &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            border-color: #cbd5e1;
        }

        .class-name {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.875rem;
            color: #7c3aed;
            background: #f3e8ff;
            padding: 0.5rem 0.75rem;
            border-radius: 6px;
            margin-bottom: 1rem;
            font-weight: 500;
            border: 1px solid #ddd6fe;
        }

        .class-demo {
            margin-bottom: 1rem;
            padding: 1rem;
            background: white;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            min-height: 60px;
            display: flex;
            align-items: center;
            word-break: break-word;
        }

        .class-info {
            display: flex;
            gap: 0.75rem;
            flex-wrap: wrap;

            span {
                font-size: 0.75rem;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.05em;

                &.font-family {
                    background: #dbeafe;
                    color: #1d4ed8;
                    border: 1px solid #93c5fd;
                }

                &.font-weight {
                    background: #fef3c7;
                    color: #d97706;
                    border: 1px solid #fcd34d;
                }

                &.font-size {
                    background: #dcfce7;
                    color: #16a34a;
                    border: 1px solid #86efac;
                }

                &.font-style {
                    background: #f3e8ff;
                    color: #7c3aed;
                    border: 1px solid #ddd6fe;
                }
            }
        }
    }

    .utility-section {
        margin-bottom: 2rem;

        .utility-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #334155;
            margin-bottom: 1rem;
            font-family: 'Mulish', sans-serif;
        }

        .utility-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .utility-example {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 1rem;
            transition: all 0.2s ease;

            &:hover {
                transform: translateY(-1px);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            }

            .class-name {
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 0.875rem;
                color: #7c3aed;
                background: #f3e8ff;
                padding: 0.5rem 0.75rem;
                border-radius: 6px;
                margin-bottom: 0.75rem;
                font-weight: 500;
                border: 1px solid #ddd6fe;
            }

            .class-demo {
                padding: 0.75rem;
                background: white;
                border-radius: 6px;
                border: 1px solid #e2e8f0;
                min-height: 50px;
                display: flex;
                align-items: center;
                word-break: break-word;
            }
        }
    }

    .responsive-demo {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 2rem;

        .responsive-text {
            margin-bottom: 1rem;
            padding: 1rem;
            background: white;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
            text-align: center;
            font-family: 'Mulish', sans-serif;

            &:last-child {
                margin-bottom: 0;
            }
        }
    }
}

// Responsive Design
@media (max-width: 768px) {
    .typography-playground {
        padding: 1rem;

        .playground-title {
            font-size: 2rem;
        }

        .font-section {
            padding: 1.5rem;
        }

        .class-grid {
            grid-template-columns: 1fr;
        }

        .utility-grid {
            grid-template-columns: 1fr;
        }
    }
}

@media (max-width: 480px) {
    .typography-playground {
        .playground-title {
            font-size: 1.75rem;
        }

        .font-section {
            padding: 1rem;
        }

        .class-example {
            padding: 1rem;
        }

        .class-info {
            flex-direction: column;
            gap: 0.5rem;
        }
    }
}