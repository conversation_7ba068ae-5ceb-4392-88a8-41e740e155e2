import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'app-typography-playground',
    standalone: true,
    imports: [CommonModule],
    templateUrl: './typography-playground.component.html',
    styleUrl: './typography-playground.component.scss'
})
export class TypographyPlaygroundComponent {

    // Mulish Primary Font Classes
    mulishClasses = {
        light: ['25', '50', '75', '100', '200', '300', '400', '500', '600', '700', '800'],
        regular: ['25', '50', '75', '100', '200', '300', '400', '500', '600', '700', '800'],
        medium: ['25', '50', '75', '100', '200', '300', '400', '500', '600', '700', '800'],
        bold: ['25', '50', '75', '100', '200', '300', '400', '500', '600', '700', '800']
    };

    // Inter Secondary Font Classes
    interClasses = {
        light: ['25', '50', '75', '100', '200', '300', '400', '500', '600', '700', '800'],
        regular: ['25', '50', '75', '100', '200', '300', '400', '500', '600', '700', '800'],
        medium: ['25', '50', '75', '100', '200', '300', '400', '500', '600', '700', '800'],
        bold: ['25', '50', '75', '100', '200', '300', '400', '500', '600', '700', '800']
    };

    // Utility Classes
    utilityClasses = {
        fontFamily: ['font-primary', 'font-secondary'],
        textSizes: ['text-xs', 'text-sm', 'text-base', 'text-lg', 'text-xl', 'text-2xl', 'text-3xl', 'text-4xl', 'text-5xl'],
        fontWeights: ['font-light', 'font-normal', 'font-medium', 'font-semibold', 'font-bold'],
        lineHeights: ['leading-tight', 'leading-snug', 'leading-normal', 'leading-relaxed', 'leading-loose'],
        letterSpacing: ['tracking-tighter', 'tracking-tight', 'tracking-normal', 'tracking-wide', 'tracking-wider', 'tracking-widest'],
        textAlign: ['text-left', 'text-center', 'text-right', 'text-justify'],
        textTransform: ['uppercase', 'lowercase', 'capitalize', 'normal-case'],
        textDecoration: ['underline', 'line-through', 'no-underline'],
        textStyle: ['text-italic', 'text-underline', 'text-normal']
    };

    // Sample text for demonstration
    sampleText = 'The quick brown fox jumps over the lazy dog';
    sampleTextLong = 'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.';
}
