import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TabsComponent } from '../../../../../../../play-comp-library/src/lib/components/tabs/tabs.component';
import { manyTabs } from '../tabs-demo.data';

@Component({
  selector: 'ava-tabs-dropdown-demo',
  standalone: true,
  imports: [CommonModule, TabsComponent],
  template: `
    <div style="max-width: 700px; margin: 2rem auto;">
      <ava-tabs
        [tabs]="manyTabs"
        [activeTabId]="activeTabId"
        [scrollable]="true"
        [showDropdown]="true"
        [maxVisibleTabs]="4"
        (tabChange)="activeTabId = $event.id"
        ariaLabel="Dropdown tabs demo"
      ></ava-tabs>
      <div
        style="margin-top: 2rem; padding: 1rem; border: 1px solid #eee; border-radius: 8px;"
      >
        <ng-container *ngIf="activeTabId"
          >Tab {{ activeTabId }} Content</ng-container
        >
      </div>
    </div>
  `,
  styles: [
    `
      :host {
        display: block;
        background: #fff;
        border-radius: 8px;
      }
    `,
    `
      ::ng-deep .ava-tabs__panel {
        margin-top: 2rem;
        padding: 1.5rem;
        border: 1px solid #eee;
        border-radius: 8px;
        min-width: 320px;
        min-height: 80px;
        display: flex;
        background: #fff;
      }
    `,
  ],
})
export class AppTabsDropdownDemoComponent {
  manyTabs = manyTabs;
  activeTabId = this.manyTabs[0]?.id || 'tab1';
}
