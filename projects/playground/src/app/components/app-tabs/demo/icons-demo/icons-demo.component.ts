import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TabsComponent } from '../../../../../../../play-comp-library/src/lib/components/tabs/tabs.component';
import { iconTabs } from '../tabs-demo.data';

@Component({
  selector: 'ava-tabs-icons-demo',
  standalone: true,
  imports: [CommonModule, TabsComponent],
  template: `
    <div style="max-width: 800px; margin: 2rem auto;">
      <ava-tabs
        [tabs]="iconTabs"
        [activeTabId]="activeTabId"
        variant="icon"
        (tabChange)="activeTabId = $event.id"
        ariaLabel="Tabs with icons demo"
      ></ava-tabs>
    </div>
  `,
  styles: [
    `
      :host {
        display: block;
        margin-top: 0;
      }
    `,
    `
      ::ng-deep .ava-tabs__panel {
        margin-top: 2rem;
        padding: 1.5rem;
        border: 1px solid #eee;
        border-radius: 8px;
        min-width: 320px;
        min-height: 80px;
        display: flex;
        margin-top: 0;
      }
    `,
  ],
})
export class AppTabsIconsDemoComponent {
  iconTabs = iconTabs;
  activeTabId = this.iconTabs[0]?.id || 'dashboard';
}
