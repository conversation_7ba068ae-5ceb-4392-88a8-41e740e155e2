import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

import { TabsComponent } from '../../../../../../../play-comp-library/src/lib/components/tabs/tabs.component';
import { buttonTabs, iconTabs } from '../tabs-demo.data';

@Component({
  selector: 'ava-tabs-variants-demo',
  standalone: true,
  imports: [CommonModule, TabsComponent],
  template: `
    <div class="tabs-demo-wrapper">
      <ava-tabs
        [tabs]="buttonTabs"
        [activeTabId]="activeTabId3"
        (tabChange)="activeTabId3 = $event.id"
      ></ava-tabs>
      <ava-tabs
        [tabs]="buttonTabs"
        [activeTabId]="activeTabId1"
        variant="button"
        (tabChange)="activeTabId1 = $event.id"
      ></ava-tabs>
      <ava-tabs
        [tabs]="iconTabs"
        [activeTabId]="activeTabId2"
        variant="icon"
        (tabChange)="activeTabId2 = $event.id"
      ></ava-tabs>
    </div>
  `,
  styles: [
    `
      .tabs-demo-wrapper {
        max-width: 800px;
        margin: 2rem auto;
        display: flex;
        flex-direction: column;
        gap: 2.5rem;
        align-items: center;
      }
      ::ng-deep .ava-tabs__panel {
        margin-top: 2rem;
        padding: 1.5rem;
        border: 1px solid #eee;
        border-radius: 8px;
        min-width: 320px;
        min-height: 80px;
        display: flex;
        margin-top: 0;
      }
      :host {
        display: block;
        border-radius: 8px;
      }
    `,
  ],
})
export class AppTabsVariantsDemoComponent {
  buttonTabs = buttonTabs;
  iconTabs = iconTabs;
  activeTabId3 = this.buttonTabs[0]?.id || 'overview';
  activeTabId1 = this.buttonTabs[0]?.id || 'overview';
  activeTabId2 = this.iconTabs[0]?.id || 'dashboard';
}
