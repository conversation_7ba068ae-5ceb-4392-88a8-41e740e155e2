/* ========================================================================
   APP TABS DEMO - COMPREHENSIVE STYLING
   
   Professional demo page showcasing the AVA Tabs component
   ======================================================================== */

.tabs-demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: var(--font-family-body, 'Inter', sans-serif);
  line-height: 1.6;
  color: var(--color-text-primary, #374151);
  background: var(--color-background-primary, #ffffff);

  /* === DEMO HEADER === */
  .demo-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: linear-gradient(135deg, var(--color-brand-primary, #e91e63) 0%, var(--color-brand-secondary, #9c27b0) 100%);
    border-radius: var(--global-radius-lg, 12px);
    color: var(--color-text-on-brand, #ffffff);
    box-shadow: var(--global-elevation-02, 0px 4px 12px rgba(0, 0, 0, 0.1));

    h1 {
      font-size: 2.5rem;
      font-weight: var(--global-font-weight-bold, 700);
      margin: 0 0 0.5rem 0;
      font-family: var(--font-family-heading, 'Mulish', sans-serif);
    }

    p {
      font-size: 1.125rem;
      margin: 0;
      opacity: 0.9;
    }
  }

  /* === DEMO SECTIONS === */
  .demo-section {
    margin-bottom: 3rem;
    padding: 2rem;
    border: 1px solid var(--color-border-subtle, #e5e7eb);
    border-radius: var(--global-radius-lg, 12px);
    background: var(--color-background-primary, #ffffff);
    box-shadow: var(--global-elevation-01, 0px 2px 4px rgba(0, 0, 0, 0.08));
    transition: all var(--global-motion-duration-swift, 150ms) var(--global-motion-easing-standard, cubic-bezier(0.4, 0, 0.2, 1));

    &:hover {
      box-shadow: var(--global-elevation-02, 0px 4px 12px rgba(0, 0, 0, 0.1));
      transform: translateY(-2px);
    }

    h2 {
      font-size: 1.75rem;
      font-weight: var(--global-font-weight-semibold, 600);
      margin: 0 0 0.5rem 0;
      color: var(--color-brand-primary, #e91e63);
      font-family: var(--font-family-heading, 'Mulish', sans-serif);

      &::after {
        content: '';
        display: block;
        width: 3rem;
        height: 3px;
        background: var(--color-brand-primary, #e91e63);
        margin-top: 0.5rem;
        border-radius: 2px;
      }
    }

    h3 {
      font-size: 1.25rem;
      font-weight: var(--global-font-weight-medium, 500);
      margin: 1.5rem 0 1rem 0;
      color: var(--color-text-primary, #374151);
    }

    h4 {
      font-size: 1.125rem;
      font-weight: var(--global-font-weight-medium, 500);
      margin: 1rem 0 0.5rem 0;
      color: var(--color-text-primary, #374151);
    }

    p {
      margin: 0 0 1.5rem 0;
      color: var(--color-text-secondary, #6b7280);
      font-size: 1rem;
    }

    /* Tabs component spacing */
    ava-tabs {
      margin: 1.5rem 0;
    }
  }

  /* === SIZE AND LAYOUT DEMOS === */
  .size-demo,
  .icon-demo,
  .layout-demo {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--color-background-secondary, #f9fafb);
    border-radius: var(--global-radius-md, 8px);
    border: 1px solid var(--color-border-subtle, #e5e7eb);

    h3 {
      margin-top: 0;
      color: var(--color-text-primary, #374151);
      font-size: 1.125rem;
    }

    ava-tabs {
      margin-top: 1rem;
    }
  }

  /* === CUSTOM CONTENT STYLING === */
  .custom-content {
    padding: 2rem;
    background: var(--color-background-secondary, #f9fafb);
    border-radius: var(--global-radius-md, 8px);
    border: 1px solid var(--color-border-subtle, #e5e7eb);

    h3 {
      margin-top: 0;
      margin-bottom: 1.5rem;
      color: var(--color-brand-primary, #e91e63);
      font-size: 1.375rem;
    }
  }

  /* === FORM STYLING === */
  .demo-form {
    max-width: 400px;

    .form-group {
      margin-bottom: 1.5rem;

      label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: var(--global-font-weight-medium, 500);
        color: var(--color-text-primary, #374151);
        font-size: 0.875rem;
      }

      input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 1px solid var(--color-border-default, #d1d5db);
        border-radius: var(--global-radius-md, 6px);
        font-size: 1rem;
        transition: all var(--global-motion-duration-swift, 150ms) var(--global-motion-easing-standard, cubic-bezier(0.4, 0, 0.2, 1));
        background: var(--color-background-primary, #ffffff);

        &:focus {
          outline: none;
          border-color: var(--color-border-focus, #e91e63);
          box-shadow: 0 0 0 3px rgba(233, 30, 99, 0.1);
        }

        &::placeholder {
          color: var(--color-text-placeholder, #9ca3af);
        }
      }
    }

    button[type="submit"] {
      background: var(--color-brand-primary, #e91e63);
      color: var(--color-text-on-brand, #ffffff);
      border: none;
      padding: 0.75rem 1.5rem;
      border-radius: var(--global-radius-md, 6px);
      font-weight: var(--global-font-weight-medium, 500);
      cursor: pointer;
      transition: all var(--global-motion-duration-swift, 150ms) var(--global-motion-easing-standard, cubic-bezier(0.4, 0, 0.2, 1));
      font-size: 1rem;

      &:hover {
        background: var(--color-brand-primary-hover, #c2185b);
        transform: translateY(-1px);
        box-shadow: var(--global-elevation-02, 0px 4px 12px rgba(0, 0, 0, 0.1));
      }

      &:active {
        transform: translateY(0);
      }
    }
  }

  /* === CHART PLACEHOLDER === */
  .chart-placeholder {
    text-align: center;

    p {
      font-size: 1.125rem;
      margin-bottom: 2rem;
      color: var(--color-text-secondary, #6b7280);
    }

    .mock-chart {
      display: flex;
      align-items: end;
      justify-content: center;
      gap: 1rem;
      height: 200px;
      padding: 1rem;
      background: var(--color-background-primary, #ffffff);
      border-radius: var(--global-radius-md, 8px);
      border: 1px solid var(--color-border-subtle, #e5e7eb);

      .bar {
        width: 40px;
        background: linear-gradient(to top, var(--color-brand-primary, #e91e63), var(--color-brand-secondary, #9c27b0));
        border-radius: 4px 4px 0 0;
        transition: all var(--global-motion-duration-standard, 300ms) var(--global-motion-easing-standard, cubic-bezier(0.4, 0, 0.2, 1));
        position: relative;

        &:hover {
          transform: scale(1.05);
          box-shadow: 0 4px 8px rgba(233, 30, 99, 0.3);
        }

        &::after {
          content: '';
          position: absolute;
          top: -8px;
          left: 50%;
          transform: translateX(-50%);
          width: 8px;
          height: 8px;
          background: var(--color-brand-primary, #e91e63);
          border-radius: 50%;
          opacity: 0;
          transition: opacity var(--global-motion-duration-swift, 150ms);
        }

        &:hover::after {
          opacity: 1;
        }
      }
    }
  }

  /* === TABLE STYLING === */
  .demo-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    background: var(--color-background-primary, #ffffff);
    border-radius: var(--global-radius-md, 8px);
    overflow: hidden;
    box-shadow: var(--global-elevation-01, 0px 2px 4px rgba(0, 0, 0, 0.08));

    thead {
      background: var(--color-brand-primary, #e91e63);
      color: var(--color-text-on-brand, #ffffff);

      th {
        padding: 1rem;
        text-align: left;
        font-weight: var(--global-font-weight-semibold, 600);
        font-size: 0.875rem;
        text-transform: uppercase;
        letter-spacing: 0.05em;
      }
    }

    tbody {
      tr {
        border-bottom: 1px solid var(--color-border-subtle, #e5e7eb);
        transition: background-color var(--global-motion-duration-swift, 150ms) var(--global-motion-easing-standard, cubic-bezier(0.4, 0, 0.2, 1));

        &:hover {
          background: var(--color-surface-subtle-hover, #f9fafb);
        }

        &:last-child {
          border-bottom: none;
        }

        td {
          padding: 1rem;
          font-size: 0.875rem;
          color: var(--color-text-primary, #374151);

          &:first-child {
            font-weight: var(--global-font-weight-medium, 500);
          }
        }
      }
    }
  }

  /* === EVENT INFO === */
  .event-info {
    background: var(--color-background-secondary, #f9fafb);
    padding: 1.5rem;
    border-radius: var(--global-radius-md, 8px);
    border-left: 4px solid var(--color-brand-primary, #e91e63);

    h4 {
      margin-top: 0;
      color: var(--color-brand-primary, #e91e63);
    }

    ul {
      margin: 0;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
        color: var(--color-text-primary, #374151);

        strong {
          color: var(--color-brand-primary, #e91e63);
          font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
          font-size: 0.875rem;
          background: rgba(233, 30, 99, 0.1);
          padding: 0.125rem 0.25rem;
          border-radius: 3px;
        }
      }
    }
  }

  /* === PROPS DOCUMENTATION TABLE === */
  .props-table {
    overflow-x: auto;
    margin-top: 1rem;

    .demo-table {
      min-width: 800px;
      font-size: 0.875rem;

      thead th {
        background: var(--color-brand-secondary, #9c27b0);
        color: var(--color-text-on-brand, #ffffff);
        font-size: 0.8rem;
        padding: 0.875rem;
      }

      tbody {
        td {
          padding: 0.875rem;
          vertical-align: top;

          &:first-child {
            font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
            font-weight: var(--global-font-weight-medium, 500);
            color: var(--color-brand-primary, #e91e63);
            background: rgba(233, 30, 99, 0.05);
          }

          &:nth-child(2) {
            font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
            font-size: 0.8rem;
            color: var(--color-brand-secondary, #9c27b0);
            background: rgba(156, 39, 176, 0.05);
          }

          &:nth-child(3) {
            font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
            font-size: 0.8rem;
            color: var(--color-text-secondary, #6b7280);
            background: rgba(107, 114, 128, 0.05);
          }
        }
      }
    }
  }

  /* === RESPONSIVE DESIGN === */
  @media (max-width: 768px) {
    padding: 1rem;

    .demo-header {
      padding: 1.5rem;
      margin-bottom: 2rem;

      h1 {
        font-size: 2rem;
      }

      p {
        font-size: 1rem;
      }
    }

    .demo-section {
      padding: 1.5rem;
      margin-bottom: 2rem;

      h2 {
        font-size: 1.5rem;
      }
    }

    .size-demo,
    .layout-demo {
      padding: 1rem;
    }

    .custom-content {
      padding: 1.5rem;
    }

    .props-table {
      .demo-table {
        min-width: 600px;
        font-size: 0.8rem;
      }
    }
  }

  @media (max-width: 480px) {
    padding: 0.75rem;

    .demo-header {
      padding: 1rem;

      h1 {
        font-size: 1.75rem;
      }
    }

    .demo-section {
      padding: 1rem;
    }

    .demo-form {
      .form-group {
        input {
          padding: 0.625rem 0.875rem;
        }
      }

      button[type="submit"] {
        padding: 0.625rem 1.25rem;
        font-size: 0.875rem;
      }
    }
  }

  /* === ANIMATION ENHANCEMENTS === */
  .demo-section {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
    transform: translateY(20px);

    @for $i from 1 through 10 {
      &:nth-child(#{$i + 1}) {
        animation-delay: #{$i * 0.1}s;
      }
    }
  }

  /* === SCROLL BEHAVIOR === */
  //

  /* === FOCUS STYLES === */
  // Remove focus border on tabs, icons, and icon buttons
  ava-tabs .ava-tabs__tab:focus,
  ava-tabs .ava-tabs__tab:active,
  ava-tabs .ava-tabs__tab .ava-tabs__tab-icon:focus,
  ava-tabs .ava-tabs__tab .ava-tabs__tab-icon:active,
  ava-tabs .ava-tabs__scroll-btn:focus,
  ava-tabs .ava-tabs__scroll-btn:active,
  ava-tabs .ava-tabs__dropdown-trigger:focus,
  ava-tabs .ava-tabs__dropdown-trigger:active,
  ava-icon:focus,
  ava-icon:active,
  .ava-icon-container:focus,
  .ava-icon-container:active {
    outline: none !important;
    box-shadow: none !important;
  }

  // Keep focus styles for other elements
  *:focus {
    outline: 2px solid var(--color-border-focus, #e91e63);
    outline-offset: 2px;
  }
}

/* === ANIMATIONS === */
@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* === PRINT STYLES === */
@media print {
  .tabs-demo-container {
    box-shadow: none;

    .demo-section {
      break-inside: avoid;
      box-shadow: none;
      border: 1px solid #ccc;
    }

    .demo-header {
      background: #f5f5f5 !important;
      color: #333 !important;
    }
  }
}