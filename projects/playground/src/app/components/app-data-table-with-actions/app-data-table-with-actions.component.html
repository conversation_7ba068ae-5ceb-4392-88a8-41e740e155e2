<div class="data-table-with-actions-demo">
  <!-- <div class="demo-controls-card">
    <ava-card>
      <div header>
        <h3>Demo Controls</h3>
      </div>
      <div content>
        <div class="controls-grid">
          <ava-button
            (click)="toggleLoading()"
            [variant]="loading ? 'warning' : 'primary'"
            size="medium"
            [label]="loading ? 'Stop Loading' : 'Start Loading'"
            [iconName]="loading ? 'pause' : 'play'"
            [iconSize]="16"
          >
          </ava-button>
          <ava-button
            (click)="toggleDisabled()"
            [variant]="disabled ? 'success' : 'secondary'"
            size="medium"
            [label]="disabled ? 'Enable' : 'Disable'"
            [iconName]="disabled ? 'unlock' : 'lock'"
            [iconSize]="16"
          >
          </ava-button>
          <ava-button
            (click)="refreshData()"
            variant="info"
            size="medium"
            label="Refresh Data"
            iconName="refresh-cw"
            [iconSize]="16"
          >
          </ava-button>
        </div>
      </div>
    </ava-card>
  </div> -->

  <div class="demo-section">
    <ava-card>
      <div content>
        <ava-data-table-with-actions
          [config]="dataTableConfig"
          [data]="sampleData"
          [loading]="loading"
          [disabled]="disabled"
          [totalItems]="totalItems"
          [currentPage]="currentPage"
          (actionClick)="onActionClick($event)"
          (bulkActionClick)="onBulkActionClick($event)"
          (selectionChange)="onSelectionChange($event)"
          (pageChange)="onPageChange($event)"
          (searchChange)="onSearchChange($event)"
          (sortChange)="onSortChange($event)"
          (dataTableEvent)="onDataTableEvent($event)"
        >
        </ava-data-table-with-actions>
      </div>
    </ava-card>
  </div>
</div>
