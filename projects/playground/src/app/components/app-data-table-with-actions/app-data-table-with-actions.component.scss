.data-table-with-actions-demo {
  display: flex;
  flex-direction: column;
  gap: 2rem;

  .demo-header {
    text-align: center;
    margin-bottom: 1rem;

    h2 {
      color: #333;
      margin-bottom: 1rem;
      font-size: 2rem;
    }

    p {
      color: #666;
      font-size: 1.1rem;
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .demo-controls-card {
    .controls-grid {
      display: flex;
      gap: 1rem;
      flex-wrap: wrap;
      justify-content: center;
    }
  }

  .demo-section {
    .ava-data-table-with-actions {
      margin: 0;
    }
  }

  .event-log-card {
    .event-content {
      pre {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 1rem;
        margin: 0;
        font-size: 0.875rem;
        overflow-x: auto;
        max-height: 300px;
        overflow-y: auto;
      }
    }
  }

  .features-card {
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;

      .feature-item {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        padding: 1rem;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        transition: all 0.2s ease;

        &:hover {
          background-color: #e9ecef;
          transform: translateY(-2px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        .feature-content {
          flex: 1;
          line-height: 1.5;

          strong {
            color: #007bff;
            font-weight: 600;
          }
        }
      }
    }
  }
}

// Status badge styles for the table
.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;

  &.status-active {
    background-color: #d4edda;
    color: #155724;
  }

  &.status-inactive {
    background-color: #f8d7da;
    color: #721c24;
  }

  &.status-pending {
    background-color: #fff3cd;
    color: #856404;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .data-table-with-actions-demo {
    padding: 1rem;
    gap: 1.5rem;

    .demo-header {
      h2 {
        font-size: 1.5rem;
      }

      p {
        font-size: 1rem;
      }
    }

    .demo-controls-card {
      .controls-grid {
        flex-direction: column;
        align-items: stretch;
      }
    }

    .features-card {
      .features-grid {
        grid-template-columns: 1fr;
        gap: 1rem;

        .feature-item {
          padding: 0.75rem;
        }
      }
    }
  }
}
