import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-icons-sizes',
  standalone: true,
  imports: [CommonModule, IconComponent],
  templateUrl: './icons-sizes.component.html',
  styleUrls: ['./icons-sizes.component.scss'],
})
export class IconsSizesComponent {
  // Size examples with different options
  sizeExamples = [
    { size: 16, label: 'Small (16px)' },
    { size: 24, label: 'Medium (24px)' },
    { size: 32, label: 'Large (32px)' },
    { size: 48, label: 'Extra Large (48px)' },
  ];

  iconName = 'heart'; // Using same icon to show size differences

  onIconClick(event: Event): void {
    console.log('Icon clicked:', event);
  }
}
