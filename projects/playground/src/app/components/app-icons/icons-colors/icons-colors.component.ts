import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-icons-colors',
  standalone: true,
  imports: [CommonModule, IconComponent],
  templateUrl: './icons-colors.component.html',
  styleUrls: ['./icons-colors.component.scss'],
})
export class IconsColorsComponent {
  // Color examples with different color options
  colorExamples = [
    { color: '#374151', label: 'Default Gray' },
    { color: '#3B82F6', label: 'Blue' },
    { color: '#10B981', label: 'Green' },
    { color: '#F59E0B', label: 'Orange' },
    { color: '#EF4444', label: 'Red' },
  ];

  iconName = 'star'; // Using same icon to show color differences

  onIconClick(event: Event): void {
    console.log('Icon clicked:', event);
  }
}
