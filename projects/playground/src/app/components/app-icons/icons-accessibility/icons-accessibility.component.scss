.demo-container {
  max-width: 870px;
  margin: 0 auto;
  padding: 2rem;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: flex-start;
  gap: 2rem;

  @media (max-width: 768px) {
    padding: 1rem;
    flex-wrap: wrap;
  }
}

// Screen reader only utility class
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
} 