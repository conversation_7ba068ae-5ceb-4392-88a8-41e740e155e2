import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-icons-basic-usage',
  standalone: true,
  imports: [CommonModule, IconComponent],
  templateUrl: './icons-basic-usage.component.html',
  styleUrls: ['./icons-basic-usage.component.scss'],
})
export class IconsBasicUsageComponent {
  // Basic icon examples
  basicIcons = [
    { name: 'home', label: 'Home' },
    { name: 'user', label: 'User' },
    { name: 'settings', label: 'Settings' },
    { name: 'heart', label: 'Heart' },
    { name: 'star', label: 'Star' },
  ];

  onIconClick(event: Event): void {
    console.log('Icon clicked:', event);
  }
}
