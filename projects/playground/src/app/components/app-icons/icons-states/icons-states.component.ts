import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-icons-states',
  standalone: true,
  imports: [CommonModule, IconComponent],
  templateUrl: './icons-states.component.html',
  styleUrls: ['./icons-states.component.scss'],
})
export class IconsStatesComponent {
  // State examples with different configurations
  stateExamples = [
    { name: 'settings', label: 'Normal', disabled: false, cursor: false },
    { name: 'settings', label: 'Interactive', disabled: false, cursor: true },
    { name: 'settings', label: 'Disabled', disabled: true, cursor: false },
    {
      name: 'settings',
      label: 'Disabled Interactive',
      disabled: true,
      cursor: true,
    },
  ];

  onIconClick(event: Event, state: string): void {
    console.log(`${state} icon clicked:`, event);
  }
}
