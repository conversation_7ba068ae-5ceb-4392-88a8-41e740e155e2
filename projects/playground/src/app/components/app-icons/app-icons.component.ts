import { Component, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { IconComponent } from '../../../../../play-comp-library/src/lib/components/icon/icon.component';

interface IconDocSection {
  title: string;
  description: string;
  showCode: boolean;
}

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

@Component({
  selector: 'awe-app-icons',
  standalone: true,
  imports: [CommonModule, IconComponent, RouterModule],
  templateUrl: './app-icons.component.html',
  styleUrls: ['./app-icons.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class AppIconsComponent {
  // Documentation sections
  sections: IconDocSection[] = [
    {
      title: 'Basic Usage',
      description: 'Simple icons with default configuration.',
      showCode: false,
    },
    // {
    //   title: 'Icon Variants',
    //   description: 'Different icon variants including action, danger, success, warning, disable, and blue.',
    //   showCode: false,
    // },

    //   {
    //     title: 'Custom Colors',
    //     description: 'Icons with custom colors specified by hex values or color names.',
    //     showCode: false,
    //   }
    //  ,
    {
      title: 'Disabled State',
      description: 'Icons in disabled state with different variants.',
      showCode: false,
    },
    // {
    //   title: 'Available Icons',
    //   description: 'List of all icons available in the library. Click on the icon to copy.',
    //   showCode: false,
    // }
  ];

  // API Documentation
  apiProps: ApiProperty[] = [
    {
      name: 'iconName',
      type: 'string',
      default: '""',
      description: 'The name of the icon.',
    },
    {
      name: 'color',
      type: 'string',
      default: '""',
      description:
        'Custom color for the icon, can be a named color or a hex value.',
    },
    {
      name: 'iconColor',
      type: '"action" | "danger" | "disable" | "neutralIcon" | "success" | "warning" | "whiteIcon" | "blue"',
      default: '""',
      description: 'Predefined color tokens for the icon.',
    },
    {
      name: 'disabled',
      type: 'boolean',
      default: 'false',
      description: 'Whether the icon is disabled.',
    },
  ];

  toggleSection(index: number): void {
    this.sections[index].showCode = !this.sections[index].showCode;
  }
  toggleCodeVisibility(index: number, event: Event): void {
    event.stopPropagation(); // Prevent the click event from bubbling up to the section header
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  copyCode(section: string): void {
    const code = this.getExampleCode(section);
    navigator.clipboard
      .writeText(code)
      .then(() => {
        console.log('Code copied to clipboard');
      })
      .catch((err) => {
        console.error('Failed to copy code:', err);
      });
  }

  getExampleCode(section: string): string {
    const examples: Record<string, string> = {
      'basic usage': `// Basic icon example
import { Component } from '@angular/core';
import { IconsComponent } from '@awe/play-comp-library';
 
@Component({
  selector: 'app-basic-icons',
  standalone: true,
  imports: [IconsComponent],
  template: \`
<div class="icon-container">
<awe-icons iconName="awe_edit"></awe-icons>
<awe-icons iconName="awe_save" iconColor="success"></awe-icons>
<awe-icons iconName="awe_trash" iconColor="danger"></awe-icons>
</div>
  \`,
  styles: [\`
    .icon-container {
      padding: 1rem;
    }
  \`]
})
export class BasicIconsComponent {}`,

      'icon variants': `// Icon variants example
import { Component } from '@angular/core';
import { IconsComponent } from '@awe/play-comp-library';
 
@Component({
  selector: 'app-icon-variants',
  standalone: true,
  imports: [IconsComponent],
  template: \`
<div class="variants-container">
<awe-icons iconName="awe_edit" iconColor="action"></awe-icons>
<awe-icons iconName="awe_trash" iconColor="danger"></awe-icons>
<awe-icons iconName="awe_send" iconColor="success"></awe-icons>
<awe-icons iconName="awe_bell" iconColor="warning"></awe-icons>
<awe-icons iconName="awe_close" iconColor="disable"></awe-icons>
<awe-icons iconName="awe_upload" iconColor="blue"></awe-icons>
</div>
  \`,
  styles: [\`
    .variants-container {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }
  \`]
})
export class IconVariantsComponent {}`,

      'custom colors': `// Custom colors example
import { Component } from '@angular/core';
import { IconsComponent } from '@awe/play-comp-library';

@Component({
selector: 'app-custom-colors',
standalone: true,
imports: [IconsComponent],
template: \`
<div class="custom-colors-container">
    <awe-icons iconName="awe_edit" color="#ff5733"></awe-icons>
    <awe-icons iconName="awe_add_modetor" color="#3498db"></awe-icons>
    <awe-icons iconName="awe_trash" color="red"></awe-icons>
    <awe-icons iconName="awe_bell" color="#2ecc71"></awe-icons>
    <awe-icons iconName="awe_database" color="gray"></awe-icons>
    <awe-icons iconName="awe_home" color="#f39c12"></awe-icons>
</div>
\`,
styles: [\`
.custom-colors-container {
display: flex;
flex-direction: column;
gap: 2rem;
}
\`]
})
export class CustomColorsComponent {}`,

      'disabled state': `// Disabled state example
import { Component } from '@angular/core';
import { IconsComponent } from '@awe/play-comp-library';
 
@Component({
  selector: 'app-disabled-icons',
  standalone: true,
  imports: [IconsComponent],
  template: \`
<div class="disabled-container">
<awe-icons iconName="awe_edit" [disabled]="true"></awe-icons>
<awe-icons iconName="awe_save" [disabled]="true" iconColor="success"></awe-icons>
<awe-icons iconName="awe_trash" [disabled]="true" iconColor="danger"></awe-icons>
</div>
  \`,
  styles: [\`
    .disabled-container {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }
  \`]
})
export class DisabledIconsComponent {}`,
    };
    return examples[section.toLowerCase()] || '';
  }
  copiedIcon: string | null = null;

  copyIconName(icon: string): void {
    navigator.clipboard
      .writeText(icon)
      .then(() => {
        this.copiedIcon = icon;

        // Remove the copied message after 2 seconds
        setTimeout(() => {
          this.copiedIcon = null;
        }, 2000);
      })
      .catch((err) => {
        console.error('Copy failed', err);
      });
  }

  userClick(event: Event) {
    console.log(event);
  }
}
