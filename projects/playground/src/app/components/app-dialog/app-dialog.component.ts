import { Component } from '@angular/core';
import { DialogService, DialogButton, SuccessDialogConfig } from '../../../../../play-comp-library/src/lib/components/dialog/dialog-service';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';
import { CommonModule } from '@angular/common';

interface DialogExample {
  title: string;
  description: string;
  buttonLabel: string;
  buttonVariant: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  action: () => void;
}

@Component({
  selector: 'app-app-dialog',
  imports: [ButtonComponent, CommonModule],
  providers: [DialogService],
  templateUrl: './app-dialog.component.html',
  styleUrl: './app-dialog.component.scss'
})
export class AppDialogComponent {
  constructor(private dialogService: DialogService) { }

  dialogExamples: DialogExample[] = [
    {
      title: 'Success Dialog',
      description: 'Shows a success message with a green checkmark icon',
      buttonLabel: 'Show Success',
      buttonVariant: 'success',
      action: () => this.showSuccess()
    },
    {
      title: 'Error Dialog',
      description: 'Displays error messages with a red alert icon',
      buttonLabel: 'Show Error',
      buttonVariant: 'danger',
      action: () => this.showError()
    },
    {
      title: 'Warning Dialog',
      description: 'Shows warning messages with a yellow triangle icon',
      buttonLabel: 'Show Warning',
      buttonVariant: 'warning',
      action: () => this.showWarning()
    },
    {
      title: 'Info Dialog',
      description: 'Displays informational messages with a blue info icon',
      buttonLabel: 'Show Info',
      buttonVariant: 'primary',
      action: () => this.showInfo()
    },
    {
      title: 'Confirmation Dialog',
      description: 'Asks for user confirmation with action buttons',
      buttonLabel: 'Show Confirmation',
      buttonVariant: 'secondary',
      action: () => this.showConfirmation()
    },
    {
      title: 'Loading Dialog',
      description: 'Shows loading state with spinner and progress',
      buttonLabel: 'Show Loading',
      buttonVariant: 'secondary',
      action: () => this.showLoading()
    },
    {
      title: 'Success with Buttons',
      description: 'Shows a success dialog with custom action buttons',
      buttonLabel: 'Show Success with Buttons',
      buttonVariant: 'success',
      action: () => this.showSuccessWithButtons()
    },
    // {
    //   title: 'Success with Multiple Buttons',
    //   description: 'Shows a success dialog with multiple custom buttons',
    //   buttonLabel: 'Show Multiple Buttons',
    //   buttonVariant: 'success',
    //   action: () => this.showSuccessWithMultipleButtons()
    // },
    {
      title: 'Custom Dialog',
      description: 'Flexible dialog with custom content and buttons',
      buttonLabel: 'Show Custom',
      buttonVariant: 'primary',
      action: () => this.showCustom()
    },
    {
      title: 'Feedback Dialog',
      description: 'Flexible dialog with Feedback content',
      buttonLabel: 'Send Back',
      buttonVariant: 'primary',
      action: () => this.feedback()
    }
  ];

  showSuccess() {
    this.dialogService.success({
      title: 'Message Sent Successfully!',
      message: 'Thank you for contacting us. We will get back to you soon.'
    }).then(result => {
      console.log('Success dialog closed:', result);
    });
  }

  showError() {
    this.dialogService.error({
      title: 'Connection Failed',
      message: 'Unable to connect to the server. Please check your internet connection and try again.',
      showRetryButton: true,
      retryButtonText: 'Retry'
    }).then(result => {
      console.log('Error dialog closed:', result);

      // Handle different button actions
      if (result.action === 'retry') {
        alert('User clicked Retry! You can implement retry logic here.');
        // Example: this.retryConnection();
      } else if (result.action === 'close') {
        alert('User clicked Close! You can implement error handling here.');
        // Example: this.handleConnectionError();
      }
    });
  }

  showWarning() {
    this.dialogService.warning({
      title: 'Unsaved Changes',
      message: 'You have unsaved changes that will be lost if you continue. Are you sure you want to proceed?',
      showProceedButton: true,
      proceedButtonText: 'Discard Changes'
    }).then(result => {
      console.log('Warning dialog closed:', result);

      // Handle different button actions
      if (result.action === 'proceed') {
        alert('User chose to discard changes! You can implement navigation here.');
        // Example: this.discardChangesAndNavigate();
      } else if (result.action === 'cancel') {
        alert('User chose to keep changes! You can stay on current page.');
        // Example: this.stayOnCurrentPage();
      }
    });
  }

  showInfo() {
    this.dialogService.info({
      title: 'New Feature Available',
      message: 'We have added new features to improve your experience. Would you like to learn more?',
      showLearnMoreButton: true,
      learnMoreButtonText: 'Learn More'
    }).then(result => {
      console.log('Info dialog closed:', result);
    });
  }

  showSuccessWithButtons() {
    // Example using SuccessDialogConfig interface for type safety
    const config: SuccessDialogConfig = {
      title: 'Account Created Successfully!',
      message: 'Your account has been created and verified. You can now start using all features.',
      buttons: [
        { label: 'OK', variant: 'secondary', action: 'ok' },
        { label: 'Get Started', variant: 'success', action: 'continue' }
      ],
      showButtons: true
    };

    this.dialogService.success(config).then(result => {
      console.log('Success with buttons dialog closed:', result);

      // Handle different button actions
      if (result.action === 'continue') {
        alert('User clicked Get Started! You can implement onboarding flow here.');
        // Example: this.startOnboarding();
      } else if (result.action === 'ok') {
        alert('User clicked OK! You can implement default success handling here.');
        // Example: this.handleSuccessConfirmation();
      } else if (result.action === 'close') {
        alert('User clicked Close! Dialog was closed.');
        // Example: this.handleDialogClose();
      }
    });
  }

  // showSuccessWithMultipleButtons() {
  //   this.dialogService.success({
  //     title: 'Payment Successful!',
  //     message: 'Your payment has been processed successfully. What would you like to do next?',
  //     buttons: [
  //       { label: 'View Receipt', variant: 'secondary', action: 'receipt' },
  //       // { label: 'Continue Shopping', variant: 'primary', action: 'shop' },
  //       { label: 'Go to Dashboard', variant: 'success', action: 'dashboard' }
  //     ],
  //     showButtons: true
  //   }).then(result => {
  //     console.log('Success with multiple buttons dialog closed:', result);

  //     // Handle different button actions
  //     switch (result.action) {
  //       case 'receipt':
  //         alert('User clicked View Receipt! You can show receipt here.');
  //         // Example: this.showReceipt();
  //         break;
  //       case 'shop':
  //         alert('User clicked Continue Shopping! You can navigate to shop here.');
  //         // Example: this.navigateToShop();
  //         break;
  //       case 'dashboard':
  //         alert('User clicked Go to Dashboard! You can navigate to dashboard here.');
  //         // Example: this.navigateToDashboard();
  //         break;
  //       case 'close':
  //         alert('User clicked Close! Dialog was closed.');
  //         // Example: this.handleDialogClose();
  //         break;
  //       default:
  //         console.log('Unknown action:', result.action);
  //     }
  //   });
  // }

  showConfirmation() {
    this.dialogService.confirmation({
      title: 'Delete Account',
      message: 'This action cannot be undone. All your data will be permanently deleted.',
      confirmButtonText: 'Delete Account',
      cancelButtonText: 'Keep Account',
      destructive: true
    }).then(result => {
      console.log('Confirmation dialog closed:', result);

      // Handle confirmation result
      if (result.confirmed === true) {
        alert('User confirmed deletion! You can implement delete logic here.');
        // Example: this.deleteUserAccount();
      } else if (result.confirmed === false) {
        alert('User cancelled deletion! Account is safe.');
        // Example: this.cancelDeletion();
      }
    });
  }

  showLoading() {
    const loadingDialog = this.dialogService.loading({
      title: 'Processing...',
      message: 'Please wait while we process your request.',
      showProgress: true,
      showCancelButton: true
    });

    // Simulate progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      if (progress >= 100) {
        clearInterval(interval);
        this.dialogService.close();
      }
    }, 500);

    loadingDialog.then(result => {
      clearInterval(interval);
      console.log('Loading dialog closed:', result);
    });
  }

  showCustom() {
    const customButtons: DialogButton[] = [
      { label: 'Cancel', variant: 'secondary', action: 'cancel' },
      { label: 'Save Draft', variant: 'secondary', action: 'draft' },
      { label: 'Publish', variant: 'primary', action: 'publish' }
    ];

    this.dialogService.custom({
      title: 'Publish Article',
      message: 'Your article is ready to be published. You can also save it as a draft for later.',
      variant: 'info',
      buttons: customButtons
    }).then(result => {
      console.log('Custom dialog closed:', result);

      // Handle custom button actions
      switch (result.action) {
        case 'publish':
          alert('User clicked Publish! You can implement publish logic here.');
          // Example: this.publishArticle();
          break;
        case 'draft':
          alert('User clicked Save Draft! You can implement draft saving here.');
          // Example: this.saveAsDraft();
          break;
        case 'cancel':
          alert('User clicked Cancel! You can return to editor.');
          // Example: this.returnToEditor();
          break;
        default:
          console.log('Unknown action:', result.action);
      }
    });
  }

  feedback() {
    const customButtons: DialogButton[] = [
      { label: 'Cancel', variant: 'secondary', action: 'cancel' },
      { label: 'Send Back', variant: 'primary', action: 'publish' }
    ];

    this.dialogService.feedback({
      title: 'We Value Your Feedback',
      message: 'Please share your feedback below. Your input helps us make meaningful improvements.',
      variant: 'info',
      buttons: customButtons
    }).then((result: any) => {
      console.log('Feedback dialog closed:', result);

      // Handle custom button actions
      switch (result.action) {
        case 'publish':
          alert('User clicked Publish! You can implement publish logic here.');
          // Example: this.publishArticle();
          break;
        case 'draft':
          alert('User clicked Save Draft! You can implement draft saving here.');
          // Example: this.saveAsDraft();
          break;
          break;
        case 'feedback':
          alert(`User clicked feedback! . feedback is ${result.feedback} `);
          // Example: this.returnToEditor();
          break;
        default:
          console.log('Unknown action:', result.action);
      }
    });
  }



  // Example business logic methods that users would implement
  private retryConnection() {
    console.log('🔄 Retrying connection...');
    // Implement actual retry logic here
  }

  private handleConnectionError() {
    console.log('❌ Handling connection error...');
    // Implement error handling logic here
  }

  private discardChangesAndNavigate() {
    console.log('🗑️ Discarding changes and navigating...');
    // Implement navigation logic here
  }

  private stayOnCurrentPage() {
    console.log('📄 Staying on current page...');
    // Keep user on current page
  }

  private deleteUserAccount() {
    console.log('🗑️ Deleting user account...');
    // Implement account deletion logic here
  }

  private cancelDeletion() {
    console.log('✅ Account deletion cancelled...');
    // Handle cancellation logic here
  }

  private publishArticle() {
    console.log('📝 Publishing article...');
    // Implement article publishing logic here
  }

  private saveAsDraft() {
    console.log('💾 Saving as draft...');
    // Implement draft saving logic here
  }

  private returnToEditor() {
    console.log('✏️ Returning to editor...');
    // Return to article editor
  }

  // Additional Dialog Examples with Borders
  showSuccessWithBorder() {
    this.dialogService.success({
      title: 'Account Created Successfully!',
      message: 'Your account has been created and verified. You can now start using all features.',
      bottomBorder: true,
      buttons: [
        { label: 'OK', variant: 'secondary', action: 'ok' },
        { label: 'Get Started', variant: 'success', action: 'continue' }
      ],
      showButtons: true
    }).then(result => {
      console.log('Success dialog with border closed:', result);
    });
  }

  showSuccessWithoutBorder() {
    this.dialogService.success({
      title: 'Task Completed',
      message: 'Your task has been completed successfully.',
      bottomBorder: false,
      buttons: [
        { label: 'Close', variant: 'secondary', action: 'close' }
      ],
      showButtons: true
    }).then(result => {
      console.log('Success dialog without border closed:', result);
    });
  }

  showErrorWithBorder() {
    this.dialogService.error({
      title: 'Connection Failed',
      message: 'Unable to connect to the server. Please check your internet connection and try again.',
      bottomBorder: true,
      buttons: [
        { label: 'Cancel', variant: 'secondary', action: 'cancel' },
        { label: 'Retry', variant: 'danger', action: 'retry' }
      ],
      showButtons: true
    }).then(result => {
      console.log('Error dialog with border closed:', result);
    });
  }

  showErrorWithoutBorder() {
    this.dialogService.error({
      title: 'Validation Error',
      message: 'Please fill in all required fields before proceeding.',
      bottomBorder: false,
      buttons: [
        { label: 'OK', variant: 'secondary', action: 'ok' }
      ],
      showButtons: true
    }).then(result => {
      console.log('Error dialog without border closed:', result);
    });
  }

  showWarningWithBorder() {
    this.dialogService.warning({
      title: 'Unsaved Changes',
      message: 'You have unsaved changes. Are you sure you want to leave this page?',
      bottomBorder: true,
      buttons: [
        { label: 'Stay', variant: 'secondary', action: 'stay' },
        { label: 'Leave', variant: 'warning', action: 'leave' }
      ],
      showButtons: true
    }).then(result => {
      console.log('Warning dialog with border closed:', result);
    });
  }

  showWarningWithoutBorder() {
    this.dialogService.warning({
      title: 'Storage Almost Full',
      message: 'Your storage is 90% full. Consider deleting some files.',
      bottomBorder: false,
      buttons: [
        { label: 'Dismiss', variant: 'secondary', action: 'dismiss' }
      ],
      showButtons: true
    }).then(result => {
      console.log('Warning dialog without border closed:', result);
    });
  }

  showInfoWithBorder() {
    this.dialogService.info({
      title: 'New Feature Available',
      message: 'We have added new features to improve your experience. Would you like to learn more?',
      bottomBorder: true,
      buttons: [
        { label: 'Maybe Later', variant: 'secondary', action: 'later' },
        { label: 'Learn More', variant: 'info', action: 'learn' }
      ],
      showButtons: true
    }).then(result => {
      console.log('Info dialog with border closed:', result);
    });
  }

  showInfoWithoutBorder() {
    this.dialogService.info({
      title: 'System Maintenance',
      message: 'Scheduled maintenance will occur tonight from 2-4 AM EST.',
      bottomBorder: false,
      buttons: [
        { label: 'Got it', variant: 'secondary', action: 'ok' }
      ],
      showButtons: true
    }).then(result => {
      console.log('Info dialog without border closed:', result);
    });
  }

  showCustomWithBorder() {
    this.dialogService.custom({
      title: 'Publish Article',
      message: 'Your article is ready to be published. You can also save it as a draft for later.',
      variant: 'success',
      bottomBorder: true,
      buttons: [
        { label: 'Cancel', variant: 'secondary', action: 'cancel' },
        { label: 'Save Draft', variant: 'secondary', action: 'draft' },
        { label: 'Publish', variant: 'primary', action: 'publish' }
      ]
    }).then(result => {
      console.log('Custom dialog with border closed:', result);
    });
  }

  showCustomWithoutBorder() {
    this.dialogService.custom({
      title: 'Quick Survey',
      message: 'Help us improve by answering a quick question.',
      variant: 'info',
      bottomBorder: false,
      buttons: [
        { label: 'Skip', variant: 'secondary', action: 'skip' },
        { label: 'Take Survey', variant: 'primary', action: 'survey' }
      ]
    }).then(result => {
      console.log('Custom dialog without border closed:', result);
    });
  }

  showFeedbackWithBorder() {
    this.dialogService.feedback({
      title: 'We Value Your Feedback',
      message: 'Please share your thoughts about our new feature.',
      label: 'Your feedback',
      confirmButtonText: 'Send Feedback',
      cancelButtonText: 'Cancel',
      bottomBorder: true
    }).then(result => {
      console.log('Feedback dialog with border closed:', result);
    });
  }

  showFeedbackWithoutBorder() {
    this.dialogService.feedback({
      title: 'Quick Feedback',
      message: 'How was your experience?',
      label: 'Comments',
      confirmButtonText: 'Submit',
      cancelButtonText: 'Skip',
      bottomBorder: false
    }).then(result => {
      console.log('Feedback dialog without border closed:', result);
    });
  }
}
