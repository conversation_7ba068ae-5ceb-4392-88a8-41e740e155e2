.with-header-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;

  .demo-section {
    margin-bottom: 3rem;
    max-width: 800px;
    display: flex;
    justify-content: center;

    .demo-grid {
      display: flex;
      justify-content: center;
      width: 100%;
      max-width: 500px;

      .demo-item {
        h3 {
          font-size: 1.25rem;
          font-weight: 600;
          color: var(--text-primary);
          margin-bottom: 1rem;
          padding-bottom: 0.5rem;
          border-bottom: 2px solid var(--primary-color);
        }

        ava-card {
          display: block;
          transition: transform 0.3s ease, box-shadow 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
          }
        }

        // Header with Icon
        .header-with-icon {
          display: flex;
          align-items: center;
          gap: 0.75rem;

          h4 {
            margin: 0;
            color: var(--text-primary);
          }
        }

        // Header with Actions
        .header-with-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .header-content {
            display: flex;
            align-items: center;
            gap: 0.75rem;

            h4 {
              margin: 0;
              color: var(--text-primary);
            }
          }

          .header-actions {
            display: flex;
            gap: 0.5rem;
          }
        }

        // Header with Status
        .header-with-status {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .header-content {
            h4 {
              margin: 0;
              color: var(--text-primary);
            }
          }

          .status-badge {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.25rem 0.75rem;
            background: rgba(40, 167, 69, 0.1);
            border: 1px solid rgba(40, 167, 69, 0.2);
            border-radius: 20px;
            font-size: 0.875rem;
            color: #28a745;
            font-weight: 500;
          }
        }

        // Complex Header
        .complex-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;

          .header-main {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            flex: 1;

            .header-icon {
              flex-shrink: 0;
            }

            .header-info {
              flex: 1;

              h4 {
                margin: 0 0 0.25rem 0;
                color: var(--text-primary);
              }

              .header-subtitle {
                margin: 0;
                font-size: 0.875rem;
                color: var(--text-secondary);
              }
            }
          }

          .header-actions {
            display: flex;
            gap: 0.5rem;
            flex-shrink: 0;
          }
        }

        // Navigation Header
        .navigation-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .nav-breadcrumb {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: var(--text-secondary);

            span {
              color: var(--text-primary);
              font-weight: 500;

              &:last-child {
                color: var(--primary-color);
              }
            }
          }

          .nav-actions {
            display: flex;
            gap: 0.5rem;
          }
        }
      }
    }
  }

  .features-section {
    h3 {
      font-size: 1.75rem;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 2rem;
      text-align: center;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;

      .feature-item {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        padding: 1.5rem;
        background: var(--surface-secondary);
        border-radius: 12px;
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
          border-color: var(--primary-color);
        }

        .feature-content {
          flex: 1;

          strong {
            display: block;
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
          }

          color: var(--text-secondary);
          line-height: 1.5;
        }
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 1rem;

    .demo-header {
      h2 {
        font-size: 2rem;
      }

      p {
        font-size: 1rem;
      }
    }

    .demo-section {
      .demo-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }
    }

    .features-section {
      .features-grid {
        grid-template-columns: 1fr;
        gap: 1rem;

        .feature-item {
          padding: 1rem;
        }
      }
    }
  }

  @media (max-width: 480px) {
    .demo-header {
      h2 {
        font-size: 1.75rem;
      }
    }

    .demo-section {
      .demo-grid {
        .demo-item {
          .complex-header {
            flex-direction: column;
            gap: 1rem;

            .header-actions {
              align-self: flex-end;
            }
          }

          .header-with-actions {
            flex-direction: column;
            gap: 1rem;
            align-items: flex-start;

            .header-actions {
              align-self: flex-end;
            }
          }
        }
      }
    }

    .features-section {
      .features-grid {
        .feature-item {
          flex-direction: column;
          text-align: center;
          gap: 0.75rem;
        }
      }
    }
  }
}
