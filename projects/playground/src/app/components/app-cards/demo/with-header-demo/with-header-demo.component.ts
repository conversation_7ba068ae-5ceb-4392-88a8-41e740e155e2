import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';
import { IconComponent } from '../../../../../../../play-comp-library/src/lib/components/icon/icon.component';
import { DefaultCardComponent } from '../../../../../../../play-comp-library/src/lib/components/card/default-card/default-card.component';
import { CardHeaderComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card-header/card-header.component';
import { CardContentComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card-content/card-content.component';
import { AvaTagComponent } from '../../../../../../../play-comp-library/src/lib/components/tags/tags.component';
import { BadgesComponent } from '../../../../../../../play-comp-library/src/lib/components/badges/badges.component';

@Component({
  selector: 'ava-card-with-header-demo',
  standalone: true,
  imports: [
    CommonModule,
    IconComponent,
    DefaultCardComponent,
    CardHeaderComponent,
    CardContentComponent,


  ],
  templateUrl: './with-header-demo.component.html',
  styleUrl: './with-header-demo.component.scss',
})
export class WithHeaderDemoComponent {
  onActionClick(action: string) {
    console.log(`${action} action clicked!`);
  }

  onNotificationClick() {
    console.log('Notification clicked!');
  }

  onShareClick() {
    console.log('Share clicked!');
  }
}
