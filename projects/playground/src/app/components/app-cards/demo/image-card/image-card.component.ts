import { Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { AvaTagComponent } from '../../../../../../../play-comp-library/src/lib/components/tags/tags.component';
import { IconComponent } from '../../../../../../../play-comp-library/src/lib/components/icon/icon.component';
import { ImageCardComponent } from '../../../../../../../play-comp-library/src/lib/composite-components/image-card/image-card.component';
import { CardHeaderComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card-header/card-header.component';
import { CardContentComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card-content/card-content.component';
import { CardFooterComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card-footer/card-footer.component';


@Component({
  selector: 'app-image-card',
  standalone: true,
  imports: [RouterModule, ImageCardComponent, AvaTagComponent, IconComponent,
    CardHeaderComponent, CardContentComponent, CardFooterComponent],
  templateUrl: './image-card.component.html',
  styleUrl: './image-card.component.scss',
})
export class AppImageCardComponent { }
