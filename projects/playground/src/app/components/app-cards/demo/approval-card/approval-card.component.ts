import { Component, ViewEncapsulation } from '@angular/core';
import { ApprovalCardComponent, AvaTagComponent, ButtonComponent } from '../../../../../../../play-comp-library/src/public-api';
import { Card<PERSON>ontentComponent, CardFooterComponent, CardHeaderComponent, IconComponent } from '../../../../../../../play-comp-library/src/public-api';
import { DefaultCardComponent } from '../../../../../../../play-comp-library/src/lib/components/card/default-card/default-card.component';

@Component({
  selector: 'app-approval-card',
  imports: [ApprovalCardComponent, ButtonComponent, AvaTagComponent,
    DefaultCardComponent, CardContentComponent, CardFooterComponent, CardHeaderComponent, IconComponent
  ],
  templateUrl: './approval-card.component.html',
  styleUrl: './approval-card.component.scss',
  encapsulation: ViewEncapsulation.None
})
export class AppApprovalCardComponent {
  uClick(even: any) {

  }

}
