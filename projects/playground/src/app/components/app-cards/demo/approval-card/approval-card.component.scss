

// Override default card styles for approval card to match the exact image design
ava-approval-card {
    display: block;
    max-width: 872px;
    margin: 0 auto;


    // Card content styling - exact match to image
    ava-card-content {
        padding: 32px;

        .user-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;

            .user-info {
                display: flex;
                align-items: center;
                gap: 12px;

                .user-avatar {
                    img {
                        width: 40px;
                        height: 40px;
                        border-radius: 50%;
                        object-fit: cover;
                    }
                }

    

                ava-tag .ava-tag__avatar {
               img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                  border-radius: 50%;
                  }
               }

                
            }
        }
    }

    // Card footer styling - exact match to image buttons
    ava-card-footer {
        padding: 0 32px 32px 32px;
        border-top: none;
        background: transparent;

        .action-buttons {
            display: flex;
            gap: 12px;

            ava-button {
                flex: 1;
                min-height: 44px;
            }
        }
    }
}
