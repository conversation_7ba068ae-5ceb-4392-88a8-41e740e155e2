import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';
import { IconComponent } from '../../../../../../../play-comp-library/src/lib/components/icon/icon.component';
import { DefaultCardComponent } from '../../../../../../../play-comp-library/src/lib/components/card/default-card/default-card.component';
import { CardHeaderComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card-header/card-header.component';
import { CardContentComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card-content/card-content.component';
import { CardFooterComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card-footer/card-footer.component';
import { AvaTagComponent } from '../../../../../../../play-comp-library/src/lib/components/tags/tags.component';
import { BadgesComponent } from '../../../../../../../play-comp-library/src/lib/components/badges/badges.component';

@Component({
  selector: 'ava-card-basic-usage-demo',
  standalone: true,
  imports: [
    CommonModule,
    IconComponent,
    DefaultCardComponent,
    CardHeaderComponent,
    CardContentComponent,
    CardFooterComponent,
    AvaTagComponent,

  ],
  templateUrl: './basic-usage-demo.component.html',
  styleUrl: './basic-usage-demo.component.scss',
})
export class BasicUsageDemoComponent {
  cardTitle = 'Basic Card Title';
  cardContent =
    'This is a simple card with basic content. It demonstrates the fundamental layout and styling of the card component.';

  onCardClick() {
    console.log('Card clicked!');
  }

  onActionClick(action: string) {
    console.log(`${action} clicked!`);
  }
}
