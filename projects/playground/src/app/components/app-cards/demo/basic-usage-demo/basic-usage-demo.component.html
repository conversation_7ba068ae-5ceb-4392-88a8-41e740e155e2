<div class="basic-usage-demo">
  <div class="demo-section">
    <div class="demo-grid">
      <!-- <div class="demo-item">
        <h3>Simple Card</h3>
        <ava-default-card>
          <ava-card-content>
            <h4>{{ cardTitle }}</h4>
            <p>{{ cardContent }}</p>
          </ava-card-content>
        </ava-default-card>
      </div>

      <div class="demo-item">
        <h3>Card with Header</h3>
        <ava-default-card>
          <ava-card-header>
            <div class="header-with-icon">
              <ava-icon
                iconName="star"
                [iconSize]="20"
                iconColor="#ffd700"
              ></ava-icon>
              <h4>Featured Content</h4>
            </div>
          </ava-card-header>
          <ava-card-content>
            <p>
              This card includes a header section with an icon to highlight
              important information and provide clear visual hierarchy.
            </p>
          </ava-card-content>
        </ava-default-card>
      </div> -->

      <!-- Card with Header, Content and Footer -->
      <div class="demo-item">
        <ava-default-card>
          <ava-card-header>
            <div class="header-with-badge">
              <h4>Article Title</h4>
              <ava-tag [pill]="true" label="New" color="primary"></ava-tag>
            </div>
          </ava-card-header>
          <ava-card-content>
            <p>
              A complete card example with header, content, and footer sections.
              Perfect for articles, blog posts, or detailed information
              displays.
            </p>
          </ava-card-content>
          <ava-card-footer>
            <div class="footer-info">
              <div class="meta-info">
                <ava-icon
                  iconName="calendar-days"
                  [iconSize]="16"
                  iconColor="#6c757d"
                ></ava-icon>
                <span>Dec 15, 2024</span>
              </div>
              <div class="meta-info">
                <ava-icon
                  iconName="user"
                  [iconSize]="16"
                  iconColor="#6c757d"
                ></ava-icon>
                <span>John Doe</span>
              </div>
            </div>
          </ava-card-footer>
        </ava-default-card>
      </div>

      <!-- <div class="demo-item">
        <h3>Interactive Card</h3>
        <ava-default-card class="interactive-card">
          <ava-card-header>
            <div class="product-header">
              <h4>Premium Product</h4>
              <ava-tag label="50% OFF" color="success"></ava-tag>
            </div>
          </ava-card-header>
          <ava-card-content>
            <div class="product-content">
              <div class="product-icon">
                <ava-icon
                  iconName="package"
                  [iconSize]="48"
                  iconColor="#007bff"
                ></ava-icon>
              </div>
              <p>
                This interactive card demonstrates action buttons and user
                engagement features.
              </p>
              <div class="price-section">
                <span class="original-price">$99.99</span>
                <span class="sale-price">$49.99</span>
              </div>
            </div>
          </ava-card-content>
          <ava-card-footer>
            <div class="action-buttons">
              <ava-button
                label="Add to Cart"
                variant="primary"
                size="small"
                (click)="onActionClick('Add to Cart')"
              >
              </ava-button>
              <ava-button
                label="Wishlist"
                variant="secondary"
                size="small"
                (click)="onActionClick('Wishlist')"
              >
              </ava-button>
            </div>
          </ava-card-footer>
        </ava-default-card>
      </div>

      <div class="demo-item">
        <h3>Profile Card</h3>
        <ava-default-card>
          <ava-card-header>
            <div class="profile-header">
              <div class="avatar-placeholder">
                <ava-icon
                  iconName="user"
                  [iconSize]="32"
                  iconColor="#ffffff"
                ></ava-icon>
              </div>
              <div class="profile-info">
                <h4>Sarah Johnson</h4>
                <p>Senior Developer</p>
              </div>
            </div>
          </ava-card-header>
          <ava-card-content>
            <div class="profile-stats">
              <div class="stat-item">
                <span class="stat-number">127</span>
                <span class="stat-label">Projects</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">1.2k</span>
                <span class="stat-label">Followers</span>
              </div>
              <div class="stat-item">
                <span class="stat-number">89</span>
                <span class="stat-label">Following</span>
              </div>
            </div>
          </ava-card-content>
          <ava-card-footer>
            <div class="profile-actions">
              <ava-button
                label="Follow"
                variant="primary"
                size="small"
                (click)="onActionClick('Follow')"
              >
              </ava-button>
              <ava-button
                label="Message"
                variant="secondary"
                size="small"
                (click)="onActionClick('Message')"
              >
              </ava-button>
            </div>
          </ava-card-footer>
        </ava-default-card>
      </div>

      <div class="demo-item">
        <h3>Status Card</h3>
        <ava-default-card>
          <ava-card-header>
            <div class="status-header">
              <ava-icon
                iconName="check-circle"
                [iconSize]="24"
                iconColor="#28a745"
              ></ava-icon>
              <h4>System Status</h4>
            </div>
          </ava-card-header>
          <ava-card-content>
            <div class="status-content">
              <p><strong>All systems operational</strong></p>
              <div class="status-list">
                <div class="status-item">
                  <ava-icon
                    iconName="circle"
                    [iconSize]="8"
                    iconColor="#28a745"
                  ></ava-icon>
                  <span>API Services</span>
                </div>
                <div class="status-item">
                  <ava-icon
                    iconName="circle"
                    [iconSize]="8"
                    iconColor="#28a745"
                  ></ava-icon>
                  <span>Database</span>
                </div>
                <div class="status-item">
                  <ava-icon
                    iconName="circle"
                    [iconSize]="8"
                    iconColor="#ffc107"
                  ></ava-icon>
                  <span>CDN (Degraded)</span>
                </div>
              </div>
            </div>
          </ava-card-content>
          <ava-card-footer>
            <div class="status-footer">
              <span class="last-updated">Last updated: 2 minutes ago</span>
              <ava-button
                label="View Details"
                variant="secondary"
                size="small"
                (click)="onActionClick('View Details')"
              >
              </ava-button>
            </div>
          </ava-card-footer>
        </ava-default-card>
      </div> -->
    </div>
  </div>
</div>
