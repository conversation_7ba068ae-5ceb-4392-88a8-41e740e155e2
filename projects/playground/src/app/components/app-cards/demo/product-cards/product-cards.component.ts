import { Component } from '@angular/core';
import { CardComponent } from '../../../../../../../play-comp-library/src/lib/components/card/product-card/product-card.component';
// import { IconComponent } from "play-comp-library";
 
@Component({
  selector: 'app-product-cards',
  standalone: true,
  imports: [ CardComponent],
  templateUrl: './product-cards.component.html',
  styleUrl: './product-cards.component.scss'
})
export class ProductCardsComponent {
  problemIcon = '../../../../../assets/problem.svg';
}
