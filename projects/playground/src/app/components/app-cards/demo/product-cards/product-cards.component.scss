.card-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  flex-shrink: 0; // Prevent the icon from shrinking

  .icon-img {
    width: 20px;
    height: 20px;
    object-fit: contain;
  }
}

.card-title {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  line-height: 1.3;

  @media (min-width: 768px) {
    font-size: 1.125rem; // Slightly larger title on tablets and up
  }
}

.line-item {
  padding: 0.5rem 0;
  border-bottom: 1px solid #e9ecef;
  color: #495057;
  font-size: 14px;
  line-height: 1.9;

  &:last-child {
    border-bottom: none;
  }
}


.card-body-content,
.empty-card-body,
.d-flex.align-items-center.justify-content-center.py-4 {
  flex-grow: 1; // THIS IS THE KEY: Makes the body take all available vertical space.
}