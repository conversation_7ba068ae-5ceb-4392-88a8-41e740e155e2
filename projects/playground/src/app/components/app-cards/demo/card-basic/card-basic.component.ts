import { Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CardComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card.component';
import { TxtCardComponent } from '../../../../../../../play-comp-library/src/lib/composite-components/txt-card/txt-card.component';
import { AvaTagComponent } from '../../../../../../../play-comp-library/src/lib/components/tags/tags.component';
import { IconComponent } from '../../../../../../../play-comp-library/src/lib/components/icon/icon.component';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';
import { ApprovalCardComponent } from '../../../../../../../play-comp-library/src/lib/composite-components/approval-card/approval-card.component';
import { DefaultCardComponent } from '../../../../../../../play-comp-library/src/lib/components/card/default-card/default-card.component';
import { CardHeaderComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card-header/card-header.component';
import { CardContentComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card-content/card-content.component';
import { CardFooterComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card-footer/card-footer.component';

@Component({
  selector: 'app-card-basic',
  standalone: true,
  imports: [
    RouterModule,
    TxtCardComponent,
    AvaTagComponent,
    IconComponent,
    CardHeaderComponent, CardContentComponent, CardFooterComponent

  ],
  templateUrl: './card-basic.component.html',
  styleUrl: './card-basic.component.scss',
})
export class AppCardBasicComponent {
  uClick(e: any) {
    console.log('Card clicked:', e);
  }
}
