.with-actions-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;

  p {
    font-size: 1.1rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
  }

  .demo-section {
    margin-bottom: 3rem;
    max-width: 800px;
    display: flex;
    justify-content: center;

    .demo-grid {
      display: flex;
      justify-content: center;
      width: 100%;
      max-width: 500px;

      .demo-item {
        h3 {
          font-size: 1.25rem;
          font-weight: 600;
          color: var(--text-primary);
          margin-bottom: 1rem;
          padding-bottom: 0.5rem;
          border-bottom: 2px solid var(--primary-color);
        }

        ava-card {
          display: block;
          transition: transform 0.3s ease, box-shadow 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
          }
        }

        .action-container {
          display: flex;
          justify-content: flex-end;
          gap: 0.75rem;
          flex-wrap: wrap;

          // For icon-only buttons, center them
          &:has(ava-button:only-child) {
            justify-content: center;
          }

          // For multiple actions, align properly
          &:has(ava-button:nth-child(2)) {
            justify-content: space-between;
          }
        }
      }
    }
  }

  .features-section {
    h3 {
      font-size: 1.75rem;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 2rem;
      text-align: center;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;

      .feature-item {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        padding: 1.5rem;
        background: var(--surface-secondary);
        border-radius: 12px;
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
          border-color: var(--primary-color);
        }

        .feature-content {
          flex: 1;

          strong {
            display: block;
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
          }

          color: var(--text-secondary);
          line-height: 1.5;
        }
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 1rem;

    .demo-header {
      h2 {
        font-size: 2rem;
      }

      p {
        font-size: 1rem;
      }
    }

    .demo-section {
      .demo-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }
    }

    .features-section {
      .features-grid {
        grid-template-columns: 1fr;
        gap: 1rem;

        .feature-item {
          padding: 1rem;
        }
      }
    }
  }

  @media (max-width: 480px) {
    .demo-header {
      h2 {
        font-size: 1.75rem;
      }
    }

    .demo-section {
      .demo-grid {
        .demo-item {
          .action-container {
            flex-direction: column;
            gap: 0.5rem;

            ava-button {
              width: 100%;
            }
          }
        }
      }
    }

    .features-section {
      .features-grid {
        .feature-item {
          flex-direction: column;
          text-align: center;
          gap: 0.75rem;
        }
      }
    }
  }
}
