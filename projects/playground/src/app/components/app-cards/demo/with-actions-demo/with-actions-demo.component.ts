import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';
import { IconComponent } from '../../../../../../../play-comp-library/src/lib/components/icon/icon.component';
import { DefaultCardComponent } from '../../../../../../../play-comp-library/src/lib/components/card/default-card/default-card.component';
import { CardHeaderComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card-header/card-header.component';
import { CardContentComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card-content/card-content.component';
import { CardFooterComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card-footer/card-footer.component';
import { AvaTagComponent } from '../../../../../../../play-comp-library/src/lib/components/tags/tags.component';

@Component({
  selector: 'ava-card-with-actions-demo',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    DefaultCardComponent,
    CardHeaderComponent,
    CardContentComponent,
    CardFooterComponent,
  ],
  templateUrl: './with-actions-demo.component.html',
  styleUrl: './with-actions-demo.component.scss',
})
export class WithActionsDemoComponent {
  onPrimaryAction(action: string) {
    console.log(`Primary action: ${action}`);
  }

  onSecondaryAction(action: string) {
    console.log(`Secondary action: ${action}`);
  }

  onActionClick(action: string) {
    console.log(`Action clicked: ${action}`);
  }
}
