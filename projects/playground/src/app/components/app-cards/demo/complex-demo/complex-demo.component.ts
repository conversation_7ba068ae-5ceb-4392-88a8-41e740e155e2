import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';
import { IconComponent } from '../../../../../../../play-comp-library/src/lib/components/icon/icon.component';
import { DefaultCardComponent } from '../../../../../../../play-comp-library/src/lib/components/card/default-card/default-card.component';
import { CardHeaderComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card-header/card-header.component';
import { CardContentComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card-content/card-content.component';
import { CardFooterComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card-footer/card-footer.component';
import { AvaTagComponent } from '../../../../../../../play-comp-library/src/lib/components/tags/tags.component';


@Component({
  selector: 'ava-card-complex-demo',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    IconComponent,
    DefaultCardComponent,
    CardHeaderComponent,
    CardContentComponent,
    CardFooterComponent,
    AvaTagComponent,
  ],
  templateUrl: './complex-demo.component.html',
  styleUrl: './complex-demo.component.scss',
})
export class ComplexDemoComponent {
  onAction(action: string) {
    console.log(`Action: ${action}`);
  }

  onProductAction(product: string, action: string) {
    console.log(`Product: ${product}, Action: ${action}`);
  }

  onDashboardAction(action: string) {
    console.log(`Dashboard action: ${action}`);
  }
}
