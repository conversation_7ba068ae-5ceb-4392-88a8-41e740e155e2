import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';
import { IconComponent } from '../../../../../../../play-comp-library/src/lib/components/icon/icon.component';
import { DefaultCardComponent } from '../../../../../../../play-comp-library/src/lib/components/card/default-card/default-card.component';
import { CardHeaderComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card-header/card-header.component';
import { CardContentComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card-content/card-content.component';
import { CardFooterComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card-footer/card-footer.component';
import { AvaTagComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-card-with-footer-demo',
  standalone: true,
  imports: [
    CommonModule,
    ButtonComponent,
    IconComponent,
    DefaultCardComponent,
    CardHeaderComponent,
    CardContentComponent,
    CardFooterComponent,
    AvaTagComponent
  ],
  templateUrl: './with-footer-demo.component.html',
  styleUrl: './with-footer-demo.component.scss',
})
export class WithFooterDemoComponent {
  onFooterAction(action: string) {
    console.log(`Footer action: ${action}`);
  }

  onLike() {
    console.log('Liked!');
  }

  onShare() {
    console.log('Shared!');
  }

  onComment() {
    console.log('Comment clicked!');
  }
}
