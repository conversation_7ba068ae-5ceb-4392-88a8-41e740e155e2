.with-footer-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;

  .demo-header {
    text-align: center;
    margin-bottom: 3rem;

    h2 {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: 1rem;
    }

    p {
      font-size: 1.1rem;
      color: var(--text-secondary);
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;
    }
  }

  .demo-section {
    margin-bottom: 3rem;

    .demo-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
      gap: 2rem;

      .demo-item {
        h3 {
          font-size: 1.25rem;
          font-weight: 600;
          color: var(--text-primary);
          margin-bottom: 1rem;
          padding-bottom: 0.5rem;
          border-bottom: 2px solid var(--primary-color);
        }

        ava-card {
          display: block;
          transition: transform 0.3s ease, box-shadow 0.3s ease;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
          }
        }

        // Footer Actions
        .footer-actions {
          display: flex;
          justify-content: flex-end;
          gap: 0.75rem;
        }

        // Footer Metadata
        .footer-metadata {
          display: flex;
          gap: 1.5rem;
          flex-wrap: wrap;

          .metadata-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            color: var(--text-secondary);

            span {
              color: var(--text-primary);
            }
          }
        }

        // Footer Status
        .footer-status {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;

            .status-dot {
              width: 8px;
              height: 8px;
              border-radius: 50%;

              &.online {
                background: #28a745;
                box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.2);
              }
            }

            span {
              font-size: 0.875rem;
              color: var(--text-primary);
              font-weight: 500;
            }
          }

          .status-actions {
            display: flex;
            gap: 0.5rem;
          }
        }

        // Footer Links
        .footer-links {
          display: flex;
          gap: 1.5rem;

          .footer-link {
            color: var(--primary-color);
            text-decoration: none;
            font-size: 0.875rem;
            font-weight: 500;
            transition: color 0.3s ease;

            &:hover {
              color: var(--primary-color-dark);
              text-decoration: underline;
            }
          }
        }

        // Complex Footer
        .complex-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-wrap: wrap;
          gap: 1rem;

          .footer-info {
            display: flex;
            gap: 1.5rem;
            flex-wrap: wrap;

            .info-item {
              display: flex;
              align-items: center;
              gap: 0.5rem;
              font-size: 0.875rem;
              color: var(--text-secondary);

              span {
                color: var(--text-primary);
              }
            }
          }

          .footer-actions {
            display: flex;
            gap: 0.5rem;
            flex-shrink: 0;
          }
        }
      }
    }
  }

  .features-section {
    h3 {
      font-size: 1.75rem;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 2rem;
      text-align: center;
    }

    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1.5rem;

      .feature-item {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        padding: 1.5rem;
        background: var(--surface-secondary);
        border-radius: 12px;
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
          border-color: var(--primary-color);
        }

        .feature-content {
          flex: 1;

          strong {
            display: block;
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
          }

          color: var(--text-secondary);
          line-height: 1.5;
        }
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 1rem;

    .demo-header {
      h2 {
        font-size: 2rem;
      }

      p {
        font-size: 1rem;
      }
    }

    .demo-section {
      .demo-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }
    }

    .features-section {
      .features-grid {
        grid-template-columns: 1fr;
        gap: 1rem;

        .feature-item {
          padding: 1rem;
        }
      }
    }
  }

  @media (max-width: 480px) {
    .demo-header {
      h2 {
        font-size: 1.75rem;
      }
    }

    .demo-section {
      .demo-grid {
        .demo-item {
          .footer-metadata {
            flex-direction: column;
            gap: 0.75rem;
          }

          .footer-status {
            flex-direction: column;
            gap: 1rem;
            align-items: flex-start;
          }

          .footer-links {
            flex-direction: column;
            gap: 0.75rem;
          }

          .complex-footer {
            flex-direction: column;
            align-items: flex-start;

            .footer-actions {
              align-self: flex-end;
            }
          }
        }
      }
    }

    .features-section {
      .features-grid {
        .feature-item {
          flex-direction: column;
          text-align: center;
          gap: 0.75rem;
        }
      }
    }
  }
}
