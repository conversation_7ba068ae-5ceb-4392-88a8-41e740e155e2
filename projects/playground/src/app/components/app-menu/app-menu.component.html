<div class="menu-demo-page">
  <!-- Header -->
  <div class="demo-header">
    <div class="container">
      <div class="row">
        <div class="col-12">
          <header class="doc-header">
            <h1>Menu Component</h1>
            <p class="description">
              A flexible dropdown menu component with support for icons,
              descriptions, multi-column layout, positioning, and full
              accessibility. Built with modern positioning system and
              comprehensive display options.
            </p>
          </header>
        </div>
      </div>
    </div>
  </div>

  <!-- Navigation Links to Demo Sections -->
  <div class="demo-navigation">
    <div class="container">
      <div class="row">
        <div class="col-12">
          <div class="nav-links">
            <h3>Demo Sections</h3>
            <div class="nav-grid">
              <a routerLink="/menu/basic-usage" class="nav-link">
                <span class="nav-icon">📝</span>
                <span class="nav-text">Basic Usage</span>
              </a>
              <a routerLink="/menu/with-icons" class="nav-link">
                <span class="nav-icon">🎯</span>
                <span class="nav-text">With Icons</span>
              </a>
              <a routerLink="/menu/with-descriptions" class="nav-link">
                <span class="nav-icon">📄</span>
                <span class="nav-text">With Descriptions</span>
              </a>
              <a routerLink="/menu/positioning" class="nav-link">
                <span class="nav-icon">📍</span>
                <span class="nav-text">Positioning</span>
              </a>
              <a routerLink="/menu/multi-column" class="nav-link">
                <span class="nav-icon">📊</span>
                <span class="nav-text">Multi-Column</span>
              </a>
              <a routerLink="/menu/disabled-items" class="nav-link">
                <span class="nav-icon">🚫</span>
                <span class="nav-text">Disabled Items</span>
              </a>
              <a routerLink="/menu/api" class="nav-link">
                <span class="nav-icon">📚</span>
                <span class="nav-text">API Reference</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Overview Section -->
  <div class="demo-sections">
    <section class="demo-section overview-bg">
      <div class="container">
        <div class="section-header">
          <h2>Menu Component Overview</h2>
          <p>
            Explore the enhanced menu component with flexible positioning and
            display options
          </p>
        </div>
        <div class="demo-content">
          <div class="row">
            <div class="col-12">
              <div class="overview-card">
                <h3>Key Features</h3>
                <ul class="features-list">
                  <li>
                    ✅ <strong>Flexible Positioning:</strong> 8 position options
                    with alignment control
                  </li>
                  <li>
                    ✅ <strong>Display Options:</strong> Configurable icons,
                    titles, and descriptions
                  </li>
                  <li>
                    ✅ <strong>Multi-Column Layout:</strong> Dynamic column
                    organization
                  </li>
                  <li>
                    ✅ <strong>Accessibility:</strong> Full keyboard navigation
                    and screen reader support
                  </li>
                  <li>
                    ✅ <strong>Theme Integration:</strong> CSS custom properties
                    for theming
                  </li>
                  <li>
                    ✅ <strong>State Management:</strong> Disabled items and
                    dividers
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</div>
