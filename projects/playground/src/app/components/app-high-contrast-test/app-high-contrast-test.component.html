<div class="high-contrast-test">
  <!-- Header -->
  <div class="test-header">
    <h1>♿ High Contrast Mode Testing</h1>
    <p>
      Enable high contrast mode in your OS settings to see how Play+ components
      adapt for accessibility. This page demonstrates how all AVA components
      respond to high contrast mode settings.
    </p>
    <div class="test-stats">
      <div class="stat">
        <span class="stat-number">15+</span>
        <span class="stat-label">Components Tested</span>
      </div>
      <div class="stat">
        <span class="stat-number">3</span>
        <span class="stat-label">Test Categories</span>
      </div>
      <div class="stat">
        <span class="stat-number">100%</span>
        <span class="stat-label">Accessibility</span>
      </div>
    </div>
  </div>

  <!-- High Contrast Mode Info -->
  <div class="high-contrast-info">
    <div class="info-header">
      <h2>High Contrast Mode Detection</h2>
      <div class="detection-status">
        <span class="status-badge" [class.active]="isHighContrastActive">
          {{ isHighContrastActive ? "Active" : "Inactive" }}
        </span>
      </div>
    </div>
    <div class="info-content">
      <p>
        High contrast mode is automatically detected using CSS media queries.
        When enabled in your operating system, all components will automatically
        adapt their colors, borders, and focus indicators for better
        accessibility.
      </p>
      <div class="instructions">
        <h4>How to Enable High Contrast Mode:</h4>
        <ul>
          <li>
            <strong>Windows:</strong> Settings → Ease of Access → High contrast
          </li>
          <li>
            <strong>macOS:</strong> System Preferences → Accessibility → Display
            → Increase contrast
          </li>
          <li>
            <strong>Linux:</strong> Settings → Universal Access → High contrast
          </li>
        </ul>
      </div>
    </div>
  </div>

  <!-- Tabs Navigation -->
  <div class="tabs-section">
    <!-- <ava-tabs
      [items]="tabItems"
      [activeTabId]="activeTabId"
      (tabChange)="onTabChange($event)"
    ></ava-tabs> -->
  </div>

  <!-- Component Preview Grid -->
  <div class="component-preview">
    <h2>🎯 Live Component Preview</h2>
    <p>
      See how actual Play+ components look with high contrast mode applied in
      real-time:
    </p>

    <div class="preview-grid">
      <!-- Buttons -->
      <div class="preview-item">
        <h3>Buttons</h3>
        <div class="button-showcase">
          <ava-button
            variant="primary"
            label="Primary Button"
            (userClick)="showTestSnackbar()"
          ></ava-button>
          <ava-button
            variant="secondary"
            label="Secondary Button"
            (userClick)="showTestSnackbar()"
          ></ava-button>
          <ava-button
            variant="success"
            label="Success Button"
            (userClick)="showTestSnackbar()"
          ></ava-button>
          <!-- <ava-button
            variant="error"
            label="Error Button"
            (userClick)="showTestSnackbar()"
          ></ava-button> -->
          <ava-button
            variant="primary"
            label="Disabled Button"
            [disabled]="true"
          ></ava-button>
        </div>
      </div>

      <!-- Glass Buttons -->
      <div class="preview-item">
        <h3>Glass Buttons</h3>
        <div class="glass-button-showcase">
          <ava-glass-button
            tone="positive"
            label="Glass Primary"
            (userClick)="showTestSnackbar()"
          ></ava-glass-button>
          <ava-glass-button
            tone="neutral"
            label="Glass Secondary"
            (userClick)="showTestSnackbar()"
          ></ava-glass-button>
          <ava-glass-button
            tone="negative"
            label="Glass Error"
            (userClick)="showTestSnackbar()"
          ></ava-glass-button>
        </div>
      </div>

      <!-- Form Elements -->
      <div class="preview-item">
        <h3>Form Elements</h3>
        <div class="form-showcase">
          <div class="input-section">
            <h4>Text Inputs</h4>
            <ava-textbox
              label="Name Input"
              placeholder="Enter your name..."
              [(ngModel)]="formData.name"
              [required]="true"
              variant="default"
            ></ava-textbox>

            <ava-textbox
              label="Email Input"
              placeholder="Enter email address..."
              [(ngModel)]="formData.email"
              variant="primary"
              type="email"
            ></ava-textbox>

            <ava-textarea
              label="Message"
              placeholder="Enter longer text here..."
              [(ngModel)]="formData.message"
              [rows]="3"
              variant="default"
            ></ava-textarea>
          </div>

          <div class="selection-section">
            <h4>Selection Controls</h4>
            <ava-checkbox
              label="I agree to terms"
              [(ngModel)]="formData.agree"
              (valueChange)="onCheckboxChange($event)"
            ></ava-checkbox>

            <ava-radio-button
              [options]="radioOptions"
              name="preference"
              [(ngModel)]="formData.preference"
              (valueChange)="onRadioChange($event)"
            ></ava-radio-button>

            <ava-toggle
              title="Enable notifications"
              [(ngModel)]="formData.notifications"
              (valueChange)="onToggleChange($event)"
            ></ava-toggle>
          </div>

          <div class="advanced-section">
            <h4>Advanced Controls</h4>
            <ava-dropdown
              label="Select Country"
              [options]="dropdownOptions"
              [(ngModel)]="formData.country"
              (valueChange)="onDropdownChange($event)"
            ></ava-dropdown>

            <ava-slider
              label="Volume Level"
              [(ngModel)]="formData.sliderValue"
              [min]="0"
              [max]="100"
              (valueChange)="onSliderChange($event)"
            ></ava-slider>
          </div>
        </div>
      </div>

      <!-- Display Components -->
      <div class="preview-item">
        <h3>Display Components</h3>
        <div class="display-showcase">
          <div class="badges-section">
            <h4>Badges</h4>
            <ava-badges state="high-priority" size="large" [count]="9"></ava-badges>
            <ava-badges state="medium-priority" size="large" [count]="9"></ava-badges>
            <ava-badges state="low-priority" size="large" [count]="9"></ava-badges>
            <ava-badges state="neutral" size="large" [count]="9"></ava-badges>
          </div>

          <div class="avatars-section">
            <h4>Avatars</h4>
            <!-- <ava-avatars size="sm" text="JD" variant="primary"></ava-avatars>
            <ava-avatars size="md" text="JS" variant="secondary"></ava-avatars>
            <ava-avatars size="lg" text="BJ" variant="success"></ava-avatars> -->
          </div>

          <div class="cards-section">
            <h4>Cards</h4>
            <ava-card>
              <div class="card-content">
                <h5>Sample Card</h5>
                <p>This card demonstrates high contrast mode adaptation.</p>
                <!-- <ava-button
                  variant="primary"
                  label="Card Action"
                  size="sm"
                ></ava-button> -->
              </div>
            </ava-card>
          </div>
        </div>
      </div>

      <!-- Feedback Components -->
      <div class="preview-item">
        <h3>Feedback & Status</h3>
        <div class="feedback-showcase">
          <div class="spinner-section">
            <h4>Loading States</h4>
            <ava-spinner
              size="md"
              color="primary"
              type="circular"
            ></ava-spinner>
            <ava-spinner
              size="md"
              color="secondary"
              type="dotted"
            ></ava-spinner>
            <ava-spinner
              size="lg"
              color="success"
              type="gradient"
            ></ava-spinner>
          </div>

          <div class="progress-section">
            <h4>Progress Indicators</h4>
            <ava-progressbar
              [percentage]="75"
              label="Linear Progress"
              type="linear"
              color="#2E308E"
            ></ava-progressbar>

            <ava-progressbar
              [percentage]="60"
              label="Circular Progress"
              type="circular"
              color="#4CAF50"
            ></ava-progressbar>
          </div>
        </div>
      </div>

      <!-- Navigation Components -->
      <div class="preview-item">
        <h3>Navigation</h3>
        <div class="navigation-showcase">
          <div class="links-section">
            <h4>Links</h4>
            <ava-link
              href="#"
              label="Primary Link"
              variant="primary"
            ></ava-link>
            <ava-link
              href="#"
              label="Secondary Link"
              variant="secondary"
            ></ava-link>
            <ava-link
              href="#"
              label="External Link"
              variant="external"
            ></ava-link>
          </div>

          <!-- <div class="list-section">
            <h4>List</h4>
            <ava-list [items]="listItems"></ava-list>
          </div> -->
        </div>
      </div>

      <!-- Interactive Elements -->
      <div class="preview-item">
        <h3>Interactive Elements</h3>
        <div class="interactive-showcase">
          <ava-button
            variant="primary"
            label="Show Popup"
            (userClick)="showTestPopup()"
          ></ava-button>
          <ava-button
            variant="secondary"
            label="Show Snackbar"
            (userClick)="showTestSnackbar()"
          ></ava-button>
        </div>
      </div>
    </div>
  </div>

  <!-- Accessibility Testing Section -->
  <div class="accessibility-testing">
    <h2>🔍 Accessibility Testing Checklist</h2>
    <div class="checklist-grid">
      <div class="checklist-item">
        <h4>Color Contrast</h4>
        <ul>
          <li>✓ Text has sufficient contrast ratio</li>
          <li>✓ Interactive elements are clearly distinguishable</li>
          <li>✓ Focus indicators are visible</li>
        </ul>
      </div>

      <div class="checklist-item">
        <h4>Keyboard Navigation</h4>
        <ul>
          <li>✓ All interactive elements are keyboard accessible</li>
          <li>✓ Tab order is logical and intuitive</li>
          <li>✓ Focus indicators are clearly visible</li>
        </ul>
      </div>

      <div class="checklist-item">
        <h4>Screen Reader Support</h4>
        <ul>
          <li>✓ Proper ARIA labels and roles</li>
          <li>✓ Semantic HTML structure</li>
          <li>✓ Alternative text for images</li>
        </ul>
      </div>

      <div class="checklist-item">
        <h4>High Contrast Mode</h4>
        <ul>
          <li>✓ Colors adapt to system settings</li>
          <li>✓ Borders and backgrounds are clearly defined</li>
          <li>✓ Text remains readable</li>
        </ul>
      </div>
    </div>
  </div>

  <!-- Popup Component -->
  <!-- <ava-popup
    [isOpen]="showPopup"
    (close)="hidePopup()"
    title="High Contrast Test Popup"
  >
    <div class="popup-content">
      <p>
        This popup demonstrates how modal dialogs adapt to high contrast mode.
      </p>
      <div class="popup-actions">
        <ava-button
          variant="primary"
          label="Confirm"
          (userClick)="hidePopup()"
        ></ava-button>
        <ava-button
          variant="secondary"
          label="Cancel"
          (userClick)="hidePopup()"
        ></ava-button>
      </div>
    </div>
  </ava-popup> -->

  <!-- Snackbar Component -->
  <!-- <ava-snackbar
    [isOpen]="showSnackbar"
    message="High contrast mode test completed successfully!"
    type="success"
  ></ava-snackbar> -->
</div>
