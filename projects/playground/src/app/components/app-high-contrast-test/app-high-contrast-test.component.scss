.high-contrast-test {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  font-family: var(
    --font-family-display,
    -apple-system,
    BlinkMacSystemFont,
    "Segoe UI",
    Roboto,
    sans-serif
  );
  color: var(--color-text-primary);
  background: var(--color-background-primary);
  min-height: 100vh;

  .test-header {
    text-align: center;
    margin-bottom: 3rem;

    h1 {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--color-text-primary);
      margin-bottom: 1rem;
    }

    p {
      font-size: 1.2rem;
      color: var(--color-text-secondary);
      max-width: 600px;
      margin: 0 auto 2rem;
    }

    .test-stats {
      display: flex;
      justify-content: center;
      gap: 3rem;
      margin-top: 2rem;

      .stat {
        text-align: center;

        .stat-number {
          display: block;
          font-size: 2rem;
          font-weight: 700;
          color: var(--color-brand-primary);
        }

        .stat-label {
          color: var(--color-text-secondary);
          font-size: 0.9rem;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }
      }
    }
  }

  .high-contrast-info {
    background: var(--color-background-secondary);
    border: 1px solid var(--color-border-default);
    border-radius: var(--global-radius-lg);
    padding: 2rem;
    margin-bottom: 3rem;

    .info-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;

      h2 {
        font-size: 1.5rem;
        color: var(--color-text-primary);
        margin: 0;
      }

      .detection-status {
        .status-badge {
          padding: 0.5rem 1rem;
          border-radius: var(--global-radius-md);
          font-weight: 600;
          font-size: 0.9rem;
          background: var(--color-surface-disabled);
          color: var(--color-text-disabled);

          &.active {
            background: var(--color-surface-success);
            color: var(--color-text-on-success);
          }
        }
      }
    }

    .info-content {
      p {
        color: var(--color-text-secondary);
        margin-bottom: 1.5rem;
        line-height: 1.6;
      }

      .instructions {
        h4 {
          color: var(--color-text-primary);
          margin-bottom: 1rem;
          font-size: 1.1rem;
        }

        ul {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            color: var(--color-text-secondary);
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;

            &::before {
              content: "•";
              position: absolute;
              left: 0;
              color: var(--color-brand-primary);
              font-weight: bold;
            }

            strong {
              color: var(--color-text-primary);
            }
          }
        }
      }
    }
  }

  .tabs-section {
    margin-bottom: 3rem;
  }

  .component-preview {
    margin-bottom: 3rem;

    h2 {
      font-size: 1.8rem;
      color: var(--color-text-primary);
      margin-bottom: 0.5rem;
    }

    > p {
      color: var(--color-text-secondary);
      margin-bottom: 2rem;
    }

    .preview-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
      gap: 2rem;

      .preview-item {
        background: var(--color-background-secondary);
        border: 1px solid var(--color-border-default);
        border-radius: var(--global-radius-lg);
        padding: 1.5rem;

        h3 {
          color: var(--color-text-primary);
          margin-bottom: 1rem;
          font-size: 1.2rem;
        }

        h4 {
          color: var(--color-text-primary);
          margin-bottom: 0.75rem;
          font-size: 1rem;
        }

        .button-showcase,
        .glass-button-showcase {
          display: flex;
          flex-wrap: wrap;
          gap: 0.75rem;
          margin-bottom: 1rem;
        }

        .form-showcase {
          .input-section,
          .selection-section,
          .advanced-section {
            margin-bottom: 1.5rem;

            ava-textbox,
            ava-textarea,
            ava-dropdown,
            ava-slider {
              margin-bottom: 1rem;
            }
          }

          .selection-section {
            ava-checkbox,
            ava-radio-button,
            ava-toggle {
              margin-bottom: 0.75rem;
            }
          }
        }

        .display-showcase {
          .badges-section,
          .avatars-section,
          .cards-section {
            margin-bottom: 1.5rem;

            .badges-section {
              display: flex;
              flex-wrap: wrap;
              gap: 0.5rem;
            }

            .avatars-section {
              display: flex;
              gap: 1rem;
              align-items: center;
            }

            .card-content {
              h5 {
                color: var(--color-text-primary);
                margin-bottom: 0.5rem;
              }

              p {
                color: var(--color-text-secondary);
                margin-bottom: 1rem;
              }
            }
          }
        }

        .feedback-showcase {
          .spinner-section,
          .progress-section {
            margin-bottom: 1.5rem;

            .spinner-section {
              display: flex;
              gap: 1rem;
              align-items: center;
            }
          }
        }

        .navigation-showcase {
          .links-section,
          .list-section {
            margin-bottom: 1.5rem;

            .links-section {
              display: flex;
              flex-direction: column;
              gap: 0.5rem;
            }
          }
        }

        .interactive-showcase {
          display: flex;
          gap: 1rem;
        }
      }
    }
  }

  .accessibility-testing {
    background: var(--color-background-secondary);
    border: 1px solid var(--color-border-default);
    border-radius: var(--global-radius-lg);
    padding: 2rem;
    margin-bottom: 3rem;

    h2 {
      font-size: 1.5rem;
      color: var(--color-text-primary);
      margin-bottom: 1.5rem;
      text-align: center;
    }

    .checklist-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;

      .checklist-item {
        h4 {
          color: var(--color-text-primary);
          margin-bottom: 1rem;
          font-size: 1.1rem;
        }

        ul {
          list-style: none;
          padding: 0;
          margin: 0;

          li {
            color: var(--color-text-secondary);
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
            position: relative;

            &::before {
              content: "✓";
              position: absolute;
              left: 0;
              color: var(--color-surface-success);
              font-weight: bold;
            }
          }
        }
      }
    }
  }

  .popup-content {
    .popup-actions {
      display: flex;
      gap: 1rem;
      justify-content: flex-end;
      margin-top: 1.5rem;
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    padding: 1rem;

    .test-header {
      .test-stats {
        flex-direction: column;
        gap: 1rem;
      }
    }

    .preview-grid {
      grid-template-columns: 1fr;
    }

    .checklist-grid {
      grid-template-columns: 1fr;
    }

    .button-showcase,
    .glass-button-showcase {
      flex-direction: column;
    }

    .interactive-showcase {
      flex-direction: column;
    }
  }

  // High Contrast Mode Specific Styles
  @media (prefers-contrast: more) {
    .preview-item {
      border-width: 2px;
      border-color: var(--color-border-high-contrast);
    }

    .status-badge {
      border: 2px solid var(--color-border-high-contrast);
    }

    .checklist-item ul li::before {
      color: var(--color-text-high-contrast);
    }
  }
}
