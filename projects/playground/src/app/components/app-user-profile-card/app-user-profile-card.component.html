<div class="user-profile-card-demo">
  <div class="demo-header">
    <h2>User Profile Card Component</h2>
    <p>
      A comprehensive user profile card component with customizable display
      options, interactive actions, and multiple themes and layouts.
    </p>
  </div>

  <!-- Profile Selection -->
  <div class="profile-selection-card">
    <ava-card>
      <div header>
        <h3>Select Profile</h3>
      </div>
      <div content>
        <div class="profile-tabs">
          <ava-button
            *ngFor="let profile of profiles; let i = index"
            (click)="selectProfile(i)"
            [variant]="selectedProfileIndex === i ? 'primary' : 'secondary'"
            size="small"
            [label]="profile.name"
            [iconName]="profile.status === 'online' ? 'circle' : 'user'"
            [iconSize]="14"
          >
          </ava-button>
        </div>
      </div>
    </ava-card>
  </div>

  <!-- Configuration Controls -->
  <div class="controls-card">
    <ava-card>
      <div header>
        <h3>Configuration Controls</h3>
      </div>
      <div content>
        <div class="controls-grid">
          <!-- Theme Controls -->
          <div class="control-group">
            <h4>Theme</h4>
            <div class="button-group">
              <ava-button
                (click)="changeTheme('default')"
                [variant]="config.theme === 'default' ? 'primary' : 'secondary'"
                size="small"
                label="Default"
              >
              </ava-button>
              <ava-button
                (click)="changeTheme('minimal')"
                [variant]="config.theme === 'minimal' ? 'primary' : 'secondary'"
                size="small"
                label="Minimal"
              >
              </ava-button>
              <ava-button
                (click)="changeTheme('modern')"
                [variant]="config.theme === 'modern' ? 'primary' : 'secondary'"
                size="small"
                label="Modern"
              >
              </ava-button>
              <ava-button
                (click)="changeTheme('professional')"
                [variant]="
                  config.theme === 'professional' ? 'primary' : 'secondary'
                "
                size="small"
                label="Professional"
              >
              </ava-button>
            </div>
          </div>

          <!-- Layout Controls -->
          <div class="control-group">
            <h4>Layout</h4>
            <div class="button-group">
              <ava-button
                (click)="changeLayout('vertical')"
                [variant]="
                  config.layout === 'vertical' ? 'primary' : 'secondary'
                "
                size="small"
                label="Vertical"
              >
              </ava-button>
              <ava-button
                (click)="changeLayout('horizontal')"
                [variant]="
                  config.layout === 'horizontal' ? 'primary' : 'secondary'
                "
                size="small"
                label="Horizontal"
              >
              </ava-button>
              <ava-button
                (click)="changeLayout('compact')"
                [variant]="
                  config.layout === 'compact' ? 'primary' : 'secondary'
                "
                size="small"
                label="Compact"
              >
              </ava-button>
            </div>
          </div>

          <!-- Avatar Size Controls -->
          <div class="control-group">
            <h4>Avatar Size</h4>
            <div class="button-group">
              <ava-button
                (click)="changeAvatarSize('small')"
                [variant]="
                  config.avatarSize === 'small' ? 'primary' : 'secondary'
                "
                size="small"
                label="Small"
              >
              </ava-button>
              <ava-button
                (click)="changeAvatarSize('medium')"
                [variant]="
                  config.avatarSize === 'medium' ? 'primary' : 'secondary'
                "
                size="small"
                label="Medium"
              >
              </ava-button>
              <ava-button
                (click)="changeAvatarSize('large')"
                [variant]="
                  config.avatarSize === 'large' ? 'primary' : 'secondary'
                "
                size="small"
                label="Large"
              >
              </ava-button>
            </div>
          </div>

          <!-- Section Toggles -->
          <div class="control-group">
            <h4>Display Sections</h4>
            <div class="toggle-group">
              <ava-button
                (click)="toggleSection('showAvatar')"
                [variant]="config.showAvatar ? 'success' : 'secondary'"
                size="small"
                [label]="config.showAvatar ? 'Avatar: On' : 'Avatar: Off'"
                [iconName]="config.showAvatar ? 'check' : 'x'"
                [iconSize]="12"
              >
              </ava-button>
              <ava-button
                (click)="toggleSection('showStatus')"
                [variant]="config.showStatus ? 'success' : 'secondary'"
                size="small"
                [label]="config.showStatus ? 'Status: On' : 'Status: Off'"
                [iconName]="config.showStatus ? 'check' : 'x'"
                [iconSize]="12"
              >
              </ava-button>
              <ava-button
                (click)="toggleSection('showBio')"
                [variant]="config.showBio ? 'success' : 'secondary'"
                size="small"
                [label]="config.showBio ? 'Bio: On' : 'Bio: Off'"
                [iconName]="config.showBio ? 'check' : 'x'"
                [iconSize]="12"
              >
              </ava-button>
              <ava-button
                (click)="toggleSection('showSkills')"
                [variant]="config.showSkills ? 'success' : 'secondary'"
                size="small"
                [label]="config.showSkills ? 'Skills: On' : 'Skills: Off'"
                [iconName]="config.showSkills ? 'check' : 'x'"
                [iconSize]="12"
              >
              </ava-button>
              <ava-button
                (click)="toggleSection('showStats')"
                [variant]="config.showStats ? 'success' : 'secondary'"
                size="small"
                [label]="config.showStats ? 'Stats: On' : 'Stats: Off'"
                [iconName]="config.showStats ? 'check' : 'x'"
                [iconSize]="12"
              >
              </ava-button>
              <ava-button
                (click)="toggleSection('showSocialLinks')"
                [variant]="config.showSocialLinks ? 'success' : 'secondary'"
                size="small"
                [label]="config.showSocialLinks ? 'Social: On' : 'Social: Off'"
                [iconName]="config.showSocialLinks ? 'check' : 'x'"
                [iconSize]="12"
              >
              </ava-button>
              <ava-button
                (click)="toggleSection('showContactInfo')"
                [variant]="config.showContactInfo ? 'success' : 'secondary'"
                size="small"
                [label]="
                  config.showContactInfo ? 'Contact: On' : 'Contact: Off'
                "
                [iconName]="config.showContactInfo ? 'check' : 'x'"
                [iconSize]="12"
              >
              </ava-button>
              <ava-button
                (click)="toggleSection('showActions')"
                [variant]="config.showActions ? 'success' : 'secondary'"
                size="small"
                [label]="config.showActions ? 'Actions: On' : 'Actions: Off'"
                [iconName]="config.showActions ? 'check' : 'x'"
                [iconSize]="12"
              >
              </ava-button>
            </div>
          </div>

          <!-- State Controls -->
          <div class="control-group">
            <h4>Component States</h4>
            <div class="button-group">
              <ava-button
                (click)="toggleLoading()"
                [variant]="loading ? 'warning' : 'primary'"
                size="small"
                [label]="loading ? 'Stop Loading' : 'Start Loading'"
                [iconName]="loading ? 'pause' : 'play'"
                [iconSize]="14"
              >
              </ava-button>
              <ava-button
                (click)="toggleDisabled()"
                [variant]="disabled ? 'success' : 'secondary'"
                size="small"
                [label]="disabled ? 'Enable' : 'Disable'"
                [iconName]="disabled ? 'unlock' : 'lock'"
                [iconSize]="14"
              >
              </ava-button>
            </div>
          </div>

          <!-- Limits Controls -->
          <div class="control-group">
            <h4>Display Limits</h4>
            <div class="limits-group">
              <div class="limit-item">
                <label for="maxSkills"
                  >Max Skills: {{ config.maxSkills }}</label
                >
                <input
                  id="maxSkills"
                  type="range"
                  [min]="1"
                  [max]="10"
                  [value]="config.maxSkills"
                  (input)="changeMaxSkills($event)"
                />
              </div>
              <div class="limit-item">
                <label for="maxBioLength"
                  >Max Bio Length: {{ config.maxBioLength }}</label
                >
                <input
                  id="maxBioLength"
                  type="range"
                  [min]="50"
                  [max]="300"
                  [step]="25"
                  [value]="config.maxBioLength"
                  (input)="changeMaxBioLength($event)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </ava-card>
  </div>

  <!-- User Profile Card Demo -->
  <div class="demo-section">
    <ava-card>
      <div header>
        <h3>Interactive User Profile Card</h3>
        <p>
          Try interacting with the profile card and observe the events below
        </p>
      </div>
      <div content>
        <ava-user-profile-card
          [profile]="currentProfile"
          [config]="config"
          [actions]="actions"
          [loading]="loading"
          [disabled]="disabled"
          (profileView)="onProfileView($event)"
          (profileEdit)="onProfileEdit($event)"
          (actionClick)="onActionClick($event)"
          (avatarClick)="onAvatarClick($event)"
          (statusChange)="onStatusChange($event)"
          (contactClick)="onContactClick($event)"
          (socialClick)="onSocialClick($event)"
          (profileEvent)="onProfileEvent($event)"
        >
        </ava-user-profile-card>
      </div>
    </ava-card>
  </div>

  <!-- Event Log -->
  <div class="event-log-card" *ngIf="lastEvent">
    <ava-card>
      <div header>
        <h3>Event Log</h3>
        <p>Real-time events from the profile card</p>
      </div>
      <div content>
        <div class="event-content">
          <pre>{{ lastEvent | json }}</pre>
        </div>
      </div>
    </ava-card>
  </div>

  <!-- Features Card -->
  <div class="features-card">
    <ava-card>
      <div header>
        <h3>Features Demonstrated</h3>
        <p>Comprehensive list of capabilities</p>
      </div>
      <div content>
        <div class="features-grid">
          <div class="feature-item">
            <ava-icon
              iconName="user"
              [iconSize]="20"
              iconColor="#007bff"
            ></ava-icon>
            <div class="feature-content">
              <strong>User Information:</strong> Display name, role, department,
              location, and contact details
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="image"
              [iconSize]="20"
              iconColor="#28a745"
            ></ava-icon>
            <div class="feature-content">
              <strong>Avatar Support:</strong> Profile pictures with fallback to
              initials
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="circle"
              [iconSize]="20"
              iconColor="#ffc107"
            ></ava-icon>
            <div class="feature-content">
              <strong>Status Indicators:</strong> Online, away, busy, do not
              disturb, and offline states
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="file-text"
              [iconSize]="20"
              iconColor="#17a2b8"
            ></ava-icon>
            <div class="feature-content">
              <strong>Bio Section:</strong> Expandable biography with character
              limits
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="tag"
              [iconSize]="20"
              iconColor="#6f42c1"
            ></ava-icon>
            <div class="feature-content">
              <strong>Skills Display:</strong> Tag-based skills with expandable
              view
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="chart-bar"
              [iconSize]="20"
              iconColor="#fd7e14"
            ></ava-icon>
            <div class="feature-content">
              <strong>Statistics:</strong> Projects, tasks, contributions, and
              experience metrics
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="share-2"
              [iconSize]="20"
              iconColor="#e83e8c"
            ></ava-icon>
            <div class="feature-content">
              <strong>Social Links:</strong> LinkedIn, Twitter, GitHub, and
              website integration
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="settings"
              [iconSize]="20"
              iconColor="#6c757d"
            ></ava-icon>
            <div class="feature-content">
              <strong>Customizable:</strong> Multiple themes, layouts, and
              display options
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="zap"
              [iconSize]="20"
              iconColor="#dc3545"
            ></ava-icon>
            <div class="feature-content">
              <strong>Interactive Actions:</strong> Message, schedule, follow,
              and other user actions
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="smartphone"
              [iconSize]="20"
              iconColor="#20c997"
            ></ava-icon>
            <div class="feature-content">
              <strong>Responsive Design:</strong> Adapts to different screen
              sizes and devices
            </div>
          </div>
        </div>
      </div>
    </ava-card>
  </div>
</div>
