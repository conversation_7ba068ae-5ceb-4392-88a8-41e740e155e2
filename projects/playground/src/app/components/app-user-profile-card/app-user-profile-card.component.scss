.user-profile-card-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 24px;

  .demo-header {
    text-align: center;
    margin-bottom: 20px;

    h2 {
      margin: 0 0 12px 0;
      font-size: 2rem;
      font-weight: 700;
      color: #212529;
    }

    p {
      margin: 0;
      font-size: 1.1rem;
      color: #6c757d;
      line-height: 1.6;
    }
  }

  .profile-selection-card {
    .profile-tabs {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      justify-content: center;
    }
  }

  .controls-card {
    .controls-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 24px;
    }

    .control-group {
      h4 {
        margin: 0 0 12px 0;
        font-size: 1rem;
        font-weight: 600;
        color: #212529;
      }

      .button-group {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }

      .toggle-group {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 8px;
      }

      .limits-group {
        display: flex;
        flex-direction: column;
        gap: 16px;
      }

      .limit-item {
        display: flex;
        flex-direction: column;
        gap: 8px;

        label {
          font-size: 0.875rem;
          font-weight: 500;
          color: #495057;
        }

        input[type="range"] {
          width: 100%;
          height: 6px;
          border-radius: 3px;
          background: #e9ecef;
          outline: none;
          -webkit-appearance: none;

          &::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #007bff;
            cursor: pointer;
            border: 2px solid #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          }

          &::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #007bff;
            cursor: pointer;
            border: 2px solid #fff;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          }
        }
      }
    }
  }

  .demo-section {
    .ava-user-profile-card {
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .event-log-card {
    .event-content {
      background: #f8f9fa;
      border-radius: 6px;
      padding: 16px;
      font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
      font-size: 0.875rem;
      line-height: 1.5;
      overflow-x: auto;

      pre {
        margin: 0;
        color: #495057;
      }
    }
  }

  .features-card {
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 16px;
    }

    .feature-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
      transition: all 0.2s ease-in-out;

      &:hover {
        background: #e9ecef;
        transform: translateY(-2px);
      }

      .feature-content {
        flex: 1;
        font-size: 0.875rem;
        line-height: 1.5;
        color: #495057;

        strong {
          color: #212529;
          font-weight: 600;
        }
      }
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    padding: 16px;
    gap: 20px;

    .demo-header {
      h2 {
        font-size: 1.75rem;
      }

      p {
        font-size: 1rem;
      }
    }

    .controls-card {
      .controls-grid {
        grid-template-columns: 1fr;
        gap: 20px;
      }

      .control-group {
        .toggle-group {
          grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        }
      }
    }

    .features-card {
      .features-grid {
        grid-template-columns: 1fr;
      }
    }
  }

  @media (max-width: 480px) {
    padding: 12px;
    gap: 16px;

    .demo-header {
      h2 {
        font-size: 1.5rem;
      }
    }

    .profile-selection-card {
      .profile-tabs {
        flex-direction: column;
        align-items: stretch;
      }
    }

    .controls-card {
      .control-group {
        .button-group {
          flex-direction: column;
        }

        .toggle-group {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}
