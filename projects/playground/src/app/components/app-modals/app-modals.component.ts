// import { Component } from '@angular/core';
// import { ModalComponent } from "../../../../../play-comp-library/src/lib/components/modal/modal.component";

// @Component({
//   selector: 'app-app-modals',
//   imports: [ModalComponent],
//   templateUrl: './app-modals.component.html',
//   styleUrl: './app-modals.component.scss'
// })
// export class AppModalsComponent {
//   //modals
 
 
// isModalOpen = false;
// modalType:
//   | 'Modal Small Light'
//   | 'Modal Medium Light'
//   | 'Modal Large Light'
//   | 'Modal Small Dark'
//   | 'Modal Medium Dark'
//   | 'Modal Large Dark' = 'Modal Medium Light';
 
// openModal(type:
//   | 'Modal Small Light'
//   | 'Modal Medium Light'
//   | 'Modal Large Light'
//   | 'Modal Small Dark'
//   | 'Modal Medium Dark'
//   | 'Modal Large Dark'): void {
//   console.log(`Opening modal of type: ${type}`);
//   this.modalType = type;
//   this.isModalOpen = true;
// }
 
// closeModal(): void {
//   console.log('Closing modal');
//   this.isModalOpen = false;
// }
 
// handlePrimaryAction(): void {
//   console.log(`Primary button clicked for ${this.modalType}`);
//   this.closeModal();
// }
// }
// import { Component } from '@angular/core';
// import { ModalComponent } from "../../../../../play-comp-library/src/lib/components/modal/modal.component";
// import { BodyTextComponent, ButtonComponent, CheckboxComponent, HeadingComponent,InputComponent } from '../../../../../play-comp-library/src/public-api';

// @Component({
//   selector: 'app-app-modals',
//   imports: [ModalComponent,ButtonComponent,HeadingComponent,BodyTextComponent,InputComponent,CheckboxComponent],
//   templateUrl: './app-modals.component.html',
//   styleUrl: './app-modals.component.scss'
// })
// export class AppModalsComponent {
//   isBottomLargeModalOpen = false;
//   isTopLeftModalOpen = false;
//   isTopRightModalOpen = false;
//   isBottomLeftModalOpen = false;
//   isBottomRightModalOpen = false;
//   isFullscreenModalOpen = false;
//   isTopFrameModalOpen=false;
//   isBottomFrameModalOpen =false;

//   // Small Modals
//   isSmallLightOpen: boolean = false;
//   isSmallDarkOpen: boolean = false;

//   // Medium Modals
//   isMediumLightOpen: boolean = false;
//   isMediumDarkOpen: boolean = false;

//   // Large Modals
//   isLargeLightOpen: boolean = false;
//   isLargeDarkOpen: boolean = false;

//   // Extra Large Modals
//   isExtraLargeLightOpen: boolean = false;
//   isExtraLargeDarkOpen: boolean = false;

//   // Function to open modal
//   openModal(size: string, theme: string) {
//     switch (`${size}-${theme}`) {
//       case 'small-light':
//         this.isSmallLightOpen = true;
//         break;
//       case 'small-dark':
//         this.isSmallDarkOpen = true;
//         break;
//       case 'medium-light':
//         this.isMediumLightOpen = true;
//         break;
//       case 'medium-dark':
//         this.isMediumDarkOpen = true;
//         break;
//       case 'large-light':
//         this.isLargeLightOpen = true;
//         break;
//       case 'large-dark':
//         this.isLargeDarkOpen = true;
//         break;
//       case 'extra-large-light':
//         this.isExtraLargeLightOpen = true;
//         break;
//       case 'extra-large-dark':
//         this.isExtraLargeDarkOpen = true;
//         break;
//     }
//   }

//   // Function to close all modals
//   closeModal(size: string, theme: string) {
//     switch (`${size}-${theme}`) {
//       case 'small-light':
//         this.isSmallLightOpen = false;
//         break;
//       case 'small-dark':
//         this.isSmallDarkOpen = false;
//         break;
//       case 'medium-light':
//         this.isMediumLightOpen = false;
//         break;
//       case 'medium-dark':
//         this.isMediumDarkOpen = false;
//         break;
//       case 'large-light':
//         this.isLargeLightOpen = false;
//         break;
//       case 'large-dark':
//         this.isLargeDarkOpen = false;
//         break;
//       case 'extra-large-light':
//         this.isExtraLargeLightOpen = false;
//         break;
//       case 'extra-large-dark':
//         this.isExtraLargeDarkOpen = false;
//         break;
//     }
//   }
// }


import { Component, ViewEncapsulation } from '@angular/core';
import { ModalComponent } from "../../../../../play-comp-library/src/lib/components/modal/modal.component";
import { BodyTextComponent, ButtonComponent, CheckboxComponent, HeadingComponent, InputComponent } from '../../../../../play-comp-library/src/public-api';
import { IconsComponent } from "../../../../../play-comp-library/src/lib/components/icons/icons.component";
import { CommonModule } from '@angular/common';

interface ModalDocSection {
  title: string;
  description: string;
  showCode: boolean;
}

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

@Component({
  selector: 'app-app-modals',
  imports: [ModalComponent, ButtonComponent, HeadingComponent, BodyTextComponent, InputComponent, CheckboxComponent, IconsComponent, CommonModule],
  templateUrl: './app-modals.component.html',
  styleUrls: ['./app-modals.component.scss'],
     encapsulation: ViewEncapsulation.None
})
export class AppModalsComponent {
  // Documentation Sections
  sections: ModalDocSection[] = [
    {
      title: 'Basic Modals',
      description: 'Modal component with basic configurations such as header, footer, and content.',
      showCode: false
    },
    {
      title: 'Modal Sizes',
      description: 'Available sizes for the modal: small, medium, large, and extra-large.',
      showCode: false
    },
    {
      title: 'Modal Themes',
      description: 'Available themes for the modal: light and dark.',
      showCode: false
    },
    {
      title: 'Modal Animations',
      description: 'Enabling or disabling animation during modal transitions.',
      showCode: false
    },
    {
      title: 'Fullscreen Modal',
      description: 'Modal that covers the full screen.',
      showCode: false
    },
    {
      title: 'Frame Modal',
      description: 'Modal that appears as a frame at the top or bottom of the screen.',
      showCode: false
    },
    {
      title: 'Experience studio',
      description: 'Modal component with basic configurations such as header, footer, and content.',
      showCode: false
    }
  ];

  // API Documentation
  apiProps: ApiProperty[] = [
    { name: 'isOpen', type: 'boolean', default: 'false', description: 'Indicates whether the modal is open.' },
    { name: 'theme', type: "'light' | 'dark'", default: "'light'", description: 'Sets the theme of the modal.' },
    { name: 'size', type: "'small' | 'medium' | 'large' | 'extra-large' | 'fullscreen'", default: "'medium'", description: 'Sets the size of the modal.' },
    { name: 'position', type: "'center' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' | 'top' | 'bottom'", default: "'center'", description: 'Sets the position of the modal.' },
    { name: 'hasHeader', type: 'boolean', default: 'false', description: 'Indicates whether the modal has a header.' },
    { name: 'hasFooter', type: 'boolean', default: 'false', description: 'Indicates whether the modal has a footer.' },
    { name: 'animation', type: 'boolean', default: 'false', description: 'Enables or disables animation during modal transitions.' },
    { name: 'animationType', type: 'string', default: "'zoomIn'", description: 'Sets the type of animation for the modal.' }
  ];

  // Events
  events = [
    { name: 'close', type: 'EventEmitter<void>', description: 'Emitted when the modal is closed.' }
  ];

  // Modal States
  isSmallLightOpen: boolean = false;
  isSmallDarkOpen: boolean = false;
  isMediumLightOpen: boolean = false;
  isMediumDarkOpen: boolean = false;
  isLargeLightOpen: boolean = false;
  isLargeDarkOpen: boolean = false;
  isExtraLargeLightOpen: boolean = false;
  isExtraLargeDarkOpen: boolean = false;
  isTopLeftModalOpen: boolean = false;
  isTopRightModalOpen: boolean = false;
  isBottomLeftModalOpen: boolean = false;
  isBottomRightModalOpen: boolean = false;
  isFullscreenModalOpen: boolean = false;
  isTopFrameModalOpen: boolean = false;
  isBottomFrameModalOpen: boolean = false;
  isExperienceStudioModalOpen: boolean = false;

  // Section Expansion
  toggleSection(index: number): void {
    this.sections[index].showCode = !this.sections[index].showCode;
  }
  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation(); // Prevent the click event from bubbling up to the section header
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  // Example Code Generator
  getExampleCode(section: string): string {
    const examples: Record<string, string> = {
      'basic modals': `<awe-modal [isOpen]="isSmallLightOpen" theme="light" size="small" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isSmallLightOpen = false">
  <div header><awe-heading variant="h6" type="regular">Subtitle goes here</awe-heading></div>
  <div content>
    <awe-body-text type="body-test">AI is here to help you achieve more in less <br> time! With our AI-powered tools,</awe-body-text>
  </div>
  <div footer>
    <awe-button label="Label" variant="secondary" (click)="isSmallLightOpen = false"></awe-button>
    <awe-button label="Label" variant="primary" (click)="isSmallLightOpen = false"></awe-button>
  </div>
</awe-modal>
 <awe-button label="Open Small Modal" variant="primary" (click)="isSmallLightOpen = true"></awe-button>`,

      'modal sizes': `<awe-modal [isOpen]="isSmallLightOpen" theme="light" size="small" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isSmallLightOpen = false">
  <div header><awe-heading variant="h6" type="regular">Subtitle goes here</awe-heading></div>
  <div content>
    <awe-body-text type="body-test">AI is here to help you achieve more in less <br> time! With our AI-powered tools,</awe-body-text>
  </div>
  <div footer>
    <awe-button label="Label" variant="secondary" (click)="isSmallLightOpen = false"></awe-button>
    <awe-button label="Label" variant="primary" (click)="isSmallLightOpen = false"></awe-button>
  </div>
</awe-modal>
<awe-button label="Open Small Modal" variant="primary" (click)="isSmallLightOpen = true"></awe-button>

<awe-modal [isOpen]="isMediumLightOpen" theme="light" size="medium" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isMediumLightOpen = false">
  <div header><awe-heading variant="h5" type="regular">Discover Our New AI-Powered Tool</awe-heading></div>
  <div content>
    <awe-body-text type="body-test">
      <ng-container>
        "We’re excited to introduce a new feature that<br> simplifies your workflow! With AI, you can now:
        <ul>
          <li>Generate content in seconds.</li>
          <li>Analyze data effortlessly.</li>
          <li>Get personalized recommendations tailored to your needs."</li>
        </ul>
      </ng-container>
    </awe-body-text>
  </div>
  <div footer class="footerbutton">
    <awe-button label="Label" variant="secondary" (click)="isMediumLightOpen = false"></awe-button>
    <awe-button label="Label" variant="primary" (click)="isMediumLightOpen = false"></awe-button>
  </div>
</awe-modal>
  <awe-button label="Open Medium Modal" variant="primary" (click)="isMediumLightOpen = true"></awe-button>

<awe-modal [isOpen]="isLargeLightOpen" theme="light" size="large" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isLargeLightOpen = false">
  <div content>
    <img class="image" src="assets/modal/image.png" alt="Modal component preview with UI elements">
    <awe-heading variant="h4" type="regular">Experience the future of analytics with our AI-powered dashboard.</awe-heading>
    <awe-body-text type="body-test">
      <ng-container>
        <ul>
          <li>Real-time data visualization.</li>
          <li>Customizable widgets for tailored insights.</li>
          <li>AI-driven recommendations to optimize performance.</li>
        </ul>
      </ng-container>
    </awe-body-text>
  </div>
  <div footer class="footerbutton">
    <awe-button label="Label" variant="secondary" (click)="isLargeLightOpen = false"></awe-button>
    <awe-button label="Label" variant="primary" (click)="isLargeLightOpen = false"></awe-button>
  </div>
</awe-modal>
 <awe-button label="Open Large Modal" variant="primary" (click)="isLargeLightOpen = true"></awe-button>

<awe-modal [isOpen]="isExtraLargeLightOpen" theme="light" size="extra-large" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isExtraLargeLightOpen = false">
  <div header>
    <awe-heading variant="h2" type="regular">From validation</awe-heading>
  </div>
  <div content>
    <awe-input label="Username" placeholder="Enter your username"></awe-input>
    <awe-input label="name" placeholder="Enter your name"></awe-input>
    <awe-input label="email" placeholder="Enter your email"></awe-input>
  </div>
  <div footer>
  <awe-button label="Close" variant="secondary" (click)="isExtraLargeLightOpen = false"></awe-button>
    <awe-button label="Open" variant="primary" (click)="isExtraLargeLightOpen = false"></awe-button>
  </div>
</awe-modal>
<awe-button label="Open Extra Large Modal" variant="primary" (click)="isExtraLargeLightOpen = true"></awe-button>`,

      'modal themes': `<awe-modal [isOpen]="isSmallDarkOpen" theme="dark" size="small" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isSmallDarkOpen = false">
  <div header><awe-heading variant="h6" type="regular">Subtitle goes here</awe-heading></div>
  <div content>
    <awe-body-text type="body-test">AI is here to help you achieve more in less <br> time! With our AI-powered tools,</awe-body-text>
  </div>
  <div footer>
    <awe-button label="Label" variant="primary" (click)="isSmallDarkOpen = false"></awe-button>
    <awe-button label="Label" variant="secondary" (click)="isSmallDarkOpen = false"></awe-button>
  </div>
</awe-modal>
 <awe-button label="Open Small Modal" variant="primary" (click)="isSmallDarkOpen = true"></awe-button>

<awe-modal [isOpen]="isMediumDarkOpen" theme="dark" size="medium" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isMediumDarkOpen = false">
  <div header><awe-heading variant="h5" type="regular">Discover Our New AI-Powered Tool</awe-heading></div>
  <div content>
    <awe-body-text type="body-test">
      <ng-container>
        "We’re excited to introduce a new feature that<br> simplifies your workflow! With AI, you can now:
        <ul>
          <li>Generate content in seconds.</li>
          <li>Analyze data effortlessly.</li>
          <li>Get personalized recommendations tailored to your needs."</li>
        </ul>
      </ng-container>
    </awe-body-text>
  </div>
  <div footer class="footerbutton">
    <awe-button label="Label" variant="secondary" (click)="isMediumDarkOpen = false"></awe-button>
    <awe-button label="Label" variant="primary" (click)="isMediumDarkOpen = false"></awe-button>
  </div>
</awe-modal>
<awe-button label="Open Medium Modal" variant="primary" (click)="isMediumDarkOpen = true"></awe-button>

<awe-modal [isOpen]="isLargeDarkOpen" theme="dark" size="large" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isLargeDarkOpen = false">
  <div content>
    <img class="image" src="assets/modal/image.png" alt="Modal component preview with UI elements">
    <awe-heading variant="h4" type="regular">Experience the future of analytics with our AI-<br>powered dashboard.</awe-heading>
    <awe-body-text type="body-test">
      <ng-container>
        <ul>
          <li>Real-time data visualization.</li>
          <li>Customizable widgets for tailored insights.</li>
          <li>AI-driven recommendations to optimize performance.</li>
        </ul>
      </ng-container>
    </awe-body-text>
  </div>
  <div footer class="footerbutton">
    <awe-button label="Label" variant="secondary" (click)="isLargeDarkOpen = false"></awe-button>
    <awe-button label="Label" variant="primary" (click)="isLargeDarkOpen = false"></awe-button>
  </div>
</awe-modal>
   <awe-button label="Open Large Modal" variant="primary" (click)="isLargeDarkOpen = true"></awe-button>

<awe-modal [isOpen]="isExtraLargeDarkOpen" theme="dark" size="extra-large" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isExtraLargeDarkOpen = false">
  <div header>
    <awe-heading variant="h2" type="regular">From validation</awe-heading>
  </div>
  <div content>
    <awe-input label="Username" placeholder="Enter your username"></awe-input>
    <awe-input label="name" placeholder="Enter your name"></awe-input>
    <awe-input label="email" placeholder="Enter your email"></awe-input>
  </div>
  <div footer>
    <awe-button label="Close" variant="secondary" (click)="isExtraLargeDarkOpen = false"></awe-button>
     <awe-button label="Open" variant="primary" (click)="isExtraLargeDarkOpen = false"></awe-button>
  </div>
</awe-modal>
<awe-button label="Open Extra Large Modal" variant="primary" (click)="isExtraLargeDarkOpen = true"></awe-button>`,

      'modal animations': `<awe-modal [isOpen]="isTopLeftModalOpen" size="medium" position="top-left" [animation]="true" animationType="zoomIn" [hasHeader]="true" [hasFooter]="true" (close)="isTopLeftModalOpen = false">
  <div header>
    <awe-heading variant="h2" type="regular">Be always up to date</awe-heading>
  </div>
  <div content>
    <awe-body-text type="body-test">
      Do you want to receive the push notification about the newest posts?
    </awe-body-text>
  </div>
  <div footer>
    <awe-button label="No" variant="secondary" (click)="isTopLeftModalOpen = false"></awe-button>
    <awe-button label="Yes" variant="primary" (click)="isTopLeftModalOpen = false"></awe-button>
  </div>
</awe-modal>
 <awe-button label="Open Top Left Modal" variant="primary" (click)="isTopLeftModalOpen = true"></awe-button>

<awe-modal [isOpen]="isTopRightModalOpen" size="medium" position="top-right" [animation]="true" animationType="zoomIn" [hasHeader]="true" [hasFooter]="true" (close)="isTopRightModalOpen = false">
  <div header>
    <awe-heading variant="h2" type="regular">Product in the cart</awe-heading>
  </div>
  <div content>
    <awe-body-text type="body-test">
      Do you need more time to make a purchase decision?
      No pressure, your product will be waiting for you in the cart.
    </awe-body-text>
  </div>
  <div footer>
    <awe-button label="GO TO THE CART" variant="primary" (click)="isTopRightModalOpen = false"></awe-button>
    <awe-button label="Close" variant="secondary" (click)="isTopRightModalOpen = false"></awe-button>
  </div>
</awe-modal>
  <awe-button label="Open Top Right Modal" variant="primary" (click)="isTopRightModalOpen = true"></awe-button>

<awe-modal [isOpen]="isBottomLeftModalOpen" size="medium" position="bottom-left" [animation]="true" animationType="zoomIn" [hasHeader]="true" [hasFooter]="true" (close)="isBottomLeftModalOpen = false">
  <div content>
    <awe-input label="name" placeholder="Enter your name"></awe-input>
    <awe-input label="email" placeholder="Enter your email"></awe-input>
  </div>
  <div footer>
    <awe-checkbox label="I have read and agree to the terms" size="small"></awe-checkbox>
  </div>
</awe-modal>
  <awe-button label="Open Bottom Left Modal" variant="primary" (click)="isBottomLeftModalOpen = true"></awe-button>

<awe-modal [isOpen]="isBottomRightModalOpen" size="medium" position="bottom-right" [animation]="true" animationType="zoomIn" [hasHeader]="true" [hasFooter]="true" (close)="isBottomRightModalOpen = false">
  <div header>
    <awe-heading variant="h3" type="regular">Related article</awe-heading>
  </div>
  <div content>
    Lorem ipsum dolor sit amet, consectetur adipisicing elit. Quod itaque voluptate nesciunt laborum incidunt. Officia, quam consectetur. Earum eligendi aliquam illum alias.
  </div>
  <div footer>
  <awe-button label="Close" variant="secondary" (click)="isBottomRightModalOpen = false"></awe-button>
    <awe-button label="READ MORE" variant="primary" (click)="isBottomRightModalOpen = false"></awe-button>
  </div>
</awe-modal>
   <awe-button label="Open Bottom Right Modal" variant="primary" (click)="isBottomRightModalOpen = true"></awe-button>`,

      'fullscreen modal': `<awe-modal [isOpen]="isFullscreenModalOpen" size="fullscreen" position="center" [hasHeader]="true" [hasFooter]="true" (close)="isFullscreenModalOpen = false">
  <div header>
    <awe-heading variant="h1" type="regular">Checkout</awe-heading>
  </div>
  <div content>
    <awe-input label="First name" placeholder="Enter your first name"></awe-input>
    <awe-input label="Last name" placeholder="Enter your last name"></awe-input>
    <awe-input label="Company name" placeholder="Enter your company name"></awe-input>
    <awe-input label="email" placeholder="Enter your email"></awe-input>
    <awe-input label="phone" placeholder="Enter your phone"></awe-input>
    <awe-checkbox label="Create an account?" size="small"></awe-checkbox>
  </div>
  <div footer>
    <awe-button label="Close" variant="secondary" (click)="isFullscreenModalOpen = false"></awe-button>
    <awe-button label="Open" variant="primary" (click)="isFullscreenModalOpen = false"></awe-button>
  </div>
</awe-modal>
 <awe-button label="Open Fullscreen Modal" variant="primary" (click)="isFullscreenModalOpen = true"></awe-button>`,

      'frame modal': `<awe-modal [isOpen]="isTopFrameModalOpen" size="medium" position="top" class="frame-modal" [hasHeader]="true" [hasFooter]="true" (close)="isTopFrameModalOpen = false">
  <div content>We have a gift for you! Use this code to get a 10% discount.</div>
  <div footer>
    <awe-button label="NO,THANKS" variant="secondary" (click)="isTopFrameModalOpen = false"></awe-button>
     <awe-button label="USE IT" variant="primary" (click)="isTopFrameModalOpen = false"></awe-button>
  </div>
</awe-modal>
 <awe-button label="Open Top Frame Modal" variant="primary" (click)="isTopFrameModalOpen = true"></awe-button>

<awe-modal [isOpen]="isBottomFrameModalOpen" size="medium" position="bottom" class="frame-modal" [hasHeader]="true" [hasFooter]="true" (close)="isBottomFrameModalOpen = false">
  <div content>We use cookies to improve your website experience</div>
  <div footer>
    <awe-button label="OK,THANKS" variant="secondary" (click)="isBottomFrameModalOpen = false"></awe-button>
    <awe-button label="LEARN MORE" variant="primary" (click)="isBottomFrameModalOpen = false"></awe-button>
  </div>
</awe-modal>
 <awe-button label="Open Bottom Frame Modal" variant="primary" (click)="isBottomFrameModalOpen = true"></awe-button>` ,
 
 'experience studio': ` <awe-modal [isOpen]="isExperienceStudioModalOpen" size="custom-variant" position="center" [hasHeader]="true" [hasFooter]="true" theme="light" (close)="isExperienceStudioModalOpen = false">
              <div header>
                <div class="modal-header">
                  <awe-heading variant="h3" type="bold">Export Project</awe-heading>
                  <awe-icons iconName="awe_close" (click)="isExperienceStudioModalOpen = false"></awe-icons>
                </div>
              
                <awe-heading variant="h6" type="regular">Project Name</awe-heading>
              
                <div class="input-button-wrapper">
                  <input class="input" type="text" placeholder="Enter your project name">
                 <awe-button
                    label="Save"
                    variant="primary"
                    class="save-button"
                    width="80px"
                    height="48px"
                    gradient="linear-gradient(45deg,#6566CD,#F96CAB)"
                    hoverEffect="slide-bg">
                  </awe-button>
               </div>
              </div>
              
              <div content class="button-pill-grid">
                <div class="pill-row">
                  <div class="button-pill-item">
                  <awe-button variant="secondary" iconName="awe_exp_polygon" state="active" iconColor="pink" iconPosition="only" pill="true"></awe-button>
                    <span class="button-label">Connect to Database</span>
                  </div>
                  <div class="button-pill-item">
                    <awe-button variant="secondary" iconName="awe_exp_squareicon" state="active" iconColor="pink" iconPosition="only" pill="true"></awe-button>
                    <span class="button-label">Unit Test Cases</span>
                  </div>
                  <div class="button-pill-item">
                    <awe-button variant="secondary" iconName="awe_exp_dimondicon" state="active" iconColor="pink" iconPosition="only" pill="true"></awe-button>
                    <span class="button-label">Generate API</span>
                  </div>
                </div>
              
                <div class="divider-line"></div> <!-- horizontal line here -->
              
                <div class="pill-row">
                  <div class="button-pill-item">
                    <awe-button variant="secondary" state="active" iconName="awe_download" iconPosition="only" pill="true"></awe-button>
                    <span class="button-label">Download</span>
                  </div>
                  <div class="button-pill-item">
                    <awe-button variant="secondary" state="active" iconName="awe_vscode" iconColor="vscodeblue" iconPosition="only" pill="true"></awe-button>
                    <span class="button-label">VSCode</span>
                  </div>
                  <div class="button-pill-item">
                    <awe-button variant="secondary" state="active" iconName="awe_github"  iconPosition="only" pill="true"></awe-button>
                    <span class="button-label">GitHub</span>
                  </div>
                </div>
              </div>
              
            </awe-modal>
            <awe-button label="Export" variant="primary" (click)="isExperienceStudioModalOpen = true"></awe-button>`
    };
    return examples[section] || '';
  }

  // Copy Code to Clipboard
  copyCode(section: string): void {
    const code = this.getExampleCode(section);
    navigator.clipboard.writeText(code).then(() => {
      console.log('Code copied to clipboard');
    }).catch(err => {
      console.error('Failed to copy code:', err);
    });
  }
}
