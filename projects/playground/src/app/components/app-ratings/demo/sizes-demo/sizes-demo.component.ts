import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RatingComponent } from '../../../../../../../play-comp-library/src/lib/components/rating/rating.component';

@Component({
  selector: 'ava-rating-sizes-demo',
  standalone: true,
  imports: [CommonModule, RatingComponent],
  template: `
    <div class="demo-container">
      <div class="sizes-grid">
        <div class="size-demo">
          <h4 style="color: var(--color-text-primary)">XSmall</h4>
          <ava-rating
            [value]="ratingValue"
            size="xsmall"
            (rated)="onRatingChange($event)"
          ></ava-rating>
        </div>

        <div class="size-demo">
          <h4 style="color: var(--color-text-primary)">Small</h4>
          <ava-rating
            [value]="ratingValue"
            size="small"
            (rated)="onRatingChange($event)"
          ></ava-rating>
        </div>

        <div class="size-demo">
          <h4 style="color: var(--color-text-primary)">Medium - Default</h4>
          <ava-rating
            [value]="ratingValue"
            size="medium"
            (rated)="onRatingChange($event)"
          ></ava-rating>
        </div>

        <div class="size-demo">
          <h4 style="color: var(--color-text-primary)">Large</h4>
          <ava-rating
            [value]="ratingValue"
            size="large"
            (rated)="onRatingChange($event)"
          ></ava-rating>
        </div>

        <div class="size-demo">
          <h4 style="color: var(--color-text-primary)">Custom</h4>
          <ava-rating
            [value]="ratingValue"
            [size]="40"
            (rated)="onRatingChange($event)"
          ></ava-rating>
        </div>
      </div>
      <p style="color: var(--color-text-primary)">
        Current Rating: {{ ratingValue }}
      </p>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        margin-top: 3rem;
        text-align: center;
      }

      .demo-description {
        margin-bottom: 2rem;
      }

      .demo-description h3 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 24px;
      }

      .demo-description p {
        color: #666;
        font-size: 16px;
        line-height: 1.5;
      }

      .sizes-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
      }

      .size-demo {
        text-align: center;
      }

      .size-demo h4 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 16px;
        font-weight: 500;
      }

      .rating-controls {
        text-align: center;
        padding: 1.5rem;
        background-color: #e9ecef;
        border-radius: 8px;
      }

      .rating-controls p {
        font-size: 18px;
        color: #333;
        margin-bottom: 1rem;
        font-weight: 500;
      }

      .reset-btn {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
      }

      .reset-btn:hover {
        background-color: #0056b3;
      }
    `,
  ],
})
export class SizesDemoComponent {
  ratingValue = 3.5;

  onRatingChange(value: number) {
    this.ratingValue = value;
    console.log('Rating changed to:', value);
  }

  resetRating() {
    this.ratingValue = 3.5;
  }
}
