import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RatingComponent } from '../../../../../../../play-comp-library/src/lib/components/rating/rating.component';

@Component({
  selector: 'ava-rating-readonly-demo',
  standalone: true,
  imports: [CommonModule, RatingComponent],
  template: `
    <div class="demo-container">
      <div class="demo-description">
        <h3>Readonly Mode</h3>
        <p>
          Display-only mode for showing existing ratings without user
          interaction.
        </p>
      </div>

      <div class="readonly-demo">
        <div class="demo-section">
          <h4>Product Reviews</h4>
          <div class="review-item">
            <div class="product-info">
              <h5>Premium Wireless Headphones</h5>
              <ava-rating [value]="4.5" [readonly]="true"></ava-rating>
              <span class="rating-value">4.5 out of 5</span>
            </div>
            <p class="review-text">
              "Excellent sound quality and comfortable fit. Highly recommended!"
            </p>
          </div>

          <div class="review-item">
            <div class="product-info">
              <h5>Smart Fitness Watch</h5>
              <ava-rating [value]="3.0" [readonly]="true"></ava-rating>
              <span class="rating-value">3.0 out of 5</span>
            </div>
            <p class="review-text">
              "Good features but battery life could be better."
            </p>
          </div>

          <div class="review-item">
            <div class="product-info">
              <h5>Organic Coffee Beans</h5>
              <ava-rating [value]="5.0" [readonly]="true"></ava-rating>
              <span class="rating-value">5.0 out of 5</span>
            </div>
            <p class="review-text">
              "Amazing flavor and quality. Will definitely order again!"
            </p>
          </div>
        </div>

        <div class="demo-section">
          <h4>Rating Display Examples</h4>
          <div class="rating-examples">
            <div class="example-item">
              <span class="example-label">Perfect Rating:</span>
              <ava-rating [value]="5.0" [readonly]="true"></ava-rating>
            </div>
            <div class="example-item">
              <span class="example-label">Good Rating:</span>
              <ava-rating [value]="4.0" [readonly]="true"></ava-rating>
            </div>
            <div class="example-item">
              <span class="example-label">Average Rating:</span>
              <ava-rating [value]="3.0" [readonly]="true"></ava-rating>
            </div>
            <div class="example-item">
              <span class="example-label">Poor Rating:</span>
              <ava-rating [value]="1.5" [readonly]="true"></ava-rating>
            </div>
          </div>
        </div>
      </div>

      <div class="info-section">
        <h4>Readonly Features</h4>
        <ul class="feature-list">
          <li>Non-interactive - No click or hover effects</li>
          <li>Display Only - Perfect for showing existing ratings</li>
          <li>Accessibility - Maintains proper ARIA attributes</li>
          <li>
            Consistent Styling - Same visual appearance as interactive mode
          </li>
        </ul>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        margin-top: 3rem;
        text-align: center;
      }

      .demo-description {
        margin-bottom: 2rem;
      }

      .demo-description h3 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 24px;
      }

      .demo-description p {
        color: #666;
        font-size: 16px;
        line-height: 1.5;
      }

      .readonly-demo {
        margin-bottom: 2rem;
      }

      .demo-section {
        padding: 2rem;
        background-color: #f8f9fa;
        border-radius: 8px;
        margin-bottom: 2rem;
        text-align: left;
      }

      .demo-section h4 {
        color: #333;
        margin-bottom: 1.5rem;
        font-size: 20px;
        font-weight: 500;
        text-align: center;
      }

      .review-item {
        padding: 1.5rem;
        background-color: white;
        border-radius: 6px;
        margin-bottom: 1rem;
        border: 1px solid #dee2e6;
      }

      .product-info {
        display: flex;
        align-items: center;
        gap: 1rem;
        margin-bottom: 1rem;
        flex-wrap: wrap;
      }

      .product-info h5 {
        margin: 0;
        color: #333;
        font-size: 16px;
        font-weight: 500;
        min-width: 200px;
      }

      .rating-value {
        color: #666;
        font-size: 14px;
        font-weight: 500;
      }

      .review-text {
        color: #666;
        font-size: 14px;
        margin: 0;
        font-style: italic;
      }

      .rating-examples {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1.5rem;
      }

      .example-item {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background-color: white;
        border-radius: 6px;
        border: 1px solid #dee2e6;
      }

      .example-label {
        font-size: 14px;
        color: #666;
        font-weight: 500;
        min-width: 120px;
      }

      .info-section {
        padding: 1.5rem;
        background-color: #e3f2fd;
        border-radius: 8px;
        border-left: 4px solid #2196f3;
        text-align: left;
      }

      .info-section h4 {
        color: #333;
        margin-bottom: 1rem;
        font-size: 18px;
        font-weight: 500;
      }

      .feature-list {
        margin: 0;
        padding-left: 1.5rem;
        color: #666;
        line-height: 1.6;
      }

      .feature-list li {
        margin-bottom: 0.5rem;
      }
    `,
  ],
})
export class ReadonlyDemoComponent {
  constructor() {
    console.log('Readonly Demo Component loaded!');
  }
}
