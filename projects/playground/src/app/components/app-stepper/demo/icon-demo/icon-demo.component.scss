.center-demo {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 100vh;
  padding: 40px 20px;
}

.demo-section {
  max-width: 1000px;
  width: 100%;
  margin: 0 auto;

  .page-title {
    text-align: center;
    margin-bottom: 16px;
    color: #1e293b;
    font-size: 32px;
    font-weight: 700;
  }

  .page-description {
    text-align: center;
    margin-bottom: 48px;
    color: #64748b;
    font-size: 18px;
    line-height: 1.6;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
  }

  .demo-item {
    border-radius: 12px;
    padding: 32px;
    margin-bottom: 32px;

    h3 {
      margin-bottom: 8px;
      color: #1e293b;
      font-size: 24px;
      font-weight: 600;
    }

    p {
      margin-bottom: 24px;
      color: #64748b;
      font-size: 16px;
      line-height: 1.5;
    }

    &.vertical-demo {
      .vertical-stepper-container {
        display: flex;
        justify-content: center;
        margin-bottom: 24px;
        min-height: 300px;
        align-items: flex-start;
        padding-top: 20px;
      }
    }

    .controls {
      display: flex;
      justify-content: center;
      gap: 16px;
      margin-top: 24px;

      button {
        padding: 10px 20px;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
        background: white;
        color: #374151;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover:not(:disabled) {
          background: #f8fafc;
          border-color: #cbd5e1;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        &.prev-btn {
          &:hover:not(:disabled) {
            background: #fef2f2;
            border-color: #fca5a5;
            color: #dc2626;
          }
        }

        &.next-btn {
          background: #3b82f6;
          color: white;
          border-color: #3b82f6;

          &:hover:not(:disabled) {
            background: #2563eb;
            border-color: #2563eb;
          }

          &:disabled {
            background: #9ca3af;
            border-color: #9ca3af;
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .center-demo {
    padding: 20px 16px;
  }

  .demo-section {
    .page-title {
      font-size: 28px;
    }

    .page-description {
      font-size: 16px;
      margin-bottom: 32px;
    }

    .demo-item {
      padding: 24px 20px;
      margin-bottom: 24px;

      h3 {
        font-size: 20px;
      }

      &.vertical-demo {
        .vertical-stepper-container {
          min-height: 250px;
        }
      }

      .controls {
        flex-direction: column;
        align-items: center;

        button {
          width: 100%;
          max-width: 200px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .demo-section {
    .demo-item {
      padding: 20px 16px;

      &.vertical-demo {
        .vertical-stepper-container {
          min-height: 200px;
        }
      }
    }
  }
} 