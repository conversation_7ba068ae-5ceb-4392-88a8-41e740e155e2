<div class="responsive-demo">
  <div class="demo-header">
    <a routerLink="/app-breadcrumbs" class="back-button">
      <ava-icon iconName="arrow-left" [iconSize]="16"></ava-icon>
      <span>Back to Breadcrumbs</span>
    </a>
    <h1>Responsive Design</h1>
    <p>
      Breadcrumbs that adapt to different screen sizes and contexts for optimal
      user experience.
    </p>
  </div>

  <div class="demo-content">
    <ava-breadcrumbs-demo-card
      title="Mobile Optimized"
      description="Compact breadcrumbs with shorter labels optimized for mobile screens."
    >
      <ava-breadcrumbs [breadcrumbs]="mobileBreadcrumbs" sizeClass="small">
      </ava-breadcrumbs>
      <p class="demo-note">Optimized for screens under 768px width.</p>
    </ava-breadcrumbs-demo-card>

    <ava-breadcrumbs-demo-card
      title="Tablet Layout"
      description="Balanced breadcrumbs with medium labels for tablet interfaces."
    >
      <ava-breadcrumbs [breadcrumbs]="tabletBreadcrumbs" sizeClass="medium">
      </ava-breadcrumbs>
      <p class="demo-note">Optimized for tablet screens (768px - 1024px).</p>
    </ava-breadcrumbs-demo-card>

    <ava-breadcrumbs-demo-card
      title="Desktop Full Width"
      description="Full-featured breadcrumbs with descriptive labels for desktop screens."
    >
      <ava-breadcrumbs
        [breadcrumbs]="desktopBreadcrumbs"
        sizeClass="large"
        [collapsible]="false"
      >
      </ava-breadcrumbs>
      <p class="demo-note">Full display for desktop screens (1024px+).</p>
    </ava-breadcrumbs-demo-card>

    <ava-breadcrumbs-demo-card
      title="Responsive with Collapsible"
      description="Adaptive breadcrumbs that collapse based on available space and content length."
    >
      <ava-breadcrumbs
        [breadcrumbs]="responsiveCollapsibleBreadcrumbs"
        [collapsible]="true"
        [maxVisibleItems]="4"
      >
      </ava-breadcrumbs>
      <p class="demo-note">
        Try resizing your browser to see adaptive behavior.
      </p>
    </ava-breadcrumbs-demo-card>
  </div>
</div>
