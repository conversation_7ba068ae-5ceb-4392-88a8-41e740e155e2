<div class="breadcrumbs-demo">
  <div class="demo-header">
    <h1>Breadcrumbs Component</h1>
    <p class="description">
      A comprehensive navigation component that displays hierarchical navigation
      paths with support for icons, collapsible behavior, multiple sizes, and
      responsive design for seamless user navigation.
    </p>
  </div>

  <div class="demo-content">
    <div class="demo-grid">
      <a routerLink="/app-breadcrumbs/basic-usage" class="demo-card">
        <div class="card-icon">🏠</div>
        <div class="card-content">
          <h3>Basic Usage</h3>
          <p>
            Simple breadcrumb navigation with text labels for hierarchical
            navigation paths.
          </p>
        </div>
      </a>

      <a routerLink="/app-breadcrumbs/with-icons" class="demo-card">
        <div class="card-icon">🎨</div>
        <div class="card-content">
          <h3>With Icons</h3>
          <p>
            Breadcrumbs with icons for enhanced visual representation and better
            user experience.
          </p>
        </div>
      </a>

      <a routerLink="/app-breadcrumbs/sizes" class="demo-card">
        <div class="card-icon">📏</div>
        <div class="card-content">
          <h3>Size Variants</h3>
          <p>
            Different size options for various design contexts and user
            interface requirements.
          </p>
        </div>
      </a>

      <a routerLink="/app-breadcrumbs/collapsible" class="demo-card">
        <div class="card-icon">📦</div>
        <div class="card-content">
          <h3>Collapsible Behavior</h3>
          <p>
            Smart collapsing for long navigation paths with ellipsis expansion
            for better user experience.
          </p>
        </div>
      </a>

      <a routerLink="/app-breadcrumbs/custom-separator" class="demo-card">
        <div class="card-icon">🔗</div>
        <div class="card-content">
          <h3>Custom Separators</h3>
          <p>
            Customizable separator icons and sizes for different design contexts
            and visual preferences.
          </p>
        </div>
      </a>

      <a routerLink="/app-breadcrumbs/responsive" class="demo-card">
        <div class="card-icon">📱</div>
        <div class="card-content">
          <h3>Responsive Design</h3>
          <p>
            Breadcrumbs that adapt to different screen sizes and contexts for
            optimal user experience.
          </p>
        </div>
      </a>
    </div>
  </div>
</div>
