<div class="dashboard-widget-grid-demo">
  <div class="demo-header">
    <h2>Dashboard Widget Grid Component</h2>
    <p>
      This component demonstrates a flexible dashboard layout with resizable and
      draggable widgets.
    </p>
  </div>

  <div class="demo-controls-card">
    <ava-card>
      <div header>
        <h3>Demo Controls</h3>
      </div>
      <div content>
        <div class="controls-grid">
          <ava-button
            (click)="toggleLoading()"
            [variant]="loading ? 'warning' : 'primary'"
            size="medium"
            [label]="loading ? 'Stop Loading' : 'Start Loading'"
            [iconName]="loading ? 'pause' : 'play'"
            [iconSize]="16"
          >
          </ava-button>
          <ava-button
            (click)="toggleDisabled()"
            [variant]="disabled ? 'success' : 'secondary'"
            size="medium"
            [label]="disabled ? 'Enable' : 'Disable'"
            [iconName]="disabled ? 'unlock' : 'lock'"
            [iconSize]="16"
          >
          </ava-button>
          <ava-button
            (click)="resetToDefault()"
            variant="info"
            size="medium"
            label="Reset to Default"
            iconName="refresh-cw"
            [iconSize]="16"
          >
          </ava-button>
        </div>
      </div>
    </ava-card>
  </div>

  <div class="demo-section">
    <ava-card>
      <div header>
        <h3>Interactive Dashboard</h3>
        <p>
          Try editing the dashboard by clicking "Edit Dashboard" and adding
          widgets
        </p>
      </div>
      <div content>
        <ava-dashboard-widget-grid
          [config]="dashboardConfig"
          [widgets]="sampleWidgets"
          [loading]="loading"
          [disabled]="disabled"
          (widgetAdd)="onWidgetAdd($event)"
          (widgetRemove)="onWidgetRemove($event)"
          (widgetMove)="onWidgetMove($event)"
          (widgetResize)="onWidgetResize($event)"
          (widgetEdit)="onWidgetEdit($event)"
          (widgetRefresh)="onWidgetRefresh($event)"
          (layoutSave)="onLayoutSave($event)"
          (layoutReset)="onLayoutReset()"
          (dashboardEvent)="onDashboardEvent($event)"
        >
        </ava-dashboard-widget-grid>
      </div>
    </ava-card>
  </div>

  <div class="event-log-card" *ngIf="lastEvent">
    <ava-card>
      <div header>
        <h3>Event Log</h3>
        <p>Real-time events from the dashboard</p>
      </div>
      <div content>
        <div class="event-content">
          <pre>{{ lastEvent | json }}</pre>
        </div>
      </div>
    </ava-card>
  </div>

  <div class="features-card">
    <ava-card>
      <div header>
        <h3>Features Demonstrated</h3>
        <p>Comprehensive list of capabilities</p>
      </div>
      <div content>
        <div class="features-grid">
          <div class="feature-item">
            <ava-icon
              iconName="grid"
              [iconSize]="20"
              iconColor="#007bff"
            ></ava-icon>
            <div class="feature-content">
              <strong>Grid Layout:</strong> CSS Grid-based responsive layout
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="move"
              [iconSize]="20"
              iconColor="#28a745"
            ></ava-icon>
            <div class="feature-content">
              <strong>Drag & Drop:</strong> Reposition widgets by dragging
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="maximize-2"
              [iconSize]="20"
              iconColor="#ffc107"
            ></ava-icon>
            <div class="feature-content">
              <strong>Resizable Widgets:</strong> Adjust widget dimensions
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="plus"
              [iconSize]="20"
              iconColor="#dc3545"
            ></ava-icon>
            <div class="feature-content">
              <strong>Add Widgets:</strong> Multiple widget types available
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="trash"
              [iconSize]="20"
              iconColor="#6c757d"
            ></ava-icon>
            <div class="feature-content">
              <strong>Remove Widgets:</strong> Delete unwanted widgets
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="edit"
              [iconSize]="20"
              iconColor="#17a2b8"
            ></ava-icon>
            <div class="feature-content">
              <strong>Edit Mode:</strong> Toggle between view and edit modes
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="save"
              [iconSize]="20"
              iconColor="#fd7e14"
            ></ava-icon>
            <div class="feature-content">
              <strong>Save Layout:</strong> Persist dashboard configuration
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="refresh-cw"
              [iconSize]="20"
              iconColor="#6f42c1"
            ></ava-icon>
            <div class="feature-content">
              <strong>Refresh Widgets:</strong> Update individual widget data
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="trending-up"
              [iconSize]="20"
              iconColor="#e83e8c"
            ></ava-icon>
            <div class="feature-content">
              <strong>Metric Widgets:</strong> Display key performance
              indicators
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="smartphone"
              [iconSize]="20"
              iconColor="#20c997"
            ></ava-icon>
            <div class="feature-content">
              <strong>Responsive Design:</strong> Adapts to different screen
              sizes
            </div>
          </div>
        </div>
      </div>
    </ava-card>
  </div>
</div>
