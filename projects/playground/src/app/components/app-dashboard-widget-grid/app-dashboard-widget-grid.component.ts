import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  DashboardWidgetGridComponent,
  DashboardConfig,
  WidgetConfig,
  DashboardEvent,
} from '../../../../../play-comp-library/src/lib/composite-components/dashboard-widget-grid/dashboard-widget-grid.component';
import { CardComponent } from '../../../../../play-comp-library/src/lib/components/card/card.component';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';
import { IconComponent } from '../../../../../play-comp-library/src/lib/components/icon/icon.component';

@Component({
  selector: 'ava-dashboard-widget-grid-demo',
  standalone: true,
  imports: [
    CommonModule,
    DashboardWidgetGridComponent,
    CardComponent,
    ButtonComponent,
    IconComponent,
  ],
  templateUrl: './app-dashboard-widget-grid.component.html',
  styleUrl: './app-dashboard-widget-grid.component.scss',
})
export class AppDashboardWidgetGridComponent {
  dashboardConfig: DashboardConfig = {
    title: 'Analytics Dashboard',
    description: 'Monitor your key metrics and performance indicators',
    editable: true,
    resizable: true,
    draggable: true,
    maxColumns: 12,
    widgetSpacing: 16,
    showGrid: true,
    showWidgetControls: true,
    allowWidgetTypes: ['metric', 'chart', 'table', 'list', 'custom'],
    defaultWidgetSize: { width: 4, height: 3 },
  };

  sampleWidgets: WidgetConfig[] = [
    {
      id: 'total-users',
      title: 'Total Users',
      type: 'metric',
      size: 'medium',
      position: { x: 0, y: 0 },
      dimensions: { width: 3, height: 2 },
      content: {
        value: '12,847',
        label: 'Active Users',
        change: '+12.5%',
        trend: 'positive',
      },
    },
    {
      id: 'revenue',
      title: 'Monthly Revenue',
      type: 'metric',
      size: 'medium',
      position: { x: 3, y: 0 },
      dimensions: { width: 3, height: 2 },
      content: {
        value: '$45,231',
        label: 'This Month',
        change: '+8.2%',
        trend: 'positive',
      },
    },
    {
      id: 'conversion-rate',
      title: 'Conversion Rate',
      type: 'metric',
      size: 'medium',
      position: { x: 6, y: 0 },
      dimensions: { width: 3, height: 2 },
      content: {
        value: '3.24%',
        label: 'Overall Rate',
        change: '-2.1%',
        trend: 'negative',
      },
    },
    {
      id: 'sales-chart',
      title: 'Sales Overview',
      type: 'chart',
      size: 'large',
      position: { x: 0, y: 2 },
      dimensions: { width: 6, height: 4 },
    },
    {
      id: 'recent-orders',
      title: 'Recent Orders',
      type: 'table',
      size: 'medium',
      position: { x: 6, y: 2 },
      dimensions: { width: 6, height: 4 },
    },
    {
      id: 'top-products',
      title: 'Top Products',
      type: 'list',
      size: 'small',
      position: { x: 9, y: 0 },
      dimensions: { width: 3, height: 2 },
    },
  ];

  loading = false;
  disabled = false;
  lastEvent: DashboardEvent | null = null;

  onWidgetAdd(event: { type: string; position: { x: number; y: number } }) {
    console.log('Widget add:', event);
    const newWidget: WidgetConfig = {
      id: `widget-${Date.now()}`,
      title: `${
        event.type.charAt(0).toUpperCase() + event.type.slice(1)
      } Widget`,
      type: event.type as 'metric' | 'chart' | 'table' | 'list' | 'custom',
      size: 'medium',
      position: event.position,
      dimensions: this.dashboardConfig.defaultWidgetSize || {
        width: 4,
        height: 3,
      },
    };
    this.sampleWidgets = [...this.sampleWidgets, newWidget];
  }

  onWidgetRemove(widgetId: string) {
    console.log('Widget remove:', widgetId);
    this.sampleWidgets = this.sampleWidgets.filter((w) => w.id !== widgetId);
  }

  onWidgetMove(event: {
    widgetId: string;
    position: { x: number; y: number };
  }) {
    console.log('Widget move:', event);
    this.sampleWidgets = this.sampleWidgets.map((w) =>
      w.id === event.widgetId ? { ...w, position: event.position } : w
    );
  }

  onWidgetResize(event: {
    widgetId: string;
    dimensions: { width: number; height: number };
  }) {
    console.log('Widget resize:', event);
    this.sampleWidgets = this.sampleWidgets.map((w) =>
      w.id === event.widgetId ? { ...w, dimensions: event.dimensions } : w
    );
  }

  onWidgetEdit(widgetId: string) {
    console.log('Widget edit:', widgetId);
    alert(`Edit widget: ${widgetId}`);
  }

  onWidgetRefresh(widgetId: string) {
    console.log('Widget refresh:', widgetId);
    // Simulate loading
    this.sampleWidgets = this.sampleWidgets.map((w) =>
      w.id === widgetId ? { ...w, loading: true } : w
    );

    setTimeout(() => {
      this.sampleWidgets = this.sampleWidgets.map((w) =>
        w.id === widgetId ? { ...w, loading: false } : w
      );
    }, 2000);
  }

  onLayoutSave(widgets: WidgetConfig[]) {
    console.log('Layout save:', widgets);
    alert('Dashboard layout saved!');
  }

  onLayoutReset() {
    console.log('Layout reset');
    this.sampleWidgets = [];
  }

  onDashboardEvent(event: DashboardEvent) {
    this.lastEvent = event;
    console.log('Dashboard event:', event);
  }

  toggleLoading() {
    this.loading = !this.loading;
  }

  toggleDisabled() {
    this.disabled = !this.disabled;
  }

  resetToDefault() {
    this.sampleWidgets = [
      {
        id: 'total-users',
        title: 'Total Users',
        type: 'metric',
        size: 'medium',
        position: { x: 0, y: 0 },
        dimensions: { width: 3, height: 2 },
        content: {
          value: '12,847',
          label: 'Active Users',
          change: '+12.5%',
          trend: 'positive',
        },
      },
      {
        id: 'revenue',
        title: 'Monthly Revenue',
        type: 'metric',
        size: 'medium',
        position: { x: 3, y: 0 },
        dimensions: { width: 3, height: 2 },
        content: {
          value: '$45,231',
          label: 'This Month',
          change: '+8.2%',
          trend: 'positive',
        },
      },
    ];
  }
}
