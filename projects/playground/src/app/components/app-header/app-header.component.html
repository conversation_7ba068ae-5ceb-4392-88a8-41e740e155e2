<div class="documentation">
  <!-- Header -->
  <div class="row">
    <div class="col-12">
      <header class="doc-header">
        <h1>Header Component</h1>
        <p class="description">
          A versatile header component that supports various themes, tabs, and user interactions. Built with
          accessibility and user experience in mind.
        </p>
      </header>
    </div>
  </div>

  <!-- Installation -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Installation</h2>
        <div class="code-block">
          <pre><code>import {{ '{' }} HeaderComponent {{ '}' }} from '&#64;awe/play-comp-library';</code></pre>
        </div>
      </section>
    </div>
  </div>

  <!-- Documentation Sections -->
  <div class="doc-sections">
    <section *ngFor="let section of sections; let i = index" class="doc-section">
      <div class="row">
        <div class="col-12">
          <div class="section-header" tabindex="0" role="button">
            <h2>{{ section.title }}</h2>
            <div class="description-container">
              <p>{{ section.description }}</p>
              <div class="code-toggle" (click)="toggleCodeVisibility(i, $event)">
                <span *ngIf="!section.showCode">View Code</span>
                <span *ngIf="section.showCode">Hide Code</span>
                <awe-icons [iconName]="section.showCode ? 'awe_arrow_back_up' : 'awe_arrow_back_down'"
                  iconColor="action"></awe-icons>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Code Example -->
      <div class="code-example" [class.expanded]="section.showCode">
        <div class="example-preview">
          <ng-container [ngSwitch]="section.title">
            <!-- Light Theme Header -->
            <ng-container *ngSwitchCase="'Light Theme Header'">
              <awe-header theme="light" containerClass="mb-4 mt-4">
                <div left-content>
                  <h2>Ascendion</h2>
                </div>
                <div center-content>
                  <awe-tabs [tabs]="TwoTabs" [showContent]="false" theme="light"></awe-tabs>
                </div>
                <div right-content class="header-right-content">
                  <awe-icons [iconName]="'awe_save'"></awe-icons>
                  <awe-avatars iconName="awe_dashboard"></awe-avatars>
                  <awe-avatars iconName="awe_bell"></awe-avatars>
                  <awe-avatars iconName="awe_jurica.jpg" [badgeCount]="6" badgeColor="danger"></awe-avatars>
                </div>
              </awe-header>
            </ng-container>

            <!-- Dark Theme Header -->
            <ng-container *ngSwitchCase="'Dark Theme Header'">
              <awe-header theme="dark">
                <div center-content>
                  <awe-tabs [tabs]="TwoTabs" [showContent]="false" theme="dark"></awe-tabs>
                </div>
                <div right-content class="header-right-content">
                  <awe-icons [iconName]="'awe_save'" iconColor="whiteIcon"></awe-icons>
                  <awe-avatars iconName="awe_dashboard"></awe-avatars>
                  <awe-avatars iconName="awe_bell"></awe-avatars>
                  <awe-avatars iconName="awe_jurica.jpg" [badgeCount]="6" badgeColor="danger"></awe-avatars>
                </div>
              </awe-header>
            </ng-container>

            <!-- Transparent Theme Header -->
            <ng-container *ngSwitchCase="'Transparent Theme Header'">
              <awe-header theme="transparent" containerClass="mb-4 mt-4">
                <div left-content>
                  <img src="assets/logos/dlogo.svg" alt="Logo">
                </div>
                <div right-content class="header-right">
                  <awe-icons class="icon" [iconName]="'awe_graph'" iconColor="header"
                    (click)="onGraphIconClick()"></awe-icons>
                  <awe-icons class="icon" [iconName]="'awe_translate'" iconColor="header"
                    (click)="onTranslateIconClick()"></awe-icons>
                  <awe-icons class="icon" [iconName]="'awe_day_and_light_mode'" iconColor="header"
                    (click)="onDayLightModeIconClick()"></awe-icons>
                  <awe-avatars [iconName]="getLoggedInUserLogo()" [avatarSquare]="true" class="user-avatar"
                    (click)="toggleDropdown()"></awe-avatars>
                  <div class="dropdown" *ngIf="isDropdownOpen">
                    <div class="account" *ngFor="let account of getSortedAccounts(); let i = index"
                      (click)="login(account)" [ngClass]="{'not-logged-in': !account.isLoggedIn}">
                      <img [src]="account.userLogo" alt="User Logo" class="user-logo">
                      <div class="account-info">
                        <div class="company-info">
                          <img [src]="account.companyLogo" alt="Company Logo" class="company-logo">
                        </div>
                        <p>{{ account.email }}</p>
                        <button [class.logged-in]="account.isLoggedIn" [class.log-in]="!account.isLoggedIn">
                          {{ account.isLoggedIn ? 'Logged in' : 'Log in' }}
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </awe-header>
            </ng-container>

            <ng-container *ngSwitchCase="'Transparent Theme Header-2'">
              <awe-header theme="transparent" containerClass="mb-4 mt-4">
                <div left-content>
                  <img src="assets/logos/dlogo.svg" alt="Logo">
                </div>

                <div center-content class="tabs-header">
                  <awe-tabs [tabs]="iconsTabs" variant="equal-width" [showContent]="false" [useUrlNavigation]="true"
                    (tabChange)="onTabChange($event)" (urlChange)="onUrlChange($event)"></awe-tabs>
                </div>

                <div right-content class="header-right-content">
                  <awe-icons class="icon" [iconName]="'awe_graph'" iconColor="header"></awe-icons>
                  <awe-icons class="icon" [iconName]="'awe_day_and_light_mode'" iconColor="header"></awe-icons>
                  <awe-avatars [iconName]="'awe_jurica.jpg'" [avatarSquare]="true"></awe-avatars>
                </div>
              </awe-header>
            </ng-container>
          </ng-container>
        </div>

        <div class="code-block" *ngIf="section.showCode">
          <div class="code-content">
            <pre><code [innerText]="getExampleCode(section.title.toLowerCase())"></code></pre>
          </div>
          <button class="copy-button" (click)="copyCode(section.title.toLowerCase())">
            <awe-icons iconName="awe_copy"></awe-icons>
          </button>
        </div>
      </div>
    </section>
  </div>

  <!-- API Reference -->
  <section class="doc-section api-reference">
    <h2>API Reference</h2>
    <table class="api-table">
      <thead>
        <tr>
          <th>Property</th>
          <th>Type</th>
          <th>Default</th>
          <th>Description</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td><code>theme</code></td>
          <td><code>'light' | 'dark' | 'transparent'</code></td>
          <td><code>'light'</code></td>
          <td>Sets the theme of the header.</td>
        </tr>
        <tr>
          <td><code>containerClass</code></td>
          <td><code>string</code></td>
          <td><code>''</code></td>
          <td>Sets additional CSS classes for the header container.</td>
        </tr>
      </tbody>
    </table>
  </section>

  <!-- Events -->
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>Events</h2>
        <table class="api-table">
          <thead>
            <tr>
              <th>Event</th>
              <th>Type</th>
              <th>Description</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td><code>iconClick</code></td>
              <td><code>EventEmitter&lt;void&gt;</code></td>
              <td>Event emitted when an icon in the header is clicked.</td>
            </tr>
          </tbody>
        </table>
      </section>
    </div>
  </div>
</div>