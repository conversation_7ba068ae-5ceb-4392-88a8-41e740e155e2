/* Prevent horizontal scroll */
html,
body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
}
 
* {
  box-sizing: border-box;
}
 
/* Main layout */
.documentation {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  overflow-x: hidden;
 
  @media (max-width: 768px) {
    padding: 1rem;
  }
}
 
/* Header */
.doc-header {
  h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
  }
 
  .description {
    font-size: 1.1rem;
    line-height: 1.6;
  }
}
 
/* Sections */
.doc-sections {
  margin-top: 4rem;
}
 
.doc-section {
  margin-bottom: 1rem;
 
  h2 {
    font-size: 1.8rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
  }
 
  p {
    font-size: 0.95rem;
    line-height: 1.5;
  }
}
 
/* Section header with toggle */
.section-header {
  display: flex;
  flex-direction: column;
  position: relative;
  cursor: pointer;
  padding: 1rem;
  background-color: var(--surface);
  border-radius: var(--border-radius);
 
  h2 {
    margin-bottom: 0.5rem;
  }
 
  .description-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
  }
 
  .code-toggle {
    font-size: 0.75rem;
    color: var(--icons-action);
    cursor: pointer;
    display: flex;
    align-items: center;
    font-weight: var(--font-font-weight-medium);
    font-family: var(--font-font-family-heading);
 
    &:hover {
      text-decoration: underline;
    }
 
    span {
      margin-right: 0.5rem;
    }
 
    awe-icons {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      line-height: 0;
      padding: 0;
      margin: 0;
      vertical-align: middle;
      flex-shrink: 0;
 
      svg {
        width: 60%;
        height: 80%;
        display: block;
      }
    }
  }
}
 
/* Code example styles */
.code-example {
  margin-top: 1.5rem;
 
  .example-preview {
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    border: 1px solid var(--surface-border);
    padding-top: 40px;
  }
 
  .code-block {
    position: relative;
    border-radius: 0.5rem;
    margin-top: 1rem;
    border: 1px solid var(--surface-border);
    background-color: var(--surface-ground);
 
    pre {
      margin: 0;
      padding: 1rem;
      border-radius: 0.25rem;
      overflow-x: auto;
    }
 
    .copy-button {
      position: absolute;
      top: 0.5rem;
      right: 0.5rem;
      padding: 0.5rem;
      background: transparent;
      border: none;
      cursor: pointer;
      color: var(--text-color-secondary);
 
      &:hover {
        color: var(--primary-color);
      }
    }
  }
}
 
/* API table styles */
.api-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
 
  th,
  td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--surface-border);
  }
 
  th {
    background-color: var(--surface);
    font-weight: 600;
    color: var(--text-color-primary);
  }
 
  td {
    code {
      background-color: var(--surface);
      padding: 0.2rem 0.4rem;
      border-radius: var(--border-radius-sm);
      font-family: monospace;
    }
  }
}
 
/* Responsive Adjustments */
@media (max-width: 768px) {
  .documentation {
    padding: 1rem;
  }
}

.header-right-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-4x);
  }

  .header-right {
  display: flex;
  align-items: center;
  gap: 32px;
}

.header-right awe-icons svg {
  margin-top: 5px;
}

.icon {
  display: flex;
  width: 40px;
  height: 40px;
  padding: 10px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
}

.icon:hover {
  border: 1px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(to bottom, #706DCE, #F773AF) border-box;
  border-radius: 50%;
}

.dropdown {
  position: absolute;
  right: 0;
  top: 60px;
  background-color: white;
  border-radius: 16px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  width: 300px;

}

.account {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  background-color: var(--account-bg-color, #fff); /* Use the dynamic background color */
}

.account:first-child {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.account:last-child {
  border-bottom: none;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}


.not-logged-in {
  background-color: #EDEDF3; /* Specific background color for not logged in accounts */
}

.company-logo {
  width: 100px;
  height: 30px;
  margin-right: 12px;
}

.user-logo {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: 8px;
}

.account-info {
  flex: 1;
}

.account-info h3 {
  margin: 0;
  font-size: 14px;
}

.account-info p {
  margin: 9px 0;
  font-size: 14px;
  color: #666D99;
}

.account-info button {
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
}

.account-info button.logged-in {
  color: #A3A7C2;
  border-radius: 24px;
  border: 1px solid var(--Text-Disabled, #A3A7C2);
}

.account-info button.log-in {
  border-radius: 24px;
  background-color: white;
  color: black;
}

// for tabs
.header-right-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-4x);
  }

  .header-right {
  display: flex;
  align-items: center;
  gap: 32px;
}

.header-right awe-icons svg {
  margin-top: 10px;
}

awe-icons svg{
  margin-top: 5px;
}

.icon {
  display: flex;
  width: 40px;
  height: 40px;
  padding: 10px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
}

.icon:hover {
  border: 1px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(to bottom, #706DCE, #F773AF) border-box;
  border-radius: 50%;
}
/* Media query for medium-sized screens */
@media only screen and (max-width: 768px) {
  /* Styles for medium screens */
  awe-header div[left-content] img {
    width: 90px;
    height: 90px;
  }

  awe-header div[right-content] awe-icons svg {
    width: 18px;
    height: 18px;
  }

  awe-header div[right-content] .avatar-container {
    width: 30px;
    height: 30px;
  }

  awe-header div[right-content] .avatar-container .img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
  }
}

/* Media query for small screens */
@media only screen and (max-width: 480px) {
  /* Styles for small screens */
  awe-header div[left-content] img {
    width: 60px;
    height: 60px;
  }

  awe-header div[right-content] awe-icons svg {
    width: 16px;
    height: 16px;
  }

  awe-header div[right-content] .avatar-container {
    width: 20px;
    height: 20px;
  }

  awe-header div[right-content] .avatar-container .img {
    width: 20px;
    height: 20px;
    border-radius: 50%;
  }
}
.tabs-header {
  width: 100%;
}

.tabs-header ::ng-deep awe-tabs {
  width: 100%;
}

.tabs-header ::ng-deep .tabs-container {
  width: 100%;
  margin: 0;
}

.tabs-header ::ng-deep .tabs-wrapper {
  width: 100%;
}

.tabs-header ::ng-deep .tabs-row {
  width: 100%;
}

/* Make tab content flow to available space */
.tabs-header ::ng-deep .tab-item {
  flex: 1;
  min-width: 0; /* Prevents flex items from overflowing */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}