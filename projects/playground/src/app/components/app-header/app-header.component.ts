import { Component, ViewEncapsulation } from '@angular/core';
import { HeaderComponent } from "../../../../../play-comp-library/src/lib/components/header/header.component";
import { Tab } from '../../../../../play-comp-library/src/lib/interfaces/tab.interface';
import { CommonModule } from '@angular/common';
import { AvatarsComponent } from "../../../../../play-comp-library/src/lib/components/avatars/avatars.component";
import { IconsComponent } from "../../../../../play-comp-library/src/lib/components/icons/icons.component";
import { TabsComponent } from "../../../../../play-comp-library/src/lib/components/tabs/tabs.component";
interface HeaderDocSection {
  title: string;
  description: string;
  showCode: boolean;
}

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}
@Component({
  selector: 'app-app-header',
  imports: [CommonModule, HeaderComponent, AvatarsComponent, IconsComponent, TabsComponent],
  templateUrl: './app-header.component.html',
  styleUrl: './app-header.component.scss',
      encapsulation: ViewEncapsulation.None
})
export class AppHeaderComponent {
  sections: HeaderDocSection[] = [
    {
      title: 'Light Theme Header',
      description: 'Header with light theme.',
      showCode: false,
    },
    {
      title: 'Dark Theme Header',
      description: 'Header with dark theme.',
      showCode: false,
    },
    {
      title: 'Transparent Theme Header',
      description: 'Header with transparent theme.',
      showCode: false,
    },
    {
    title: 'Transparent Theme Header-2',
    description: 'Responsive header with transparent theme, including logo, navigation tabs, and user icons.',
    showCode: false,
    },
  ];

  apiProps: ApiProperty[] = [
    {
      name: 'theme',
      type: "'light' | 'dark' | 'transparent'",
      default: "'light'",
      description: 'The theme of the header.',
    },
    {
      name: 'containerClass',
      type: 'string',
      default: "''",
      description: 'Additional CSS classes for the header container.',
    },
  ];

  handleIconClick(): void {
    console.log('Icon clicked');
  }

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation();
    this.sections[index].showCode = !this.sections[index].showCode;
  }

   iconsTabs: Tab[] = [
    { label: 'Overview', active: true, icon: 'awe_home', url: '/overview' },
       {
      label: 'Launch',
      active: false,
      icon: 'awe_manage',
      dropdown: true,
      dropdownOpen: false,
      dropdownOptions: [
        { label: 'Agents', url: '/agents', icon: 'awe_tick', sublabel: 'Create, Manage and Edit Agents' },
        { label: 'Workflows', url: '/workflows', icon: 'awe_tick', sublabel: 'Create, Manage and Edit' }
      ]
    },
     {
      label: 'Libraries',
      active: false,
      icon: 'awe_manage',
      dropdown: true,
      dropdownOpen: false,
      dropdownOptions: [
        { label: 'Prompts', url: '/Prompts', icon: 'awe_tick', sublabel: 'Create, Manage and Edit Prompts' },
        { label: 'Models', url: '/Models', icon: 'awe_tick', sublabel: 'Create, Manage and Edit Models' },  
        { label: 'knowlegde-base', url: '/knowlegde', icon: 'awe_tick', sublabel: 'Create, Manage and Edit knowlegde-base' },
        { label: 'Tools', url: '/Tools', icon: 'awe_tick', sublabel: 'Create, Manage and Edit Tools' },
        { label: 'Guardrails', url: '/Guardrails', icon: 'awe_tick', sublabel: 'Create, Manage and Edit Guardrails' },
      ]
    },
       {
      label: 'Manage',
      active: false,
      icon: 'awe_manage',
      dropdown: true,
      dropdownOpen: false,
      dropdownOptions: [
        { label: 'Management-1', url: '/Management-1', icon: 'awe_tick', sublabel: 'Create, Manage and Edit Management-1' },
        { label: 'Management-2', url: '/Management-2', icon: 'awe_tick', sublabel: 'Create, Manage and Edit Management-2' },  
      ]
    },
     {
      label: 'Analytics',
      active: false,
      icon: 'awe_manage',
      dropdown: true,
      dropdownOpen: false,
      dropdownOptions: [
        { label: 'Analytics-1', url: '/Analytics-1', icon: 'awe_tick', sublabel: 'Manage your Analytics' },
        { label: 'Analytics-2', url: '/Analytics-2', icon: 'awe_tick', sublabel: 'Manage your Analytics' },  
      ]
  
    }]
    onTabChange(index: number) {
      console.log('Tab changed:', index);
      const allTabSets = [
        this.iconsTabs,
      ];
      const activeTabSet = allTabSets.find(tabSet => tabSet[index] !== undefined);
      if (activeTabSet) {
        activeTabSet.forEach(tab => {
          tab.active = false;
        });
        activeTabSet[index].active = true;
      }
    }
  
    onUrlChange(url: string) {
      console.log('URL changed:', url);
    }

  getExampleCode(section: string): string {
    const examples: Record<string, string> = {
      'light theme header': `
import { Component } from '@angular/core';
import { HeaderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-light-theme-header',
  standalone: true,
  imports: [HeaderComponent],
  template: \`
    <awe-header theme="light" containerClass="mb-4 mt-4">
      <div left-content>
        <h2>Ascendion</h2>
      </div>
      <div center-content>
        <awe-tabs [tabs]="ThreeTabs" [showContent]="false" theme="light"></awe-tabs>
      </div>
      <div right-content class="header-right-content">
        <awe-icons [iconName]="'awe_save'"></awe-icons>
        <awe-avatars iconName="awe_dashboard"></awe-avatars>
        <awe-avatars iconName="awe_bell"></awe-avatars>
        <awe-avatars iconName="awe_jurica.jpg" [badgeCount]="6" badgeColor="danger"></awe-avatars>
      </div>
    </awe-header>
  \`
})
export class LightThemeHeaderComponent {
  ThreeTabs = this.generateTabs(3);

  generateTabs(count: number): Tab[] {
    return Array.from({ length: count }, (_, i) => ({
      label: \`Main Tab \${i + 1}\`,
      active: i === 0,
    }));
  }
}`,
      'dark theme header': `
import { Component } from '@angular/core';
import { HeaderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-dark-theme-header',
  standalone: true,
  imports: [HeaderComponent],
  template: \`
    <awe-header theme="dark">
      <div center-content>
        <awe-tabs [tabs]="TwoTabs" [showContent]="false" theme="dark"></awe-tabs>
      </div>
      <div right-content class="header-right-content">
        <awe-icons [iconName]="'awe_save'" iconColor="whiteIcon"></awe-icons>
        <awe-avatars iconName="awe_dashboard"></awe-avatars>
        <awe-avatars iconName="awe_bell"></awe-avatars>
        <awe-avatars iconName="awe_jurica.jpg" [badgeCount]="6" badgeColor="danger"></awe-avatars>
      </div>
    </awe-header>
  \`
})
export class DarkThemeHeaderComponent {
  TwoTabs = this.generateTabs(2);

  generateTabs(count: number): Tab[] {
    return Array.from({ length: count }, (_, i) => ({
      label: \`Main Tab \${i + 1}\`,
      active: i === 0,
    }));
  }
}`,
    'transparent theme header': `
import { Component } from '@angular/core';
import { HeaderComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-transparent-theme-header',
  standalone: true,
  imports: [HeaderComponent],
  template: \`
    <awe-header theme="transparent" containerClass="mb-4 mt-4">
      <div left-content>
        <img src="assets/logos/dlogo.svg" alt="Logo">
      </div>
      <div right-content class="header-right">
        <awe-icons class="icon" [iconName]="'awe_graph'" iconColor="header" (click)="onGraphIconClick()"></awe-icons>
        <awe-icons class="icon" [iconName]="'awe_translate'" iconColor="header" (click)="onTranslateIconClick()"></awe-icons>
        <awe-icons class="icon" [iconName]="'awe_day_and_light_mode'" iconColor="header" (click)="onDayLightModeIconClick()"></awe-icons>
        <awe-avatars [iconName]="getLoggedInUserLogo()" [avatarSquare]="true" class="user-avatar" (click)="toggleDropdown()"></awe-avatars>
        <div class="dropdown" *ngIf="isDropdownOpen">
          <div class="account" *ngFor="let account of getSortedAccounts(); let i = index" (click)="login(account)" [ngClass]="{'not-logged-in': !account.isLoggedIn}">
            <img [src]="account.userLogo" alt="User Logo" class="user-logo">
            <div class="account-info">
              <div class="company-info">
                <img [src]="account.companyLogo" alt="Company Logo" class="company-logo">
              </div>
              <p>{{ account.email }}</p>
              <button [class.logged-in]="account.isLoggedIn" [class.log-in]="!account.isLoggedIn">
                {{ account.isLoggedIn ? 'Logged in' : 'Log in' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </awe-header>
  \`,

  
  styles: [\`
    .header-right {
      display: flex;
      align-items: center;
      gap: 32px;
    }

    .header-right awe-icons svg {
      margin-top: 5px;
    }

    .icon {
      display: flex;
      width: 40px;
      height: 40px;
      padding: 10px;
      justify-content: center;
      align-items: center;
      gap: 10px;
      flex-shrink: 0;
    }

    .icon:hover {
      border: 1px solid transparent;
      background: linear-gradient(white, white) padding-box,
                  linear-gradient(to bottom, #706DCE, #F773AF) border-box;
      border-radius: 50%;
    }

    .dropdown {
      position: absolute;
      right: 0;
      top: 60px;
      background-color: white;
      border-radius: 16px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      z-index: 1000;
      width: 300px;
    }

    .account {
      display: flex;
      align-items: center;
      padding: 12px;
      border-bottom: 1px solid #eee;
      cursor: pointer;
      background-color: var(--account-bg-color, #fff); /* Use the dynamic background color */
    }

    .account:first-child {
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
    }

    .account:last-child {
      border-bottom: none;
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
    }

    .not-logged-in {
      background-color: #EDEDF3; /* Specific background color for not logged in accounts */
    }

    .company-logo {
      width: 100px;
      height: 30px;
      margin-right: 12px;
    }

    .user-logo {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      margin-right: 8px;
    }

    .account-info {
      flex: 1;
    }

    .account-info h3 {
      margin: 0;
      font-size: 14px;
    }

    .account-info p {
      margin: 9px 0;
      font-size: 14px;
      color: #666D99;
    }

    .account-info button {
      border: none;
      padding: 4px 8px;
      border-radius: 4px;
      cursor: pointer;
    }

    .account-info button.logged-in {
      color: #A3A7C2;
      border-radius: 24px;
      border: 1px solid var(--Text-Disabled, #A3A7C2);
    }

    .account-info button.log-in {
      border-radius: 24px;
      background-color: white;
      color: black;
    }
  \`]
})


export class TransparentThemeHeaderComponent {
  isDropdownOpen = false;
  accounts = [
    {
      companyName: 'ASCENDION',
      email: '<EMAIL>',
      companyLogo: 'assets/logos/dlogo.svg',
      userLogo: 'assets/images/awe_john.jpg',
      isLoggedIn: true
    },
    {
      companyName: 'Axos',
      email: '<EMAIL>',
      companyLogo: 'assets/logos/dlogo.svg',
      userLogo: 'assets/images/awe_akash.jpg',
      isLoggedIn: false
    }
  ];

  toggleDropdown() {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  login(account: any) {
    this.accounts.forEach(acc => {
      acc.isLoggedIn = false;
    });
    account.isLoggedIn = true;
    this.isDropdownOpen = false;
  }

  onDayLightModeIconClick(): void {
    console.log('Day Light Mode Icon Clicked');
  }

  onTranslateIconClick(): void {
    console.log('Translate Icon Clicked');
  }

  onGraphIconClick(): void {
    console.log('Graph Icon Clicked');
  }

  getSortedAccounts() {
    return this.accounts.sort((a, b) => {
      if (a.isLoggedIn && !b.isLoggedIn) {
        return -1;
      } else if (!a.isLoggedIn && b.isLoggedIn) {
        return 1;
      } else {
        return 0;
      }
    });
  }

  getLoggedInUserLogo(): string {
    const loggedInAccount = this.accounts.find(account => account.isLoggedIn);
    const userLogo = loggedInAccount ? loggedInAccount.userLogo : 'assets/images/awe_jurica.jpg';
    return userLogo;
  }
}`,
    'transparent theme header-2': `
import { Component } from '@angular/core';
import { HeaderComponent } from '@awe/play-comp-library';
import { Tab } from '@awe/play-comp-library';
import { TabsComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-transparent-theme-header',
  standalone: true,
  imports: [HeaderComponent],
  template: \`
       <awe-header theme="transparent" containerClass="mb-4 mt-4">
                <div left-content>
                  <img src="assets/logos/dlogo.svg" alt="Logo">
                </div>

                <div center-content class="tabs-header">
                  <awe-tabs [tabs]="iconsTabs" variant="equal-width" [showContent]="false" [useUrlNavigation]="true"
                    (tabChange)="onTabChange($event)" (urlChange)="onUrlChange($event)"></awe-tabs>
                </div>

                <div right-content class="header-right-content">
                  <awe-icons class="icon" [iconName]="'awe_graph'" iconColor="header"></awe-icons>
                  <awe-icons class="icon" [iconName]="'awe_day_and_light_mode'" iconColor="header"></awe-icons>
                  <awe-avatars [iconName]="'awe_jurica.jpg'" [avatarSquare]="true"></awe-avatars>
                </div>
              </awe-header>
  \`,

  
  styles: [\`
    .header-right-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-4x);
  }

  .header-right {
  display: flex;
  align-items: center;
  gap: 32px;
}

.header-right awe-icons svg {
  margin-top: 10px;
}

awe-icons svg{
  margin-top: 5px;
}

.icon {
  display: flex;
  width: 40px;
  height: 40px;
  padding: 10px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  flex-shrink: 0;
}

.icon:hover {
  border: 1px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(to bottom, #706DCE, #F773AF) border-box;
  border-radius: 50%;
}
/* Media query for medium-sized screens */
@media only screen and (max-width: 768px) {
  /* Styles for medium screens */
  awe-header div[left-content] img {
    width: 90px;
    height: 90px;
  }

  awe-header div[right-content] awe-icons svg {
    width: 18px;
    height: 18px;
  }

  awe-header div[right-content] .avatar-container {
    width: 30px;
    height: 30px;
  }

  awe-header div[right-content] .avatar-container .img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
  }
}

/* Media query for small screens */
@media only screen and (max-width: 480px) {
  /* Styles for small screens */
  awe-header div[left-content] img {
    width: 60px;
    height: 60px;
  }

  awe-header div[right-content] awe-icons svg {
    width: 16px;
    height: 16px;
  }

  awe-header div[right-content] .avatar-container {
    width: 20px;
    height: 20px;
  }

  awe-header div[right-content] .avatar-container .img {
    width: 20px;
    height: 20px;
    border-radius: 50%;
  }
}
.tabs-header {
  width: 100%;
}

.tabs-header ::ng-deep awe-tabs {
  width: 100%;
}

.tabs-header ::ng-deep .tabs-container {
  width: 100%;
  margin: 0;
}

.tabs-header ::ng-deep .tabs-wrapper {
  width: 100%;
}

.tabs-header ::ng-deep .tabs-row {
  width: 100%;
}

.tabs-header ::ng-deep .tab-item {
  flex: 1;
  min-width: 0; /* Prevents flex items from overflowing */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
  \`]
})


export class TransparentThemeHeaderComponent {
   iconsTabs: Tab[] = [
    { label: 'Overview', active: true, icon: 'awe_home', url: '/overview' },
       {
      label: 'Launch',
      active: false,
      icon: 'awe_manage',
      dropdown: true,
      dropdownOpen: false,
      dropdownOptions: [
        { label: 'Agents', url: '/agents', icon: 'awe_tick', sublabel: 'Create, Manage and Edit Agents' },
        { label: 'Workflows', url: '/workflows', icon: 'awe_tick', sublabel: 'Create, Manage and Edit' }
      ]
    },
     {
      label: 'Libraries',
      active: false,
      icon: 'awe_manage',
      dropdown: true,
      dropdownOpen: false,
      dropdownOptions: [
        { label: 'Prompts', url: '/Prompts', icon: 'awe_tick', sublabel: 'Create, Manage and Edit Prompts' },
        { label: 'Models', url: '/Models', icon: 'awe_tick', sublabel: 'Create, Manage and Edit Models' },  
        { label: 'knowlegde-base', url: '/knowlegde', icon: 'awe_tick', sublabel: 'Create, Manage and Edit knowlegde-base' },
        { label: 'Tools', url: '/Tools', icon: 'awe_tick', sublabel: 'Create, Manage and Edit Tools' },
        { label: 'Guardrails', url: '/Guardrails', icon: 'awe_tick', sublabel: 'Create, Manage and Edit Guardrails' },
      ]
    },
       {
      label: 'Manage',
      active: false,
      icon: 'awe_manage',
      dropdown: true,
      dropdownOpen: false,
      dropdownOptions: [
        { label: 'Management-1', url: '/Management-1', icon: 'awe_tick', sublabel: 'Create, Manage and Edit Management-1' },
        { label: 'Management-2', url: '/Management-2', icon: 'awe_tick', sublabel: 'Create, Manage and Edit Management-2' },  
      ]
    },
     {
      label: 'Analytics',
      active: false,
      icon: 'awe_manage',
      dropdown: true,
      dropdownOpen: false,
      dropdownOptions: [
        { label: 'Analytics-1', url: '/Analytics-1', icon: 'awe_tick', sublabel: 'Manage your Analytics' },
        { label: 'Analytics-2', url: '/Analytics-2', icon: 'awe_tick', sublabel: 'Manage your Analytics' },  
      ]
  
    }]
    onTabChange(index: number) {
      console.log('Tab changed:', index);
      const allTabSets = [
        this.iconsTabs,
      ];
      const activeTabSet = allTabSets.find(tabSet => tabSet[index] !== undefined);
      if (activeTabSet) {
        activeTabSet.forEach(tab => {
          tab.active = false;
        });
        activeTabSet[index].active = true;
      }
    }
  
    onUrlChange(url: string) {
      console.log('URL changed:', url);
    }
}`,
  };


    return examples[section] || '';
  }

  copyCode(section: string): void {
    const code = this.getExampleCode(section);
    navigator.clipboard
      .writeText(code)
      .then(() => {
        console.log('Code copied to clipboard');
      })
      .catch((err) => {
        console.error('Failed to copy code:', err);
      });
  }

  searchText: string = '';

  onSearchChange() {
    console.log('Search text changed:', this.searchText); 

  }

  // Basic Tabs
  OneTab: Tab[] = this.generateTabs(1);
  TwoTabs: Tab[] = this.generateTabs(2);
  ThreeTabs: Tab[] = this.generateTabs(3);
  FourTabs: Tab[] = this.generateTabs(4);
  FiveTabs: Tab[] = this.generateTabs(5);
  SixTabs: Tab[] = this.generateTabs(6);

  generateTabs(count: number): Tab[] {
    return Array.from({ length: count }, (_, i) => ({
      label: `Main Tab ${i + 1}`,
      active: i === 0,
    }));
  }

isDropdownOpen = false;
accounts = [
  {
    companyName: 'ASCENDION',
    email: '<EMAIL>',
    companyLogo: 'assets/logos/dlogo.svg',
    userLogo: 'assets/images/awe_john.jpg',
    isLoggedIn: true
  },
  {
    companyName: 'Axos',
    email: '<EMAIL>',
    companyLogo: 'assets/logos/dlogo.svg',
    userLogo: 'assets/images/awe_akash.jpg',
    isLoggedIn: false
  }
];

toggleDropdown() {
  this.isDropdownOpen = !this.isDropdownOpen;
}

login(account: any) {
  // Log out from all accounts
  this.accounts.forEach(acc => {
    acc.isLoggedIn = false;
  });

  // Log in to the selected account
  account.isLoggedIn = true;

  // Close the dropdown
  this.isDropdownOpen = false;
}

onDayLightModeIconClick(): void {
  console.log('Day Light Mode Icon Clicked');
}

onTranslateIconClick(): void {
  console.log('Translate Icon Clicked');
}

onGraphIconClick(): void {
  console.log('Graph Icon Clicked');
}

// Method to sort accounts
getSortedAccounts() {
  return this.accounts.sort((a, b) => {
    if (a.isLoggedIn && !b.isLoggedIn) {
      return -1;
    } else if (!a.isLoggedIn && b.isLoggedIn) {
      return 1;
    } else {
      return 0;
    }
  });
}

// Method to get the user logo of the logged-in account
getLoggedInUserLogo(): string {
  const loggedInAccount = this.accounts.find(account => account.isLoggedIn);
  const userLogo = loggedInAccount ? loggedInAccount.userLogo : 'assets/images/awe_jurica.jpg';
  return userLogo;
}


}
