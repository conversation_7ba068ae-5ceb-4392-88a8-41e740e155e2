import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DateTimePickerComponent, DateTimeSelection } from '../../../../../play-comp-library/src/lib/composite-components/date-time-picker/date-time-picker.component';
import { DateRange } from '../../../../../play-comp-library/src/lib/components/calendar/calendar.component';

@Component({
  selector: 'app-app-date-time-picker',
  imports: [CommonModule, DateTimePickerComponent],
  templateUrl: './app-date-time-picker.component.html',
  styleUrl: './app-date-time-picker.component.scss'
})
export class AppDateTimePickerComponent {
  selectedDate: Date | null = null;
  selectedTime = '';
  selectedRange: DateRange = { start: null, end: null };
  selectedRangeTime = '';

  onBasicDateTimeSelected(selection: DateTimeSelection): void {
    console.log('Basic Date-Time Selection:', selection);
    this.selectedDate = selection.date;
    this.selectedTime = selection.time;
  }

  onRangeDateTimeSelected(selection: DateTimeSelection): void {
    console.log('Range Date-Time Selection:', selection);
    this.selectedRange = selection.dateRange || { start: null, end: null };
    this.selectedRangeTime = selection.time;
  }

  onBasicDateSelected(date: Date): void {
    console.log('Basic Date Selected:', date);
  }

  onRangeSelected(range: DateRange): void {
    console.log('Range Selected:', range);
  }

  onTimeSelected(time: string): void {
    console.log('Time Selected:', time);
  }
}
