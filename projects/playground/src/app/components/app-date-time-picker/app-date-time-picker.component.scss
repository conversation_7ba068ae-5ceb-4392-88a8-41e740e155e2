.demo-sections {
  // padding: 32px;
  max-width: 800px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
  display: flex;
  flex-direction: column;
  // align-items: center;
  justify-content: center;
  // min-height: 100vh;
  // gap: 48px;

  @media (max-width: 768px) {
    gap: 32px;
  }

  .demo-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    width: 100%;

    h2 {
      color: #111827;
      font-size: 24px;
      margin-bottom: 8px;
      font-weight: 600;
    }

    p {
      color: #6b7280;
      margin-bottom: 24px;
      line-height: 1.5;
      font-size: 16px;
    }

    .demo-container {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 32px;
      width: 100%;
      max-width: 600px;
    }

    .selection-info {
      background: #f9fafb;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 24px;
      text-align: center;
      width: 100%;
      max-width: 600px;

      h3 {
        color: #111827;
        font-size: 18px;
        margin-bottom: 16px;
        font-weight: 600;
      }

      .info-item {
        color: #6b7280;
        margin-bottom: 8px;
        line-height: 1.5;
        font-size: 14px;

        strong {
          color: #111827;
          font-weight: 600;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
