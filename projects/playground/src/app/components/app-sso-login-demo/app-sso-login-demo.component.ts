import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SSOLoginComponent, SSOLoginVariant } from '../../../../../play-comp-library/src/lib/composite-components/sso-login/sso-login.component';

@Component({
  selector: 'app-sso-login-demo',
  standalone: true,
  imports: [
    CommonModule,
    SSOLoginComponent
  ],
  templateUrl: './app-sso-login-demo.component.html',
  styleUrl: './app-sso-login-demo.component.scss'
})
export class AppSSOLoginDemoComponent {
  // Simple demo component to showcase SSO login variants
}
