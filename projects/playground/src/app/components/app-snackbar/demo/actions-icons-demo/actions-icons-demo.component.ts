import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SnackbarService } from '../../../../../../../play-comp-library/src/lib/components/snackbar/snackbar.service';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';

@Component({
  selector: 'ava-snackbar-actions-icons-demo',
  standalone: true,
  imports: [CommonModule, ButtonComponent],
  template: `
    <div class="demo-container">
      <div class="actions-grid">
        <ava-button
          label="Snackbar with Action"
          variant="primary"
          size="medium"
          state="default"
          (click)="showActionSnackbar()"
        >
        </ava-button>
        <!-- <ava-button
          label="Snackbar with Icon"
          variant="primary"
          size="medium"
          state="default"
          (click)="showIconSnackbar()"
        >
        </ava-button>
        <ava-button
          label="Dismissible Snackbar"
          variant="secondary"
          size="medium"
          state="default"
          (click)="showDismissibleSnackbar()"
        >
        </ava-button>
        <ava-button
          label="Persistent Snackbar"
          variant="secondary"
          size="medium"
          state="default"
          (click)="showPersistentSnackbar()"
        >
        </ava-button> -->
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 700px;
        margin: 0 auto;
        padding: 2rem;
        text-align: center;
      }
      .actions-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
      }
    `,
  ],
})
export class SnackbarActionsIconsDemoComponent {
  private snackbar = inject(SnackbarService);

  showActionSnackbar() {
    this.snackbar.show('File uploaded!', 'bottom-center', 4000, '', '', {
      action: {
        text: 'Undo',
        color: 'primary',
        callback: () => alert('Undo clicked!'),
      },
    });
  }

  showIconSnackbar() {
    this.snackbar.show('Success!', 'bottom-center', 4000, '', '', {
      icon: {
        name: 'check-circle',
        color: '#16a34a',
        size: 20,
        position: 'left',
      },
    });
  }

  showDismissibleSnackbar() {
    this.snackbar.show('Dismissible snackbar', 'bottom-center', 4000, '', '', {
      dismissible: true,
    });
  }

  showPersistentSnackbar() {
    this.snackbar.show(
      'Persistent snackbar (must be dismissed)',
      'bottom-center',
      0,
      '',
      '',
      {
        persistent: true,
        dismissible: true,
      }
    );
  }
}
