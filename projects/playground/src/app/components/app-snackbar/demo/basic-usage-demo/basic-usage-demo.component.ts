import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SnackbarService } from '../../../../../../../play-comp-library/src/lib/components/snackbar/snackbar.service';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';

@Component({
  selector: 'ava-snackbar-basic-usage-demo',
  standalone: true,
  imports: [CommonModule, ButtonComponent],
  template: `
    <div class="demo-container">
      <ava-button
        label="Show Snackbar"
        variant="primary"
        size="medium"
        state="default"
        (click)="showSnackbar()"
      >
      </ava-button>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 700px;
        margin: 0 auto;
        padding: 2rem;
        margin-top: 3rem;
        text-align: center;
      }
    `,
  ],
})
export class SnackbarBasicUsageDemoComponent {
  private snackbar = inject(SnackbarService);

  showSnackbar() {
    this.snackbar.show('This is a snackbar message', 'top-center');
  }
}
