import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SnackbarService } from '../../../../../../../play-comp-library/src/lib/components/snackbar/snackbar.service';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';

@Component({
  selector: 'ava-snackbar-variants-demo',
  standalone: true,
  imports: [CommonModule, ButtonComponent],
  template: `
    <div class="demo-container">
      <h2>Snackbar Variants & Types</h2>
      <p class="demo-description">
        Show snackbars with different surface variants and types.
      </p>
      <div class="variants-grid">
        <ava-button
          label="Surface Bold / Medium"
          variant="primary"
          size="medium"
          state="default"
          (click)="showVariant('surface-bold', 'medium')"
        >
        </ava-button>
        <ava-button
          label="Surface Bold / Strong"
          variant="primary"
          size="medium"
          state="default"
          (click)="showVariant('surface-bold', 'strong')"
        >
        </ava-button>
        <ava-button
          label="Surface Bold / Max"
          variant="primary"
          size="medium"
          state="default"
          (click)="showVariant('surface-bold', 'max')"
        >
        </ava-button>
        <ava-button
          label="Custom Variant / Medium"
          variant="secondary"
          size="medium"
          state="default"
          (click)="showVariant('custom', 'medium')"
        >
        </ava-button>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 700px;
        margin: 0 auto;
        padding: 2rem;
        text-align: center;
      }
      .variants-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
      }
    `,
  ],
})
export class SnackbarVariantsDemoComponent {
  private snackbar = inject(SnackbarService);

  showVariant(variant: string, type: string) {
    this.snackbar.show(
      `Snackbar: ${variant} / ${type}`,
      'bottom-center',
      4000,
      '',
      '',
      { variant, type }
    );
  }
}
