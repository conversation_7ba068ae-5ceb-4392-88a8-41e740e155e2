import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  SnackbarService,
  SnackbarPosition,
} from '../../../../../../../play-comp-library/src/lib/components/snackbar/snackbar.service';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/lib/components/button/button.component';

@Component({
  selector: 'ava-snackbar-positions-demo',
  standalone: true,
  imports: [CommonModule, ButtonComponent],
  template: `
    <div class="demo-container">
      <div class="positions-grid">
        <ava-button
          *ngFor="let pos of positions"
          [label]="pos"
          variant="primary"
          size="medium"
          state="default"
          (click)="showSnackbar(pos)"
        >
        </ava-button>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 700px;
        margin: 0 auto;
        padding: 2rem;
        margin-top: 3rem;
        text-align: center;
      }
      .positions-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        justify-content: center;
        margin-top: 2rem;
      }
    `,
  ],
})
export class SnackbarPositionsDemoComponent {
  private snackbar = inject(SnackbarService);

  positions: SnackbarPosition[] = [
    'top-left',
    'top-center',
    'top-right',
    'bottom-left',
    'bottom-center',
    'bottom-right',
    'center',
  ];

  showSnackbar(position: SnackbarPosition) {
    this.snackbar.show(`Snackbar at ${position}`, position);
  }
}
