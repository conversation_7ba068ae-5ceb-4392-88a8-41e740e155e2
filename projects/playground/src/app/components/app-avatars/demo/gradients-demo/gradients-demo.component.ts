import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AvatarsComponent } from '../../../../../../../play-comp-library/src/lib/components/avatars/avatars.component';

@Component({
  selector: 'ava-avatars-gradients-demo',
  standalone: true,
  imports: [CommonModule, AvatarsComponent],
  template: `
    <div class="demo-container">
      <div class="demo-section">
        <div class="gradient-examples">
          <div class="gradient-item">
            <h4>Default Gradient</h4>
            <ava-avatars
              size="large"
              shape="pill"
              [imageUrl]="sampleImageUrl"
              [processedanddone]="true"
              altText="Avatar with default gradient"
            >
            </ava-avatars>
          </div>
          <div class="gradient-item">
            <h4>Custom Colors</h4>
            <ava-avatars
              size="large"
              shape="pill"
              [imageUrl]="sampleImageUrl"
              [processedanddone]="true"
              [gradientColors]="['#654ea3', '#eaafc8']"
              altText="Avatar with custom gradient colors"
            >
            </ava-avatars>
          </div>
          <div class="gradient-item">
            <h4>Blue Gradient</h4>
            <ava-avatars
              size="large"
              shape="square"
              [imageUrl]="sampleImageUrl"
              [processedanddone]="true"
              [gradientColors]="['#4949FF', '#0000FF']"
              altText="Avatar with blue gradient"
            >
            </ava-avatars>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        padding: 2rem;
        max-width: 800px;
        margin: 0 auto;
      }

      .demo-section {
        margin-bottom: 2rem;
      }

      .demo-section h3 {
        color: #1f2937;
        margin-bottom: 1rem;
        font-size: 1.25rem;
        font-weight: 600;
      }

      .gradient-examples {
        display: flex;
        gap: 2rem;
        align-items: center;
        justify-content: center;
        padding: 1rem;
        border-radius: 8px;
        margin-top: 0;
      }

      .gradient-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
      }

      .gradient-item h4 {
        color: #374151;
        margin: 0;
        font-size: 0.875rem;
        font-weight: 500;
      }
    `,
  ],
})
export class GradientsDemoComponent {
  sampleImageUrl = 'assets/1.png';
}
