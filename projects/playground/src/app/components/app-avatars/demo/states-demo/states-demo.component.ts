import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AvatarsComponent } from '../../../../../../../play-comp-library/src/lib/components/avatars/avatars.component';

@Component({
  selector: 'ava-avatars-states-demo',
  standalone: true,
  imports: [CommonModule, AvatarsComponent],
  template: `
    <div class="demo-container">
      <div class="demo-section">
        <div class="state-examples">
          <div class="state-item">
            <h4 style="color:var(--color-text-primary)">Default</h4>
            <ava-avatars
              size="large"
              shape="pill"
              [imageUrl]="sampleImageUrl"
              altText="Default avatar state"
            >
            </ava-avatars>
          </div>
          <div class="state-item">
            <h4 style="color:var(--color-text-primary)">Active</h4>
            <ava-avatars
              size="large"
              shape="pill"
              [imageUrl]="sampleImageUrl"
              [active]="true"
              altText="Active avatar state"
            >
            </ava-avatars>
          </div>
          <div class="state-item">
            <h4 style="color:var(--color-text-primary)">Processing</h4>
            <ava-avatars
              size="large"
              shape="pill"
              [imageUrl]="sampleImageUrl"
              [processedanddone]="true"
              altText="Processed avatar state"
            >
            </ava-avatars>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        padding: 2rem;
        max-width: 800px;
        margin: 0 auto;
      }

      .demo-section {
        margin-bottom: 2rem;
      }

      .demo-section h3 {
        color: #1f2937;
        margin-bottom: 1rem;
        font-size: 1.25rem;
        font-weight: 600;
      }

      .state-examples {
        display: flex;
        gap: 2rem;
        align-items: center;
        justify-content: center;
        padding: 1rem;
        border-radius: 8px;
        margin-top: 0;
      }

      .state-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
      }

      .state-item h4 {
        color: #374151;
        margin: 0;
        font-size: 0.875rem;
        font-weight: 500;
      }
    `,
  ],
})
export class StatesDemoComponent {
  sampleImageUrl = 'assets/1.svg';
}
