import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AvatarsComponent } from '../../../../../../../play-comp-library/src/lib/components/avatars/avatars.component';

@Component({
  selector: 'ava-avatars-sizes-demo',
  standalone: true,
  imports: [CommonModule, AvatarsComponent],
  template: `
    <div class="demo-container">
      <div class="demo-section">
        <div class="size-examples">
          <div class="size-item">
            <h4 style="color:var(--color-text-primary)">Ultra Small</h4>
            <ava-avatars
              size="ultra-small"
              shape="pill"
              [imageUrl]="sampleImageUrl"
              altText="Ultra Small avatar"
            >
            </ava-avatars>
          </div>
          <div class="size-item">
            <h4 style="color:var(--color-text-primary)">Small</h4>
            <ava-avatars
              size="small"
              shape="pill"
              [imageUrl]="sampleImageUrl"
              altText="Small avatar"
            >
            </ava-avatars>
          </div>
          <div class="size-item">
            <h4 style="color:var(--color-text-primary)">Medium</h4>
            <ava-avatars
              size="medium"
              shape="pill"
              [imageUrl]="sampleImageUrl"
              altText="Medium avatar"
            >
            </ava-avatars>
          </div>
          <div class="size-item">
            <h4 style="color:var(--color-text-primary)">Large</h4>
            <ava-avatars
              size="large"
              shape="pill"
              [imageUrl]="sampleImageUrl"
              altText="Large avatar"
            >
            </ava-avatars>
          </div>
          <div class="size-item">
            <h4 style="color:var(--color-text-primary)">Extra Large</h4>
            <ava-avatars
              size="extra-large"
              shape="pill"
              [imageUrl]="sampleImageUrl"
              altText="Extra Large avatar"
            >
            </ava-avatars>
          </div>
          <div class="size-item">
            <h4 style="color:var(--color-text-primary)">Ultra Large</h4>
            <ava-avatars
              size="ultra-large"
              shape="pill"
              [imageUrl]="sampleImageUrl"
              altText="Ultra Large avatar"
            >
            </ava-avatars>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        padding: 2rem;
        max-width: 800px;
        margin: 0 auto;
      }

      .demo-section {
        margin-bottom: 2rem;
      }

      .demo-section h3 {
        color: #1f2937;
        margin-bottom: 1rem;
        font-size: 1.25rem;
        font-weight: 600;
      }

      .size-examples {
        display: flex;
        gap: 2rem;
        align-items: center;
        justify-content: center;
        padding: 1rem;
        border-radius: 8px;
        margin-top: 0;
      }

      .size-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
      }

      .size-item h4 {
        color: #374151;
        margin: 0;
        font-size: 0.875rem;
        font-weight: 500;
      }
    `,
  ],
})
export class SizesDemoComponent {
  sampleImageUrl = 'assets/1.svg';
}
