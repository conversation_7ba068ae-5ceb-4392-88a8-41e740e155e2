.demo-container {
  max-width: 840px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.demo-header {
  margin-bottom: 3rem;
  text-align: center;

  h2 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
  }

  p {
    font-size: 1.1rem;
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 2rem;
  }

  .copy-button {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;

    &:hover {
      background: #2563eb;
    }
  }
}

.demo-content {
  .demo-section {
    margin-bottom: 4rem;
    padding: 2rem;
    border-radius: 0.75rem;

    h3 {
      font-size: 1.5rem;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 0.5rem;
    }

    p {
      font-size: 0.95rem;
      color: #64748b;
      margin-bottom: 2rem;
      line-height: 1.5;
    }
  }
}

.spinner-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
  }
}

.spinner-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 0.5rem;

  .type-label {
    font-size: 0.875rem;
    color: #475569;
    font-weight: 600;
    text-align: center;
  }
}

.type-descriptions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.type-description {
  padding: 1rem;
  border-radius: 0.5rem;

  h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
  }

  p {
    font-size: 0.875rem;
    color: #64748b;
    line-height: 1.5;
    margin: 0;
  }
}
