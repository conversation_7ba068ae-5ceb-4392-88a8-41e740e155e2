import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SpinnerComponent } from '../../../../../../../play-comp-library/src/lib/components/spinner/spinner.component';

@Component({
  selector: 'ava-basic-usage-demo',
  standalone: true,
  imports: [CommonModule, SpinnerComponent],
  templateUrl: './basic-usage-demo.component.html',
  styleUrls: ['./basic-usage-demo.component.scss'],
})
export class BasicUsageDemoComponent {
  copyToClipboard(): void {
    const code = this.getExampleCode();
    navigator.clipboard
      .writeText(code)
      .then(() => {
        console.log('Code copied to clipboard');
      })
      .catch((err) => {
        console.error('Failed to copy code:', err);
      });
  }

  getExampleCode(): string {
    return `import { Component } from '@angular/core';
import { SpinnerComponent } from 'play-comp-library';

@Component({
  selector: 'app-basic-spinners',
  standalone: true,
  imports: [SpinnerComponent],
  template: \`
    <div class="spinner-demo">
      <h3>Progress Indicators</h3>
      <div class="spinner-row">
        <ava-spinner type="circular" color="primary" [progressIndex]="25" size="sm"></ava-spinner>
        <ava-spinner type="circular" color="primary" [progressIndex]="50" size="sm"></ava-spinner>
        <ava-spinner type="circular" color="primary" [progressIndex]="75" size="sm"></ava-spinner>
        <ava-spinner type="circular" color="primary" [progressIndex]="100" size="sm"></ava-spinner>
      </div>
      
      <h3>Medium Size Progress</h3>
      <div class="spinner-row">
        <ava-spinner type="circular" color="primary" [progressIndex]="25" size="md"></ava-spinner>
        <ava-spinner type="circular" color="primary" [progressIndex]="50" size="md"></ava-spinner>
        <ava-spinner type="circular" color="primary" [progressIndex]="75" size="md"></ava-spinner>
        <ava-spinner type="circular" color="primary" [progressIndex]="100" size="md"></ava-spinner>
      </div>
      
      <h3>Animated Spinners</h3>
      <div class="spinner-row">
        <ava-spinner type="circular" color="primary" size="md" [animation]="true"></ava-spinner>
        <ava-spinner type="dotted" color="success" size="md" [animation]="true"></ava-spinner>
        <ava-spinner type="partial" color="warning" size="md" [animation]="true"></ava-spinner>
      </div>
    </div>
  \`
})
export class BasicSpinnersComponent { }`;
  }
}
