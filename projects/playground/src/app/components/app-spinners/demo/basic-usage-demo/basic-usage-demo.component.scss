.demo-container {
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.demo-header {
  margin-bottom: 3rem;
  text-align: center;

  h2 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
  }

  p {
    font-size: 1.1rem;
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 2rem;
  }

  .copy-button {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;

    &:hover {
      background: #2563eb;
    }
  }
}

.demo-content {
  .demo-section {
    margin-bottom: 4rem;
    padding: 2rem;
    border-radius: 0.75rem;

    h3 {
      font-size: 1.5rem;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 0.5rem;
    }

    p {
      font-size: 0.95rem;
      color: #64748b;
      margin-bottom: 2rem;
      line-height: 1.5;
    }
  }
}

.spinner-row {
  display: flex;
  flex-wrap: wrap;
  gap: 2rem;
  justify-content: center;
  align-items: center;
  min-height: 120px;

  @media (max-width: 768px) {
    gap: 1.5rem;
  }
}

.spinner-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  min-width: 80px;

  .label {
    font-size: 0.875rem;
    color: #64748b;
    font-weight: 500;
    text-align: center;
  }
}
