.demo-container {
  max-width: 800px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.demo-header {
  margin-bottom: 3rem;
  text-align: center;

  h2 {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: 1rem;
  }

  p {
    font-size: 1.1rem;
    color: var(--color-text-primary);
    line-height: 1.6;
    margin-bottom: 2rem;
  }

  .copy-button {
    background: var(--color-primary);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;

    &:hover {
      background: var(--color-primary);
    }
  }
}

.demo-content {
  .demo-section {
    margin-bottom: 4rem;
    padding: 2rem;
    border-radius: 0.75rem;

    h3 {
      font-size: 1.5rem;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 0.5rem;
    }

    p {
      font-size: 0.95rem;
      color: #64748b;
      margin-bottom: 2rem;
      line-height: 1.5;
    }
  }
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
  }
}

.color-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 0.5rem;

  .color-label {
    font-size: 0.875rem;
    color: var(--color-text-primary);
    font-weight: 600;
    text-align: center;
  }
}

.color-descriptions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.color-description {
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.5rem;
  border-left: 4px solid #3b82f6;

  h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.5rem;
  }

  p {
    font-size: 0.875rem;
    color: #64748b;
    line-height: 1.5;
    margin: 0;
  }
}

.usage-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.usage-example {
  padding: 1.5rem;
  background: #f8fafc;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;

  h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
  }

  .example-content {
    display: flex;
    align-items: center;
    gap: 1rem;

    span {
      font-size: 0.875rem;
      color: #64748b;
    }
  }
}
