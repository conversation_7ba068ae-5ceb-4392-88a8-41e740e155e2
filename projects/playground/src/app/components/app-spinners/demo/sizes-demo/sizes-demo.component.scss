.demo-container {
  max-width: 840px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.demo-header {
  margin-bottom: 3rem;
  text-align: center;

  h2 {
    font-size: 2.5rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 1rem;
  }

  p {
    font-size: 1.1rem;
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 2rem;
  }

  .copy-button {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s ease;

    &:hover {
      background: #2563eb;
    }
  }
}

.demo-content {
  .demo-section {
    margin-bottom: 4rem;
    padding: 2rem;
    border-radius: 0.75rem;

    h3 {
      font-size: 1.5rem;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 0.5rem;
    }

    p {
      font-size: 0.95rem;
      color: #64748b;
      margin-bottom: 2rem;
      line-height: 1.5;
    }
  }
}

.size-row {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 3rem;
  margin-bottom: 2rem;

  @media (max-width: 768px) {
    flex-direction: column;
    gap: 2rem;
  }
}

.size-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  border-radius: 0.5rem;
  min-width: 120px;

  .size-label {
    font-size: 0.875rem;
    color: var(--color-text-primary);
    font-weight: 600;
    text-align: center;
  }
}

.size-guidelines {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.size-guideline {
  padding: 1rem;
  border-radius: 0.5rem;

  h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--color-text-primary);
    margin-bottom: 0.5rem;
  }

  p {
    font-size: 0.875rem;
    color: #64748b;
    line-height: 1.5;
    margin: 0;
  }
}

.size-comparison {
  .comparison-row {
    display: flex;
    align-items: center;
    gap: 2rem;
    padding: 1rem;

    &:last-child {
      border-bottom: none;
    }

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 1rem;
      text-align: center;
    }
  }

  .size-label {
    font-size: 0.875rem;
    color: #475569;
    font-weight: 600;
    min-width: 60px;
  }

  .spinner-group {
    display: flex;
    gap: 2rem;
    align-items: center;

    @media (max-width: 768px) {
      gap: 1.5rem;
    }
  }
}
