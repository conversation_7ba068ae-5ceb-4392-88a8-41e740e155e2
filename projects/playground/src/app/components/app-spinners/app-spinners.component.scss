.spinner-demo-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 4rem;
}

.demo-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 3rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);

  .doc-header {
    text-align: center;

    h1 {
      font-size: 3rem;
      font-weight: 700;
      color: #1e293b;
      margin-bottom: 1rem;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .description {
      font-size: 1.2rem;
      color: #64748b;
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;
    }
  }
}

.demo-navigation {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 2rem 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);

  .nav-links {
    h3 {
      font-size: 1.5rem;
      font-weight: 600;
      color: #1e293b;
      margin-bottom: 1.5rem;
      text-align: center;
    }

    .nav-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      max-width: 1000px;
      margin: 0 auto;

      @media (max-width: 768px) {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      }
    }

    .nav-link {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 1.5rem;
      background: rgba(255, 255, 255, 0.8);
      border-radius: 1rem;
      text-decoration: none;
      color: #1e293b;
      transition: all 0.3s ease;
      border: 1px solid rgba(255, 255, 255, 0.3);
      backdrop-filter: blur(10px);

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        background: rgba(255, 255, 255, 0.95);
        text-decoration: none;
        color: #1e293b;
      }

      .nav-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
      }

      .nav-text {
        font-weight: 600;
        font-size: 0.9rem;
      }
    }
  }
}

.demo-sections {
  padding: 4rem 0;

  .demo-section {
    margin-bottom: 4rem;
    padding: 3rem 0;

    &:last-child {
      margin-bottom: 0;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 1rem;
    }

    .section-header {
      text-align: center;
      margin-bottom: 3rem;

      h2 {
        font-size: 2.5rem;
        font-weight: 700;
        color: white;
        margin-bottom: 1rem;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
      }

      p {
        font-size: 1.1rem;
        color: rgba(255, 255, 255, 0.9);
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
      }
    }

    .demo-content {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 1.5rem;
      padding: 2rem;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);

      .row {
        justify-content: center;
        align-items: center;
        min-height: 120px;
      }

      h4 {
        font-size: 1.1rem;
        font-weight: 600;
        color: #1e293b;
        margin-bottom: 1rem;
        text-align: center;
      }
    }
  }

  // Background variations for different sections
  .basic-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .types-bg {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  }

  .sizes-bg {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  }

  .colors-bg {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  }

  .animation-bg {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .spinner-demo-page {
    .demo-header {
      padding: 2rem 0;

      .doc-header h1 {
        font-size: 2rem;
      }

      .doc-header .description {
        font-size: 1rem;
      }
    }

    .demo-navigation {
      padding: 1.5rem 0;

      .nav-links .nav-grid {
        gap: 0.75rem;
      }
    }

    .demo-sections {
      padding: 2rem 0;

      .demo-section {
        padding: 2rem 0;

        .section-header h2 {
          font-size: 2rem;
        }

        .demo-content {
          padding: 1.5rem;
        }
      }
    }
  }
}
