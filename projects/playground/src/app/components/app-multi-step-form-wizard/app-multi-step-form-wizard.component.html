<div class="multi-step-form-wizard-demo">
  <div class="demo-header">
    <h2>Multi-Step Form Wizard Component</h2>
    <p>
      This component demonstrates a comprehensive form wizard with step
      navigation, validation, and progress tracking.
    </p>
  </div>

  <!-- Navigation Links to Demo Sections -->
  <div class="demo-navigation">
    <div class="container">
      <div class="row">
        <div class="col-12">
          <div class="nav-links">
            <h3>Demo Sections</h3>
            <div class="nav-grid">
              <a
                routerLink="/multi-step-form-wizard/basic-usage"
                class="nav-link"
              >
                <span class="nav-icon">📝</span>
                <span class="nav-text">Basic Usage</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="demo-controls-card">
    <ava-card>
      <div header>
        <h3>Demo Controls</h3>
      </div>
      <div content>
        <div class="controls-grid">
          <div class="control-group">
            <h4>Theme</h4>
            <div class="button-group">
              <ava-button
                (click)="changeTheme('default')"
                [variant]="
                  wizardConfig.theme === 'default' ? 'primary' : 'secondary'
                "
                size="small"
                label="Default"
              >
              </ava-button>
              <ava-button
                (click)="changeTheme('minimal')"
                [variant]="
                  wizardConfig.theme === 'minimal' ? 'primary' : 'secondary'
                "
                size="small"
                label="Minimal"
              >
              </ava-button>
              <ava-button
                (click)="changeTheme('modern')"
                [variant]="
                  wizardConfig.theme === 'modern' ? 'primary' : 'secondary'
                "
                size="small"
                label="Modern"
              >
              </ava-button>
            </div>
          </div>

          <div class="control-group">
            <h4>Layout</h4>
            <div class="button-group">
              <ava-button
                (click)="changeLayout('horizontal')"
                [variant]="
                  wizardConfig.stepLayout === 'horizontal'
                    ? 'primary'
                    : 'secondary'
                "
                size="small"
                label="Horizontal"
              >
              </ava-button>
              <ava-button
                (click)="changeLayout('vertical')"
                [variant]="
                  wizardConfig.stepLayout === 'vertical'
                    ? 'primary'
                    : 'secondary'
                "
                size="small"
                label="Vertical"
              >
              </ava-button>
              <ava-button
                (click)="changeLayout('tabs')"
                [variant]="
                  wizardConfig.stepLayout === 'tabs' ? 'primary' : 'secondary'
                "
                size="small"
                label="Tabs"
              >
              </ava-button>
            </div>
          </div>

          <div class="control-group">
            <h4>Actions</h4>
            <div class="button-group">
              <ava-button
                (click)="toggleLoading()"
                [variant]="loading ? 'warning' : 'primary'"
                size="small"
                [label]="loading ? 'Stop Loading' : 'Start Loading'"
                [iconName]="loading ? 'pause' : 'play'"
                [iconSize]="14"
              >
              </ava-button>
              <ava-button
                (click)="toggleDisabled()"
                [variant]="disabled ? 'success' : 'secondary'"
                size="small"
                [label]="disabled ? 'Enable' : 'Disable'"
                [iconName]="disabled ? 'unlock' : 'lock'"
                [iconSize]="14"
              >
              </ava-button>
              <ava-button
                (click)="toggleAutoSave()"
                [variant]="wizardConfig.autoSave ? 'success' : 'secondary'"
                size="small"
                [label]="
                  wizardConfig.autoSave ? 'Auto Save On' : 'Auto Save Off'
                "
                [iconName]="wizardConfig.autoSave ? 'save' : 'save'"
                [iconSize]="14"
              >
              </ava-button>
            </div>
          </div>
        </div>
      </div>
    </ava-card>
  </div>

  <div class="demo-section">
    <ava-card>
      <div header>
        <h3>Interactive Form Wizard</h3>
        <p>Try navigating through the steps and filling out the form</p>
      </div>
      <div content>
        <ava-multi-step-form-wizard
          [config]="wizardConfig"
          [steps]="formSteps"
          [loading]="loading"
          [disabled]="disabled"
          (stepChange)="onStepChange($event)"
          (stepComplete)="onStepComplete($event)"
          (stepValidate)="onStepValidate($event)"
          (formSubmit)="onFormSubmit($event)"
          (formReset)="onFormReset()"
          (formSave)="onFormSave($event)"
          (formCancel)="onFormCancel()"
          (wizardEvent)="onWizardEvent($event)"
        >
        </ava-multi-step-form-wizard>
      </div>
    </ava-card>
  </div>

  <div class="event-log-card" *ngIf="lastEvent">
    <ava-card>
      <div header>
        <h3>Event Log</h3>
        <p>Real-time events from the wizard</p>
      </div>
      <div content>
        <div class="event-content">
          <pre>{{ lastEvent | json }}</pre>
        </div>
      </div>
    </ava-card>
  </div>

  <div class="form-data-card" *ngIf="getFormDataKeys().length > 0">
    <ava-card>
      <div header>
        <h3>Form Data</h3>
        <p>Collected data from completed steps</p>
      </div>
      <div content>
        <div class="form-data-content">
          <pre>{{ formData | json }}</pre>
        </div>
      </div>
    </ava-card>
  </div>

  <div class="features-card">
    <ava-card>
      <div header>
        <h3>Features Demonstrated</h3>
        <p>Comprehensive list of capabilities</p>
      </div>
      <div content>
        <div class="features-grid">
          <div class="feature-item">
            <ava-icon
              iconName="list"
              [iconSize]="20"
              iconColor="#007bff"
            ></ava-icon>
            <div class="feature-content">
              <strong>Step Navigation:</strong> Navigate between steps with
              validation
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="check-circle"
              [iconSize]="20"
              iconColor="#28a745"
            ></ava-icon>
            <div class="feature-content">
              <strong>Form Validation:</strong> Real-time validation with custom
              rules
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="chart-bar"
              [iconSize]="20"
              iconColor="#ffc107"
            ></ava-icon>
            <div class="feature-content">
              <strong>Progress Tracking:</strong> Visual progress bar and step
              indicators
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="save"
              [iconSize]="20"
              iconColor="#dc3545"
            ></ava-icon>
            <div class="feature-content">
              <strong>Auto Save:</strong> Automatic form data persistence
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="edit"
              [iconSize]="20"
              iconColor="#6c757d"
            ></ava-icon>
            <div class="feature-content">
              <strong>Multiple Themes:</strong> Default, minimal, and modern
              themes
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="layout"
              [iconSize]="20"
              iconColor="#17a2b8"
            ></ava-icon>
            <div class="feature-content">
              <strong>Layout Options:</strong> Horizontal, vertical, and tab
              layouts
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="shield"
              [iconSize]="20"
              iconColor="#fd7e14"
            ></ava-icon>
            <div class="feature-content">
              <strong>Field Types:</strong> Text, email, password, select,
              checkbox, radio, textarea
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="alert-triangle"
              [iconSize]="20"
              iconColor="#6f42c1"
            ></ava-icon>
            <div class="feature-content">
              <strong>Error Handling:</strong> Comprehensive error display and
              validation
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="smartphone"
              [iconSize]="20"
              iconColor="#e83e8c"
            ></ava-icon>
            <div class="feature-content">
              <strong>Responsive Design:</strong> Adapts to different screen
              sizes
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="settings"
              [iconSize]="20"
              iconColor="#20c997"
            ></ava-icon>
            <div class="feature-content">
              <strong>Configurable:</strong> Highly customizable with extensive
              options
            </div>
          </div>
        </div>
      </div>
    </ava-card>
  </div>
</div>
