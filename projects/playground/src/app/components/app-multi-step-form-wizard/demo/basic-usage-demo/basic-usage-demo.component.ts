import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  MultiStepFormWizardComponent,
  WizardConfig,
  FormStep,
  WizardEvent,
} from '../../../../../../../play-comp-library/src/lib/composite-components/multi-step-form-wizard/multi-step-form-wizard.component';
import { CardComponent } from '../../../../../../../play-comp-library/src/lib/components/card/card.component';

@Component({
  selector: 'ava-multi-step-form-wizard-basic-usage-demo',
  standalone: true,
  imports: [CommonModule, MultiStepFormWizardComponent, CardComponent],
  templateUrl: './basic-usage-demo.component.html',
  styleUrl: './basic-usage-demo.component.scss',
})
export class BasicUsageDemoComponent {
  wizardConfig: WizardConfig = {
    title: 'User Registration',
    description: 'Complete your registration in 3 simple steps',
    showProgress: true,
    showStepNumbers: true,
    allowStepNavigation: true,
    validateOnStepChange: true,
    theme: 'default',
    stepLayout: 'horizontal',
    buttonPosition: 'bottom',
    showResetButton: true,
  };

  formSteps: FormStep[] = [
    {
      id: 'personal-info',
      title: 'Personal Information',
      description: 'Enter your basic details',
      icon: 'user',
      required: true,
      fields: [
        {
          id: 'firstName',
          type: 'text',
          label: 'First Name',
          placeholder: 'Enter your first name',
          required: true,
          minLength: 2,
          maxLength: 50,
        },
        {
          id: 'lastName',
          type: 'text',
          label: 'Last Name',
          placeholder: 'Enter your last name',
          required: true,
          minLength: 2,
          maxLength: 50,
        },
        {
          id: 'email',
          type: 'email',
          label: 'Email Address',
          placeholder: 'Enter your email address',
          required: true,
        },
      ],
    },
    {
      id: 'account-details',
      title: 'Account Details',
      description: 'Set up your account credentials',
      icon: 'lock',
      required: true,
      fields: [
        {
          id: 'username',
          type: 'text',
          label: 'Username',
          placeholder: 'Choose a username',
          required: true,
          minLength: 3,
          maxLength: 20,
        },
        {
          id: 'password',
          type: 'password',
          label: 'Password',
          placeholder: 'Enter your password',
          required: true,
          minLength: 8,
        },
        {
          id: 'confirmPassword',
          type: 'password',
          label: 'Confirm Password',
          placeholder: 'Confirm your password',
          required: true,
          validationRules: [
            {
              type: 'custom',
              message: 'Passwords do not match',
              validator: (value: unknown) => {
                const confirmPassword = value as string;
                const password = this.formData['password'] as string;
                // Only validate if both passwords have values
                if (!confirmPassword || !password) {
                  return true; // Let required validation handle empty fields
                }
                return confirmPassword === password;
              },
            },
          ],
        },
      ],
    },
    {
      id: 'preferences',
      title: 'Preferences',
      description: 'Customize your experience',
      icon: 'settings',
      required: false,
      fields: [
        {
          id: 'newsletter',
          type: 'checkbox',
          label: 'Subscribe to newsletter',
          value: false,
        },
        {
          id: 'theme',
          type: 'select',
          label: 'Preferred Theme',
          placeholder: 'Select your preferred theme',
          options: [
            { value: 'light', label: 'Light Theme' },
            { value: 'dark', label: 'Dark Theme' },
            { value: 'auto', label: 'Auto (System)' },
          ],
          value: 'light',
        },
      ],
    },
  ];

  loading = false;
  disabled = false;
  lastEvent: WizardEvent | null = null;
  formData: Record<string, unknown> = {};

  onStepChange(event: {
    stepId: string;
    stepIndex: number;
    direction: 'next' | 'prev';
  }) {
    console.log('Step changed:', event);
    this.lastEvent = {
      type: 'step-change',
      data: event,
      stepId: event.stepId,
      currentStep: event.stepIndex + 1,
      totalSteps: this.formSteps.length,
    };
  }

  onStepComplete(event: {
    stepId: string;
    stepIndex: number;
    data: Record<string, unknown>;
  }) {
    console.log('Step completed:', event);
    this.formData = { ...this.formData, ...event.data };
    this.lastEvent = {
      type: 'step-complete',
      data: event,
      stepId: event.stepId,
      currentStep: event.stepIndex + 1,
      totalSteps: this.formSteps.length,
    };
  }

  onStepValidate(event: {
    stepId: string;
    stepIndex: number;
    isValid: boolean;
    errors: string[];
  }) {
    console.log('Step validated:', event);
    this.lastEvent = {
      type: 'step-validate',
      data: event,
      stepId: event.stepId,
      currentStep: event.stepIndex + 1,
      totalSteps: this.formSteps.length,
    };
  }

  onFormSubmit(data: Record<string, unknown>) {
    console.log('Form submitted:', data);
    this.formData = { ...this.formData, ...data };
    this.lastEvent = {
      type: 'form-submit',
      data: data,
      currentStep: this.formSteps.length,
      totalSteps: this.formSteps.length,
    };
    alert('Registration completed successfully!');
  }

  onFormReset() {
    console.log('Form reset');
    this.formData = {};
    this.lastEvent = {
      type: 'form-reset',
      data: {},
      currentStep: 1,
      totalSteps: this.formSteps.length,
    };
  }

  onWizardEvent(event: WizardEvent) {
    console.log('Wizard event:', event);

    // Update form data on form-save events to keep validation in sync
    if (event.type === 'form-save' && event.data) {
      this.formData = {
        ...this.formData,
        ...(event.data as Record<string, unknown>),
      };
    }
  }

  private getFormData(): Record<string, unknown> {
    return this.formData;
  }

  getFormDataKeys(): string[] {
    return Object.keys(this.formData);
  }
}
