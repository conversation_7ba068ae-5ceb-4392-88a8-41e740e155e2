.basic-usage-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;

  .demo-header {
    text-align: center;
    margin-bottom: 3rem;

    h2 {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: 1rem;
    }

    p {
      font-size: 1.1rem;
      color: var(--text-secondary);
      max-width: 600px;
      margin: 0 auto;
      line-height: 1.6;
    }
  }

  .demo-section {
    margin-bottom: 2rem;

    ava-card {
      display: block;
    }
  }

  .event-log-card,
  .form-data-card {
    margin-bottom: 2rem;

    .event-content,
    .form-data-content {
      background: var(--surface-secondary);
      border-radius: 8px;
      padding: 1rem;
      overflow-x: auto;

      pre {
        margin: 0;
        font-size: 0.875rem;
        line-height: 1.5;
        color: var(--text-primary);
        white-space: pre-wrap;
        word-break: break-word;
      }
    }
  }

  .features-card {
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
      margin-top: 1rem;

      .feature-item {
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        padding: 1.5rem;
        background: var(--surface-secondary);
        border-radius: 12px;
        border: 1px solid var(--border-color);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
          border-color: var(--primary-color);
        }

        .feature-icon {
          font-size: 2rem;
          flex-shrink: 0;
          margin-top: 0.25rem;
        }

        .feature-content {
          flex: 1;

          strong {
            display: block;
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
          }

          color: var(--text-secondary);
          line-height: 1.5;
        }
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    padding: 1rem;

    .demo-header {
      h2 {
        font-size: 2rem;
      }

      p {
        font-size: 1rem;
      }
    }

    .features-card {
      .features-grid {
        grid-template-columns: 1fr;
        gap: 1rem;

        .feature-item {
          padding: 1rem;

          .feature-icon {
            font-size: 1.5rem;
          }
        }
      }
    }
  }

  @media (max-width: 480px) {
    .demo-header {
      h2 {
        font-size: 1.75rem;
      }
    }

    .features-card {
      .features-grid {
        .feature-item {
          flex-direction: column;
          text-align: center;
          gap: 0.75rem;

          .feature-icon {
            margin-top: 0;
          }
        }
      }
    }
  }
}
