import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import {
  MultiStepFormWizardComponent,
  WizardConfig,
  FormStep,
  WizardEvent,
} from '../../../../../play-comp-library/src/lib/composite-components/multi-step-form-wizard/multi-step-form-wizard.component';
import { CardComponent } from '../../../../../play-comp-library/src/lib/components/card/card.component';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';
import { IconComponent } from '../../../../../play-comp-library/src/lib/components/icon/icon.component';

@Component({
  selector: 'ava-multi-step-form-wizard-demo',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MultiStepFormWizardComponent,
    CardComponent,
    ButtonComponent,
    IconComponent,
  ],
  templateUrl: './app-multi-step-form-wizard.component.html',
  styleUrl: './app-multi-step-form-wizard.component.scss',
})
export class AppMultiStepFormWizardComponent {
  wizardConfig: WizardConfig = {
    title: 'User Registration Wizard',
    description: 'Complete your registration in 4 simple steps',
    showProgress: true,
    showStepNumbers: true,
    allowStepNavigation: true,
    validateOnStepChange: true,
    autoSave: true,
    saveInterval: 10000,
    theme: 'default',
    stepLayout: 'horizontal',
    buttonPosition: 'bottom',
    showCancelButton: true,
    showResetButton: true,
    confirmBeforeExit: true,
  };

  formSteps: FormStep[] = [
    {
      id: 'personal-info',
      title: 'Personal Information',
      description: 'Enter your basic personal details',
      icon: 'user',
      required: true,
      fields: [
        {
          id: 'firstName',
          type: 'text',
          label: 'First Name',
          placeholder: 'Enter your first name',
          required: true,
          minLength: 2,
          maxLength: 50,
        },
        {
          id: 'lastName',
          type: 'text',
          label: 'Last Name',
          placeholder: 'Enter your last name',
          required: true,
          minLength: 2,
          maxLength: 50,
        },
        {
          id: 'email',
          type: 'email',
          label: 'Email Address',
          placeholder: 'Enter your email address',
          required: true,
        },
        {
          id: 'phone',
          type: 'text',
          label: 'Phone Number',
          placeholder: 'Enter your phone number',
          pattern: '^[+]?[0-9\\s\\-\\(\\)]{10,}$',
          errorMessage: 'Please enter a valid phone number',
        },
      ],
    },
    {
      id: 'account-details',
      title: 'Account Details',
      description: 'Set up your account credentials',
      icon: 'lock',
      required: true,
      fields: [
        {
          id: 'username',
          type: 'text',
          label: 'Username',
          placeholder: 'Choose a username',
          required: true,
          minLength: 3,
          maxLength: 20,
          pattern: '^[a-zA-Z0-9_]+$',
          errorMessage:
            'Username can only contain letters, numbers, and underscores',
        },
        {
          id: 'password',
          type: 'password',
          label: 'Password',
          placeholder: 'Enter your password',
          required: true,
          minLength: 8,
          validationRules: [
            {
              type: 'custom',
              message:
                'Password must contain at least one uppercase letter, one lowercase letter, and one number',
              validator: (value: unknown) => {
                const password = value as string;
                return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(password);
              },
            },
          ],
        },
        {
          id: 'confirmPassword',
          type: 'password',
          label: 'Confirm Password',
          placeholder: 'Confirm your password',
          required: true,
          validationRules: [
            {
              type: 'custom',
              message: 'Passwords do not match',
              validator: (value: unknown) => {
                const confirmPassword = value as string;
                const password = this.getFormData()['password'] as string;
                return confirmPassword === password;
              },
            },
          ],
        },
      ],
    },
    {
      id: 'preferences',
      title: 'Preferences',
      description: 'Customize your experience',
      icon: 'settings',
      required: false,
      fields: [
        {
          id: 'newsletter',
          type: 'checkbox',
          label: 'Subscribe to newsletter',
          value: false,
        },
        {
          id: 'notifications',
          type: 'checkbox',
          label: 'Enable email notifications',
          value: true,
        },
        {
          id: 'theme',
          type: 'select',
          label: 'Preferred Theme',
          placeholder: 'Select your preferred theme',
          options: [
            { value: 'light', label: 'Light Theme' },
            { value: 'dark', label: 'Dark Theme' },
            { value: 'auto', label: 'Auto (System)' },
          ],
          value: 'light',
        },
        {
          id: 'language',
          type: 'select',
          label: 'Language',
          placeholder: 'Select your language',
          options: [
            { value: 'en', label: 'English' },
            { value: 'es', label: 'Spanish' },
            { value: 'fr', label: 'French' },
            { value: 'de', label: 'German' },
          ],
          value: 'en',
        },
      ],
    },
    {
      id: 'verification',
      title: 'Verification',
      description: 'Verify your information and complete registration',
      icon: 'check-circle',
      required: true,
      fields: [
        {
          id: 'terms',
          type: 'checkbox',
          label: 'I agree to the Terms and Conditions',
          required: true,
          validationRules: [
            {
              type: 'custom',
              message: 'You must agree to the terms and conditions',
              validator: (value: unknown) => value === true,
            },
          ],
        },
        {
          id: 'privacy',
          type: 'checkbox',
          label: 'I agree to the Privacy Policy',
          required: true,
          validationRules: [
            {
              type: 'custom',
              message: 'You must agree to the privacy policy',
              validator: (value: unknown) => value === true,
            },
          ],
        },
        {
          id: 'marketing',
          type: 'checkbox',
          label: 'I agree to receive marketing communications',
          value: false,
        },
        {
          id: 'comments',
          type: 'textarea',
          label: 'Additional Comments',
          placeholder: 'Any additional comments or special requests',
          maxLength: 500,
        },
      ],
    },
  ];

  loading = false;
  disabled = false;
  lastEvent: WizardEvent | null = null;
  formData: Record<string, unknown> = {};

  onStepChange(event: {
    stepId: string;
    stepIndex: number;
    direction: 'next' | 'prev';
  }) {
    console.log('Step change:', event);
    this.lastEvent = {
      type: 'step-change',
      data: event,
      stepId: event.stepId,
      currentStep: event.stepIndex + 1,
      totalSteps: this.formSteps.length,
    };
  }

  onStepComplete(event: {
    stepId: string;
    stepIndex: number;
    data: Record<string, unknown>;
  }) {
    console.log('Step complete:', event);
    this.formData = { ...this.formData, ...event.data };
    this.lastEvent = {
      type: 'step-complete',
      data: event,
      stepId: event.stepId,
      currentStep: event.stepIndex + 1,
      totalSteps: this.formSteps.length,
    };
  }

  onStepValidate(event: {
    stepId: string;
    stepIndex: number;
    isValid: boolean;
    errors: string[];
  }) {
    console.log('Step validate:', event);
    this.lastEvent = {
      type: 'step-validate',
      data: event,
      stepId: event.stepId,
      currentStep: event.stepIndex + 1,
      totalSteps: this.formSteps.length,
    };
  }

  onFormSubmit(data: Record<string, unknown>) {
    console.log('Form submit:', data);
    this.loading = true;

    // Simulate API call
    setTimeout(() => {
      this.loading = false;
      alert('Registration completed successfully!');
      this.lastEvent = {
        type: 'form-submit',
        data,
        currentStep: this.formSteps.length,
        totalSteps: this.formSteps.length,
      };
    }, 2000);
  }

  onFormReset() {
    console.log('Form reset');
    this.formData = {};
    this.lastEvent = {
      type: 'form-reset',
      data: {},
      currentStep: 1,
      totalSteps: this.formSteps.length,
    };
  }

  onFormSave(data: Record<string, unknown>) {
    console.log('Form save:', data);
    this.formData = { ...this.formData, ...data };
    this.lastEvent = {
      type: 'form-save',
      data,
      currentStep: 1,
      totalSteps: this.formSteps.length,
    };
  }

  onFormCancel() {
    console.log('Form cancel');
    this.lastEvent = {
      type: 'form-cancel',
      data: {},
      currentStep: 1,
      totalSteps: this.formSteps.length,
    };
  }

  onWizardEvent(event: WizardEvent) {
    console.log('Wizard event:', event);
    this.lastEvent = event;
  }

  toggleLoading() {
    this.loading = !this.loading;
  }

  toggleDisabled() {
    this.disabled = !this.disabled;
  }

  changeTheme(theme: 'default' | 'minimal' | 'modern') {
    this.wizardConfig = { ...this.wizardConfig, theme };
  }

  changeLayout(layout: 'horizontal' | 'vertical' | 'tabs') {
    this.wizardConfig = { ...this.wizardConfig, stepLayout: layout };
  }

  toggleAutoSave() {
    this.wizardConfig = {
      ...this.wizardConfig,
      autoSave: !this.wizardConfig.autoSave,
    };
  }

  private getFormData(): Record<string, unknown> {
    return this.formData;
  }

  getFormDataKeys(): string[] {
    return Object.keys(this.formData);
  }
}
