.demo-container {
  max-width: 840px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.demo-section {
  margin-bottom: 3rem;

  h3 {
    color: var(--text-primary, #1e293b);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  p {
    color: var(--text-secondary, #64748b);
    line-height: 1.6;
    margin-bottom: 1.5rem;
  }
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}

.color-item {
  padding: 1.5rem;

  .color-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;

    h4 {
      color: var(--text-primary, #1e293b);
      font-size: 1.1rem;
      font-weight: 600;
      margin: 0;
    }

    .color-preview {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      border: 2px solid var(--surface-border, #e2e8f0);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  p {
    color: var(--text-secondary, #475569);
    line-height: 1.6;
    margin-bottom: 1rem;
    font-size: 0.9rem;
  }

  .link-demo {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60px;
  }
}

.guidelines {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 1.5rem 0;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .guideline {
    padding: 1.5rem;

    h4 {
      color: var(--text-primary, #1e293b);
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 1rem;
    }

    p {
      color: var(--text-secondary, #475569);
      line-height: 1.6;
      margin-bottom: 1rem;
      font-size: 0.9rem;
    }

    ul {
      color: var(--text-secondary, #475569);
      line-height: 1.6;
      padding-left: 1.5rem;
      margin: 0;

      li {
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
      }
    }
  }
}

.accessibility-notes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin: 1.5rem 0;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }

  .note {
    padding: 1.5rem;

    h4 {
      color: var(--text-primary, #1e293b);
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 1rem;
    }

    p {
      color: var(--text-secondary, #475569);
      line-height: 1.6;
      margin: 0;
      font-size: 0.9rem;
    }
  }
}
