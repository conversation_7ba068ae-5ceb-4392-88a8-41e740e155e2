import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LinkComponent } from '../../../../../../play-comp-library/src/public-api';

interface ColorExample {
  title: string;
  description: string;
  label: string;
  color: string;
  category: string;
}

@Component({
  selector: 'ava-actionlinks-colors',
  standalone: true,
  imports: [CommonModule, LinkComponent],
  templateUrl: './actionlinks-colors.component.html',
  styleUrls: ['./actionlinks-colors.component.scss'],
})
export class ActionlinksColorsComponent {
  semanticColors: ColorExample[] = [
    {
      title: 'Primary',
      description: 'Brand primary color for main actions.',
      label: 'Primary Action',
      color: 'primary',
      category: 'semantic',
    },
    {
      title: 'Success',
      description: 'Green color for positive actions.',
      label: 'Success Action',
      color: 'success',
      category: 'semantic',
    },
    {
      title: 'Warning',
      description: 'Orange color for cautionary actions.',
      label: 'Warning Action',
      color: 'warning',
      category: 'semantic',
    },
    {
      title: 'Danger',
      description: 'Red color for destructive actions.',
      label: 'Danger Action',
      color: 'danger',
      category: 'semantic',
    },
    {
      title: 'Info',
      description: 'Blue color for informational actions.',
      label: 'Info Action',
      color: 'info',
      category: 'semantic',
    },
    {
      title: 'Default',
      description: 'Default color for general navigation.',
      label: 'Default Action',
      color: 'default',
      category: 'semantic',
    },
  ];

  customColors: ColorExample[] = [
    {
      title: 'Purple',
      description: 'Custom purple hex color.',
      label: 'Purple Link',
      color: '#7C3AED',
      category: 'custom',
    },
    {
      title: 'Teal',
      description: 'Custom teal hex color.',
      label: 'Teal Link',
      color: '#14B8A6',
      category: 'custom',
    },
    {
      title: 'Pink',
      description: 'Custom pink hex color.',
      label: 'Pink Link',
      color: '#EC4899',
      category: 'custom',
    },
    {
      title: 'Indigo',
      description: 'Custom indigo hex color.',
      label: 'Indigo Link',
      color: '#6366F1',
      category: 'custom',
    },
    {
      title: 'Amber',
      description: 'Custom amber hex color.',
      label: 'Amber Link',
      color: '#F59E0B',
      category: 'custom',
    },
    {
      title: 'Rose',
      description: 'Custom rose hex color.',
      label: 'Rose Link',
      color: '#F43F5E',
      category: 'custom',
    },
  ];

  colorGuidelines = [
    {
      title: 'Semantic Colors',
      description:
        'Use predefined semantic colors for consistent meaning across your application.',
      examples: [
        'Primary: Main brand actions and primary navigation',
        'Success: Positive actions like save, confirm, or complete',
        'Warning: Cautionary actions that require attention',
        'Danger: Destructive actions like delete or remove',
        'Info: Informational links and help content',
        'Default: General navigation and secondary actions',
      ],
    },
    {
      title: 'Custom Colors',
      description:
        'Use custom hex colors for brand-specific styling or special use cases.',
      examples: [
        'Brand colors that match your design system',
        'Special promotions or featured content',
        'Category-specific styling',
        'Accessibility considerations for color contrast',
        'Consistent with overall design language',
        'Limited use to maintain visual hierarchy',
      ],
    },
  ];

  onLinkClick(event: Event, linkInfo: ColorExample) {
    console.log('Color link clicked:', linkInfo);
    event.preventDefault();
    alert(`Demo: ${linkInfo.label} (${linkInfo.color}) clicked!`);
  }

  onLinkKeyPress(event: KeyboardEvent, linkInfo: ColorExample) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.onLinkClick(event, linkInfo);
    }
  }
}
