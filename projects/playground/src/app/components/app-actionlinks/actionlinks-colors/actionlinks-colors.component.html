<div class="demo-container">
  <div class="demo-section">
    <div class="color-grid">
      <div class="color-item" *ngFor="let color of semanticColors">
        <div class="link-demo">
          <ava-link
            [label]="color.label"
            [color]="color.color"
            (click)="onLinkClick($event, color)"
            (keydown.enter)="onLinkKeyPress($any($event), color)"
            (keydown.space)="onLinkKeyPress($any($event), color)"
            tabindex="0"
            role="button"
            [attr.aria-label]="color.label + ' link'"
          >
          </ava-link>
        </div>
      </div>
    </div>
  </div>
</div>
