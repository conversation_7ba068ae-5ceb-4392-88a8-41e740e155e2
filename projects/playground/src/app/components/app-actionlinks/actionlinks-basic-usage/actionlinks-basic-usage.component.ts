import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LinkComponent } from '../../../../../../play-comp-library/src/public-api';

interface LinkExample {
  title: string;
  description: string;
  label: string;
  color: string;
}

interface UseCase {
  title: string;
  description: string;
  examples: { label: string; color: string }[];
}

@Component({
  selector: 'ava-actionlinks-basic-usage',
  standalone: true,
  imports: [CommonModule, LinkComponent],
  templateUrl: './actionlinks-basic-usage.component.html',
  styleUrls: ['./actionlinks-basic-usage.component.scss'],
})
export class ActionlinksBasicUsageComponent {
  basicExamples: LinkExample[] = [
    {
      title: 'Primary Link',
      description: 'Standard primary action link with brand colors.',
      label: 'Get Started',
      color: 'primary',
    },
  ];

  useCases: UseCase[] = [
    {
      title: 'Navigation Links',
      description: 'Primary navigation and menu links.',
      examples: [
        { label: 'Home', color: 'primary' },
        { label: 'About', color: 'default' },
        { label: 'Contact', color: 'default' },
      ],
    },
    {
      title: 'Action Links',
      description: 'Interactive action links for user tasks.',
      examples: [
        { label: 'Download', color: 'success' },
        { label: 'Edit Profile', color: 'info' },
        { label: 'Delete Item', color: 'danger' },
      ],
    },
    {
      title: 'Status Links',
      description: 'Links that indicate status or state.',
      examples: [
        { label: 'View Details', color: 'info' },
        { label: 'Warning Notice', color: 'warning' },
        { label: 'Error Log', color: 'danger' },
      ],
    },
  ];

  onLinkClick(
    event: Event,
    linkInfo: LinkExample | { label: string; color: string }
  ) {
    console.log('Link clicked:', linkInfo);
    // Prevent default navigation for demo purposes
    event.preventDefault();

    // Show a demo message
    alert(`Demo: ${linkInfo.label} link clicked!`);
  }

  onLinkKeyPress(
    event: KeyboardEvent,
    linkInfo: LinkExample | { label: string; color: string }
  ) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      this.onLinkClick(event, linkInfo);
    }
  }
}
