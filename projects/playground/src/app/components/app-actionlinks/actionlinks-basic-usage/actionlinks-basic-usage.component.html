<div class="demo-container">
  <div class="demo-section">
    <div class="basic-examples">
      <div class="example-item" *ngFor="let example of basicExamples">
        <div class="link-demo">
          <ava-link
            [label]="example.label"
            [color]="example.color"
            (click)="onLinkClick($event, example)"
            (keydown.enter)="onLinkKeyPress($any($event), example)"
            (keydown.space)="onLinkKeyPress($any($event), example)"
            tabindex="0"
            role="button"
            [attr.aria-label]="example.label + ' link'"
          >
          </ava-link>
        </div>
      </div>
    </div>
  </div>
</div>
