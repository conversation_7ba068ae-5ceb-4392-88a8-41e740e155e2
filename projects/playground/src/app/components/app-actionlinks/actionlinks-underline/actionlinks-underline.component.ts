import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LinkComponent } from '../../../../../../play-comp-library/src/public-api';

interface UnderlineExample {
  title: string;
  description: string;
  label: string;
  underline: boolean;
  color: string;
}

@Component({
  selector: 'ava-actionlinks-underline',
  standalone: true,
  imports: [CommonModule, LinkComponent],
  template: `
    <div class="demo-container">
      <div class="demo-section">
        <div class="underline-examples">
          <div class="example-item" *ngFor="let example of underlineExamples">
            <div class="link-demo">
              <ava-link
                [label]="example.label"
                [color]="example.color"
                [underline]="example.underline"
                (click)="onLinkClick($event, example)"
                tabindex="0"
                role="button"
              >
              </ava-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
      }
      .demo-section {
        margin-bottom: 2rem;
      }
      .underline-examples {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;
      }
      .example-item {
      }
      .link-demo {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 60px;
      }
    `,
  ],
})
export class ActionlinksUnderlineComponent {
  underlineExamples: UnderlineExample[] = [
    {
      title: 'Default Link',
      description: 'Standard link without underline.',
      label: 'Default Link',
      underline: false,
      color: 'primary',
    },
    {
      title: 'Underlined Link',
      description: 'Link with underline for emphasis.',
      label: 'Underlined Link',
      underline: true,
      color: 'primary',
    },
  ];

  onLinkClick(event: Event, linkInfo: UnderlineExample) {
    console.log('Underline link clicked:', linkInfo);
    event.preventDefault();
    alert(`Demo: ${linkInfo.label} clicked!`);
  }
}
