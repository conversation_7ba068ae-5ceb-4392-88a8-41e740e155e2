import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LinkComponent } from '../../../../../../play-comp-library/src/public-api';

interface SizeExample {
  title: string;
  description: string;
  label: string;
  size: 'small' | 'medium' | 'large';
  useCase: string;
}

@Component({
  selector: 'ava-actionlinks-sizes',
  standalone: true,
  imports: [CommonModule, LinkComponent],
  template: `
    <div class="demo-container">
      <div class="demo-section">
        <div class="size-examples">
          <div class="example-item" *ngFor="let example of sizeExamples">
            <div class="link-demo">
              <ava-link
                [label]="example.label"
                [size]="example.size"
                color="primary"
                (click)="onLinkClick($event, example)"
                tabindex="0"
                role="button"
              >
              </ava-link>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
      }
      .demo-section {
        margin-bottom: 2rem;
      }
      .size-examples {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
        gap: 1.5rem;
        margin-top: 1.5rem;
      }
      .example-item {
        padding: 1rem;
      }
      .link-demo {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 60px;
        padding: 1rem;
      }
    `,
  ],
})
export class ActionlinksSizesComponent {
  sizeExamples: SizeExample[] = [
    {
      title: 'Small',
      description: 'Compact size for dense layouts and secondary actions.',
      label: 'Small Link',
      size: 'small',
      useCase: 'Secondary actions, footers, dense layouts',
    },
    {
      title: 'Medium',
      description: 'Standard size for most common use cases.',
      label: 'Medium Link',
      size: 'medium',
      useCase: 'Primary navigation, main actions, general use',
    },
    {
      title: 'Large',
      description: 'Prominent size for important actions and touch targets.',
      label: 'Large Link',
      size: 'large',
      useCase: 'Primary actions, mobile interfaces, accessibility',
    },
  ];

  onLinkClick(event: Event, linkInfo: SizeExample) {
    console.log('Size link clicked:', linkInfo);
    event.preventDefault();
    alert(`Demo: ${linkInfo.label} (${linkInfo.size}) clicked!`);
  }
}
