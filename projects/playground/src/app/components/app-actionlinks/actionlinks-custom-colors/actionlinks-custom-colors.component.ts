import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LinkComponent } from '../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-actionlinks-custom-colors',
  standalone: true,
  imports: [CommonModule, LinkComponent],
  template: `
    <div class="demo-container">
      <h3>Custom Colors Demo</h3>
      <p>This demo shows custom hex color usage for links.</p>
      <ava-link label="Custom Purple Link" color="#7C3AED"></ava-link>
    </div>
  `,
  styles: [
    `
      .demo-container {
        padding: 2rem;
        max-width: 800px;
        margin: 0 auto;
      }
    `,
  ],
})
export class ActionlinksCustomColorsComponent {}
