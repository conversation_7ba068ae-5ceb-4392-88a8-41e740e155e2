.theme-demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;
}

.demo-header {
  text-align: center;
  margin-bottom: 3rem;

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  p {
    font-size: 1.1rem;
    color: #64748b;
    margin: 0;
  }
}

.current-state,
.controls-section,
.navigation-section,
.sharing-section,
.test-section,
.docs-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 2rem;
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;

  h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    color: #1e293b;
  }

  h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #374151;
  }
}

.state-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.state-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;

  .label {
    font-weight: 600;
    color: #374151;
    min-width: 120px;
  }

  .value {
    padding: 0.25rem 0.75rem;
    border-radius: 6px;
    font-weight: 500;

    &.light {
      background: #dbeafe;
      color: #1e40af;
    }

    &.dark {
      background: #f3e8ff;
      color: #7c3aed;
    }

    &.has-theme {
      background: #dcfce7;
      color: #16a34a;
    }
  }

  .url {
    font-family: "SF Mono", Monaco, "Cascadia Code", monospace;
    font-size: 0.875rem;
    background: #f1f5f9;
    padding: 0.5rem;
    border-radius: 4px;
    border: 1px solid #cbd5e1;
    word-break: break-all;
    flex: 1;
  }
}

.control-group {
  margin-bottom: 2rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 1rem;

  &:last-child {
    margin-bottom: 0;
  }
}

.theme-btn,
.personality-btn,
.nav-btn,
.copy-btn,
.clear-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 44px;
}

.theme-btn {
  &.light {
    background: #dbeafe;
    color: #1e40af;
    border-color: #bfdbfe;

    &:hover {
      background: #bfdbfe;
    }

    &.active {
      background: #1e40af;
      color: white;
    }
  }

  &.dark {
    background: #f3e8ff;
    color: #7c3aed;
    border-color: #e9d5ff;

    &:hover {
      background: #e9d5ff;
    }

    &.active {
      background: #7c3aed;
      color: white;
    }
  }

  &.toggle {
    background: #fbbf24;
    color: #92400e;
    border-color: #fcd34d;

    &:hover {
      background: #fcd34d;
    }
  }
}

.personality-btn {
  &.minimal {
    background: #ecfdf5;
    color: #047857;
    border-color: #a7f3d0;

    &:hover {
      background: #d1fae5;
    }

    &.active {
      background: #047857;
      color: white;
    }
  }

  &.professional {
    background: #fef3c7;
    color: #92400e;
    border-color: #fde68a;

    &:hover {
      background: #fde68a;
    }

    &.active {
      background: #92400e;
      color: white;
    }
  }
}

.nav-btn {
  background: #f1f5f9;
  color: #475569;
  border-color: #cbd5e1;

  &:hover {
    background: #e2e8f0;
    color: #334155;
  }
}

.copy-btn {
  background: #10b981;
  color: white;
  border-color: #10b981;

  &:hover {
    background: #059669;
  }
}

.clear-btn {
  background: #ef4444;
  color: white;
  border-color: #ef4444;

  &:hover {
    background: #dc2626;
  }

  &:disabled {
    background: #9ca3af;
    border-color: #9ca3af;
    cursor: not-allowed;
    opacity: 0.6;
  }
}

.url-display {
  display: flex;
  gap: 1rem;
  align-items: center;
  margin-bottom: 1rem;

  .shareable-url {
    flex: 1;
    font-family: "SF Mono", Monaco, "Cascadia Code", monospace;
    font-size: 0.875rem;
    background: #f1f5f9;
    padding: 0.75rem;
    border-radius: 6px;
    border: 1px solid #cbd5e1;
    word-break: break-all;
  }
}

.test-links {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.test-link {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;

  code {
    flex: 1;
    font-family: "SF Mono", Monaco, "Cascadia Code", monospace;
    font-size: 0.875rem;
    word-break: break-all;
  }

  a {
    background: #3b82f6;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    min-width: 60px;
    text-align: center;

    &:hover {
      background: #2563eb;
    }
  }
}

.docs-content {
  ul {
    list-style-type: none;
    padding: 0;

    li {
      padding: 0.5rem 0;
      border-bottom: 1px solid #f1f5f9;

      &:last-child {
        border-bottom: none;
      }

      strong {
        color: #1e293b;
      }
    }
  }

  pre {
    background: #1e293b;
    color: #e2e8f0;
    padding: 1.5rem;
    border-radius: 8px;
    overflow-x: auto;
    font-family: "SF Mono", Monaco, "Cascadia Code", monospace;
    font-size: 0.875rem;
    line-height: 1.5;

    code {
      background: none;
      color: inherit;
      padding: 0;
    }
  }
}

// Dark theme styles
html[data-theme="dark"] {
  .theme-demo-container {
    color: #e2e8f0;
  }

  .current-state,
  .controls-section,
  .navigation-section,
  .sharing-section,
  .test-section,
  .docs-section {
    background: #1e293b;
    border-color: #374151;

    h2,
    h3 {
      color: #f1f5f9;
    }
  }

  .state-item {
    background: #374151;
    border-color: #4b5563;

    .label {
      color: #e2e8f0;
    }

    .url {
      background: #4b5563;
      border-color: #6b7280;
      color: #e2e8f0;
    }
  }

  .url-display .shareable-url {
    background: #374151;
    border-color: #4b5563;
    color: #e2e8f0;
  }

  .test-link {
    background: #374151;
    border-color: #4b5563;

    code {
      color: #e2e8f0;
    }
  }

  .docs-content {
    ul li {
      border-bottom-color: #374151;
      color: #d1d5db;

      strong {
        color: #f1f5f9;
      }
    }

    pre {
      background: #0f172a;
      border: 1px solid #374151;
    }
  }

  .demo-header p {
    color: #94a3b8;
  }
}

// Responsive design
@media (max-width: 768px) {
  .theme-demo-container {
    padding: 1rem;
  }

  .current-state,
  .controls-section,
  .navigation-section,
  .sharing-section,
  .test-section,
  .docs-section {
    padding: 1.5rem;
  }

  .state-grid {
    grid-template-columns: 1fr;
  }

  .button-group {
    flex-direction: column;
  }

  .url-display {
    flex-direction: column;
    align-items: stretch;
  }

  .test-link {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
}
