.glass-button-demo-page {
  .demo-header {
    background: #f8fafc;
    padding: 2rem 0 1rem 0;
    border-bottom: 1px solid #e5e7eb;
    .doc-header {
      h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
      }
      .description {
        color: #64748b;
        font-size: 1.1rem;
      }
    }
  }
  .demo-navigation {
    background: #fff;
    border-bottom: 1px solid #e5e7eb;
    padding: 1rem 0;
    .nav-links {
      h3 {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
      }
      .nav-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        .nav-link {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          padding: 0.5rem 1rem;
          border-radius: 6px;
          background: #f1f5f9;
          color: #334155;
          text-decoration: none;
          font-weight: 500;
          transition: background 0.2s;
          &:hover {
            background: #e0e7ef;
          }
          .nav-icon {
            font-size: 1.2rem;
          }
        }
      }
    }
  }
  .demo-sections {
    section.demo-section {
      background: url('/assets/glass_1.png') !important;
      background-size: cover !important;
      background-repeat: no-repeat !important;
      padding: 2rem 0 1.5rem 0;
      border-bottom: 1px solid #e5e7eb;
      .section-header {
        margin-bottom: 1.5rem;
        h2 {
          font-size: 1.5rem;
          font-weight: 600;
        }
        p {
          color: #64748b;
        }
      }
      .demo-content {
        display: flex;
        flex-wrap: wrap;
        gap: 1.5rem;
        align-items: center;
      }
      table.api-table {
        width: 100%;
        border-collapse: collapse;
        th, td {
          border: 1px solid #e5e7eb;
          padding: 0.5rem 0.75rem;
          text-align: left;
        }
        th {
          background: #f1f5f9;
          font-weight: 600;
        }
      }
    }
  }
} 