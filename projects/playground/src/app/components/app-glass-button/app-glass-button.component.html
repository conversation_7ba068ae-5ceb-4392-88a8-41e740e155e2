<div class="glass-button-demo-page">
  <!-- Header -->
  <div class="demo-header">
    <div class="container">
      <div class="row">
        <div class="col-12">
          <header class="doc-header">
            <h1>GlassButton Component</h1>
            <p class="description">
              A glass-only button component supporting only glass-medium,
              glass-strong, and glass-bold variants. All other features match
              the standard Button.
            </p>
          </header>
        </div>
      </div>
    </div>
  </div>

  <!-- Navigation Links to Demo Sections -->
  <div class="demo-navigation">
    <div class="container">
      <div class="row">
        <div class="col-12">
          <div class="nav-links">
            <h3>Demo Sections</h3>
            <div class="nav-grid">
              <a routerLink="/glass-button/variants" class="nav-link">
                <span class="nav-icon">🔮</span>
                <span class="nav-text">Glass Variants</span>
              </a>
              <a routerLink="/glass-button/sizes" class="nav-link">
                <span class="nav-icon">📏</span>
                <span class="nav-text">Sizes</span>
              </a>
              <a routerLink="/glass-button/effects" class="nav-link">
                <span class="nav-icon">✨</span>
                <span class="nav-text">Effects</span>
              </a>
              <a routerLink="/glass-button/shapes" class="nav-link">
                <span class="nav-icon">🔷</span>
                <span class="nav-text">Shapes</span>
              </a>
              <a routerLink="/glass-button/api" class="nav-link">
                <span class="nav-icon">📚</span>
                <span class="nav-text">API Reference</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Demo Sections -->
  <div class="demo-sections">
    <!-- Glass Variants -->
    <section class="demo-section">
      <div class="container">
        <div class="section-header">
          <h2>Glass Variants</h2>
          <p>Showcasing glass-medium, glass-strong, and glass-bold variants.</p>
        </div>
        <div class="demo-content row g-3 mb-4">
          <div class="col-12 col-sm-auto">
            <ava-glass-button
              label="Glass Medium"
              glassVariant="glass-medium"
              (userClick)="onButtonClick($event)"
            ></ava-glass-button>
          </div>
          <div class="col-12 col-sm-auto">
            <ava-glass-button
              label="Glass Strong"
              glassVariant="glass-strong"
              (userClick)="onButtonClick($event)"
            ></ava-glass-button>
          </div>
          <div class="col-12 col-sm-auto">
            <ava-glass-button
              label="Glass Bold"
              glassVariant="glass-bold"
              (userClick)="onButtonClick($event)"
            ></ava-glass-button>
          </div>
        </div>
      </div>
    </section>

    <!-- Sizes -->
    <section class="demo-section">
      <div class="container">
        <div class="section-header">
          <h2>Sizes</h2>
          <p>Small, medium, and large GlassButtons.</p>
        </div>
        <div class="demo-content row g-3 mb-4">
          <div class="col-12 col-sm-auto">
            <ava-glass-button
              label="Small"
              glassVariant="glass-strong"
              size="small"
            ></ava-glass-button>
          </div>
          <div class="col-12 col-sm-auto">
            <ava-glass-button
              label="Medium"
              glassVariant="glass-strong"
              size="medium"
            ></ava-glass-button>
          </div>
          <div class="col-12 col-sm-auto">
            <ava-glass-button
              label="Large"
              glassVariant="glass-strong"
              size="large"
            ></ava-glass-button>
          </div>
        </div>
      </div>
    </section>

    <!-- Effects -->
    <section class="demo-section">
      <div class="container">
        <div class="section-header">
          <h2>Effects</h2>
          <p>Hover, pressed, and processing effects.</p>
        </div>
        <div class="demo-content row g-3 mb-4">
          <div class="col-12 col-sm-auto">
            <ava-glass-button
              label="Torch Hover"
              glassVariant="glass-strong"
              hoverEffect="torch"
            ></ava-glass-button>
          </div>
          <div class="col-12 col-sm-auto">
            <ava-glass-button
              label="Glow Hover"
              glassVariant="glass-strong"
              hoverEffect="glow"
            ></ava-glass-button>
          </div>
          <div class="col-12 col-sm-auto">
            <ava-glass-button
              label="Tint Hover"
              glassVariant="glass-strong"
              hoverEffect="tint"
            ></ava-glass-button>
          </div>
          <div class="col-12 col-sm-auto">
            <ava-glass-button
              label="Ripple Pressed"
              glassVariant="glass-strong"
              pressedEffect="ripple"
            ></ava-glass-button>
          </div>
          <div class="col-12 col-sm-auto">
            <ava-glass-button
              label="Processing"
              glassVariant="glass-strong"
              [processing]="true"
            ></ava-glass-button>
          </div>
        </div>
      </div>
    </section>

    <!-- Shapes -->
    <section class="demo-section">
      <div class="container">
        <div class="section-header">
          <h2>Shapes</h2>
          <p>Pill and icon-only GlassButtons.</p>
        </div>
        <div class="demo-content row g-3 mb-4">
          <div class="col-12 col-sm-auto">
            <ava-glass-button
              label="Pill"
              glassVariant="glass-strong"
              [pill]="true"
            ></ava-glass-button>
          </div>
          <div class="col-12 col-sm-auto">
            <ava-glass-button
              iconName="star"
              iconPosition="only"
              glassVariant="glass-strong"
            ></ava-glass-button>
          </div>
        </div>
      </div>
    </section>

    <!-- Tone Variants -->
    <section class="demo-section">
      <div class="container">
        <div class="section-header">
          <h2>Tone Variants</h2>
          <p>
            Showcasing all tone options for GlassButton, with all glass
            intensities.
          </p>
        </div>
        <div class="demo-content row g-3 mb-4">
          <div class="col-12">
            <div *ngFor="let tone of tones" style="margin-bottom: 1rem">
              <strong
                style="
                  display: inline-block;
                  width: 90px;
                  text-transform: capitalize;
                "
                >{{ tone }}</strong
              >
              <ava-glass-button
                label="Medium"
                glassVariant="glass-medium"
                [tone]="tone"
              ></ava-glass-button>
              <ava-glass-button
                label="Strong"
                glassVariant="glass-strong"
                [tone]="tone"
              ></ava-glass-button>
              <ava-glass-button
                label="Bold"
                glassVariant="glass-bold"
                [tone]="tone"
              ></ava-glass-button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- API Reference -->
    <section class="demo-section">
      <div class="container">
        <div class="section-header">
          <h2>API Reference</h2>
        </div>
        <div class="demo-content">
          <table class="api-table">
            <thead>
              <tr>
                <th>Prop</th>
                <th>Type</th>
                <th>Default</th>
                <th>Description</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let prop of apiProps">
                <td>{{ prop.name }}</td>
                <td>{{ prop.type }}</td>
                <td>{{ prop.default }}</td>
                <td>{{ prop.description }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
  </div>
</div>
