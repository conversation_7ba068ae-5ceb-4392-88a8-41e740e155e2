import { Component, ViewEncapsulation } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { GlassButtonComponent } from '../../../../../play-comp-library/src/lib/components/glass-button/glass-button.component';
import { GlassButtonTone } from '../../../../../play-comp-library/src/lib/components/glass-button/glass-button.component';

interface GlassButtonDocSection {
  title: string;
  description: string;
  showCode: boolean;
}

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

@Component({
  selector: 'awe-app-glass-button',
  standalone: true,
  imports: [CommonModule, RouterModule, GlassButtonComponent],
  templateUrl: './app-glass-button.component.html',
  styleUrls: [
    './app-glass-button.component.scss',
    '../../../../../play-comp-library/src/lib/styles/grid.scss',
  ],
  encapsulation: ViewEncapsulation.None,
})
export class AppGlassButtonComponent {
  sections: GlassButtonDocSection[] = [
    {
      title: 'GlassButton Variants',
      description:
        'GlassButton supports only glass-medium, glass-strong, and glass-bold glass intensity variants. All other features match the standard Button.',
      showCode: false,
    },
    {
      title: 'Interaction States',
      description:
        'Default, processing, disabled, and focus states are supported as in the standard Button.',
      showCode: false,
    },
    {
      title: 'Button Sizes',
      description:
        'Available sizes: small, medium, and large, with responsive scaling.',
      showCode: false,
    },
    {
      title: 'Effects',
      description:
        'Hover, pressed, processing, focus, and disabled effects are all supported.',
      showCode: false,
    },
    {
      title: 'Shape Modifiers',
      description: 'Pill-shaped and icon-only buttons are supported.',
      showCode: false,
    },
  ];

  apiProps: ApiProperty[] = [
    {
      name: 'label',
      type: 'string',
      default: 'undefined',
      description: 'The text content of the button.',
    },
    {
      name: 'glassVariant',
      type: "'glass-medium' | 'glass-strong' | 'glass-bold'",
      default: 'glass-medium',
      description: 'The glass intensity level (surface opacity).',
    },
    {
      name: 'size',
      type: "'small' | 'medium' | 'large'",
      default: 'large',
      description: 'The size of the button.',
    },
    {
      name: 'hoverEffect',
      type: "'torch' | 'glow' | 'tint' | 'scale' | 'none'",
      default: 'torch',
      description: 'The hover effect.',
    },
    {
      name: 'pressedEffect',
      type: "'ripple' | 'inset' | 'solid' | 'none'",
      default: 'ripple',
      description: 'The pressed feedback effect.',
    },
    {
      name: 'processingEffect',
      type: "'pulse' | 'none'",
      default: 'pulse',
      description: 'The processing animation effect.',
    },
    {
      name: 'focusEffect',
      type: "'border' | 'none'",
      default: 'border',
      description: 'The focus indicator effect.',
    },
    {
      name: 'disabledEffect',
      type: "'dim' | 'none'",
      default: 'dim',
      description: 'The disabled state effect.',
    },
    {
      name: 'disabled',
      type: 'boolean',
      default: 'false',
      description: 'Whether the button is disabled.',
    },
    {
      name: 'processing',
      type: 'boolean',
      default: 'false',
      description: 'Whether the button is in a processing state.',
    },
    {
      name: 'iconName',
      type: 'string',
      default: 'undefined',
      description: 'The name of the icon to display.',
    },
    {
      name: 'iconPosition',
      type: "'left' | 'right' | 'only'",
      default: 'left',
      description: 'The position of the icon.',
    },
    {
      name: 'iconColor',
      type: 'string',
      default: 'undefined',
      description: 'Custom color for the icon.',
    },
    {
      name: 'pill',
      type: 'boolean',
      default: 'false',
      description: 'Whether to use pill-shaped border radius.',
    },
    {
      name: 'customStyles',
      type: 'Record<string, string>',
      default: '{}',
      description: 'CSS custom properties for style overrides.',
    },
    {
      name: 'width',
      type: 'string',
      default: 'undefined',
      description: 'Custom width for the button.',
    },
    {
      name: 'height',
      type: 'string',
      default: 'undefined',
      description: 'Custom height for the button.',
    },
  ];

  tones: GlassButtonTone[] = [
    'accent',
    'neutral',
    'positive',
    'negative',
    'warning',
    'info',
  ];

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation();
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  onButtonClick(event: Event) {
    console.log('GlassButton clicked:', event);
  }
}
