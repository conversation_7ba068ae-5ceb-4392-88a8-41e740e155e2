.demo-container {
  // max-width: 920px;
  margin: 0 auto;
  // padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, sans-serif;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.demo-section {
  margin-bottom: 3rem;
  padding: 2rem;
  background: url('/assets/glass_1.png') !important;
  background-size: cover !important;
  // background-position: center !important;
  background-repeat: no-repeat !important;
  width: 100%;
  min-height: 400px;
  display: flex;
  justify-content: center;
  // border-radius: 12px;

  h3 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  p {
    // color: #64748b;
    line-height: 1.6;
    margin-bottom: 1.5rem;
  }
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.rich-content {
  h4 {
    color: #1e293b;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
  }

  p {
    color: #475569;
    line-height: 1.6;
    margin-bottom: 1.5rem;
  }
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin: 1.5rem 0;

  .feature-item {
    padding: 1rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;

    h5 {
      color: #1e293b;
      font-size: 1rem;
      font-weight: 600;
      margin-bottom: 0.5rem;
    }

    p {
      color: #64748b;
      font-size: 0.9rem;
      line-height: 1.5;
      margin: 0;
    }
  }
}

.cta-section {
  text-align: center;
  margin-top: 2rem;
  padding: 1.5rem;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #bae6fd;

  p {
    color: #0c4a6e;
    margin-bottom: 1rem;
  }
}

.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  text-align: center;

  &.btn-primary {
    background: #3b82f6;
    color: white;

    &:hover {
      background: #2563eb;
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .demo-section {
    padding: 1.5rem;
  }

  .feature-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .cta-section {
    padding: 1rem;
  }
}
