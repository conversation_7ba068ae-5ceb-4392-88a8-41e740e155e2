.demo-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.demo-section {
  margin-bottom: 3rem;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #ef4444;

  h3 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  p {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 1.5rem;
  }
}



.control-panel {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;

  h4 {
    color: #1e293b;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    text-align: center;
  }

  .control-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;

    @media (max-width: 768px) {
      flex-direction: column;
      align-items: center;
    }
  }
}



.controlled-accordions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.accordion-wrapper {
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.2s ease;
  outline: none;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  &:focus-visible {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }

  &:active {
    transform: translateY(0);
  }
}



/* Button styles */
.btn {
  display: inline-block;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  text-align: center;

  &.btn-primary {
    background: #3b82f6;
    color: white;

    &:hover {
      background: #2563eb;
    }
  }

  &.btn-secondary {
    background: #6b7280;
    color: white;

    &:hover {
      background: #4b5563;
    }
  }

  &.btn-info {
    background: #06b6d4;
    color: white;

    &:hover {
      background: #0891b2;
    }
  }

  &.btn-warning {
    background: #f59e0b;
    color: white;

    &:hover {
      background: #d97706;
    }
  }
}

/* Content styling */
p {
  color: #475569;
  line-height: 1.6;
  margin-bottom: 1rem;

  strong {
    color: #1e293b;
    font-weight: 600;
  }
}

h4 {
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

ul {
  color: #64748b;
  line-height: 1.6;
  padding-left: 1.5rem;

  li {
    margin-bottom: 0.5rem;
  }
}

code {
  background: #f1f5f9;
  padding: 0.125rem 0.25rem;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.875rem;
  color: #1e293b;
}
