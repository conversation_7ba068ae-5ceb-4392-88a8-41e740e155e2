.demo-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.demo-section {
  margin-bottom: 3rem;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #ec4899;

  h3 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  p {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 1.5rem;
  }
}

.api-table {
  overflow-x: auto;
  margin: 1.5rem 0;

  table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.875rem;

    th,
    td {
      padding: 0.75rem;
      text-align: left;
      border-bottom: 1px solid #e2e8f0;
    }

    th {
      background: #f8fafc;
      font-weight: 600;
      color: #1e293b;
    }

    td {
      color: #475569;
      vertical-align: top;
    }

    code {
      background: #f1f5f9;
      padding: 0.125rem 0.25rem;
      border-radius: 4px;
      font-family: monospace;
      font-size: 0.75rem;
      color: #1e293b;
    }
  }
}

.usage-examples {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin: 1.5rem 0;

  .example {
    h4 {
      color: #1e293b;
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 1rem;
    }

    pre {
      background: #1e293b;
      color: #e2e8f0;
      padding: 1.5rem;
      border-radius: 8px;
      overflow-x: auto;
      font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
      font-size: 0.875rem;
      line-height: 1.5;

      code {
        color: inherit;
        background: none;
        padding: 0;
      }
    }
  }
}

/* Content styling */
p {
  color: #475569;
  line-height: 1.6;
  margin-bottom: 1rem;

  strong {
    color: #1e293b;
    font-weight: 600;
  }
}

h4 {
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

code {
  background: #f1f5f9;
  padding: 0.125rem 0.25rem;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.875rem;
  color: #1e293b;
}
