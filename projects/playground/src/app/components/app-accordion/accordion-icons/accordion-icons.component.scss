.demo-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.demo-section {
  margin-bottom: 3rem;
  padding: 2rem;
  background: white;
  border-radius: 12px;


  h3 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  p {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 1.5rem;
  }
}

.icon-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.title-icon-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.position-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 1.5rem 0;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .position-item {
    h4 {
      color: #1e293b;
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 1rem;
      text-align: center;
    }
  }
}

.custom-icons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Content styling */
p {
  color: #475569;
  line-height: 1.6;
  margin-bottom: 1rem;

  strong {
    color: #1e293b;
    font-weight: 600;
  }
}

h4 {
  color: #1e293b;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}
