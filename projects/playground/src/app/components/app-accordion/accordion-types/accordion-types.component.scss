.demo-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, sans-serif;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

.demo-section {
  margin-bottom: 3rem;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #10b981;

  h3 {
    color: #1e293b;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  p {
    color: #64748b;
    line-height: 1.6;
    margin-bottom: 1.5rem;
  }
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 1.5rem 0;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .comparison-item {
    h4 {
      color: #1e293b;
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 1rem;
      text-align: center;
    }
  }
}

.use-cases {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin: 1.5rem 0;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .use-case {
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;

    h4 {
      color: #1e293b;
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 1rem;
    }

    ul {
      color: #475569;
      line-height: 1.6;
      padding-left: 1.5rem;

      li {
        margin-bottom: 0.5rem;
      }
    }
  }
}

/* Content styling */
h4 {
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

p {
  color: #475569;
  line-height: 1.6;
  margin-bottom: 1rem;
}

ul {
  color: #64748b;
  line-height: 1.6;
  padding-left: 1.5rem;

  li {
    margin-bottom: 0.5rem;
  }
}
