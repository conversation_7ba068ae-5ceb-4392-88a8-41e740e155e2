<div class="demo-container">
  <div class="demo-section">
    <div class="position-comparison">
      <ava-accordion
        *ngFor="let example of positionExamples"
        [iconPosition]="example.position"
        [iconClosed]="example.iconClosed"
        [iconOpen]="example.iconOpen"
        (click)="onAccordionToggle($event)"
      >
        <span header>{{ example.title }}</span>
        <div content>
          <p>
            <strong>{{ example.description }}</strong>
          </p>
          <p>
            This accordion demonstrates the
            <code>{{ example.position }}</code> icon positioning. The icons are
            positioned on the {{ example.position }} side of the header,
            providing a different visual emphasis compared to the opposite
            position.
          </p>
        </div>
      </ava-accordion>
    </div>
  </div>
</div>
