import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ToggleComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-events-demo',
  standalone: true,
  imports: [CommonModule, ToggleComponent],
  template: `
    <div class="center-demo">
      <div class="demo-section">
        <div class="demo-item">
          <ava-toggle
            size="medium"
            title="Event Toggle"
            position="left"
            [checked]="eventToggleEnabled"
            (checkedChange)="onEventToggleChange($event)"
          >
          </ava-toggle>

          <div class="event-log">
            <h5 style="color: var(--color-text-primary)">Event Log:</h5>
            <div class="log-entries">
              <div *ngFor="let log of eventLogs" class="log-entry">
                {{ log }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .center-demo {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
      }

      .demo-section {
        max-width: 800px;
        width: 100%;
        margin-left: auto;
        margin-right: auto;

        h3 {
          text-align: center;
          margin-bottom: 10px;
          color: #333;
          font-size: 24px;
        }

        .description {
          text-align: center;
          margin-bottom: 30px;
          color: #666;
          font-size: 16px;
        }
      }

      .demo-item {
        margin-bottom: 40px;
        padding: 20px;
        border-radius: 8px;

        h4 {
          margin-bottom: 15px;
          color: #555;
          font-size: 18px;
        }

        .status-text {
          margin-top: 15px;
          color: #666;
          font-weight: 500;
        }
      }

      .event-log {
        margin-top: 20px;

        h5 {
          margin-bottom: 10px;
          color: #555;
          font-size: 16px;
        }
      }

      .log-entries {
        max-height: 150px;
        overflow-y: auto;
        background-color: none;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 10px;
      }

      .log-entry {
        font-family: monospace;
        font-size: 12px;
        color: var(--color-text-secondary);
        margin-bottom: 5px;
        padding: 2px 0;
      }

      .multiple-toggles {
        display: flex;
        flex-direction: column;
        gap: 15px;
      }

      .toggle-item {
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .toggle-status {
        font-weight: bold;
        color: #007bff;
        min-width: 30px;
      }
    `,
  ],
})
export class EventsDemoComponent {
  eventToggleEnabled = false;
  toggle1Enabled = false;
  toggle2Enabled = true;
  toggle3Enabled = false;
  eventLogs: string[] = [];

  onEventToggleChange(checked: boolean) {
    this.eventToggleEnabled = checked;
    const timestamp = new Date().toLocaleTimeString();
    this.eventLogs.unshift(
      `[${timestamp}] Event toggle changed to: ${
        checked ? 'enabled' : 'disabled'
      }`
    );

    // Keep only last 10 entries
    if (this.eventLogs.length > 10) {
      this.eventLogs = this.eventLogs.slice(0, 10);
    }

    console.log('Event toggle:', checked ? 'enabled' : 'disabled');
  }

  onToggle1Change(checked: boolean) {
    this.toggle1Enabled = checked;
    console.log('Toggle 1:', checked ? 'enabled' : 'disabled');
  }

  onToggle2Change(checked: boolean) {
    this.toggle2Enabled = checked;
    console.log('Toggle 2:', checked ? 'enabled' : 'disabled');
  }

  onToggle3Change(checked: boolean) {
    this.toggle3Enabled = checked;
    console.log('Toggle 3:', checked ? 'enabled' : 'disabled');
  }
}
