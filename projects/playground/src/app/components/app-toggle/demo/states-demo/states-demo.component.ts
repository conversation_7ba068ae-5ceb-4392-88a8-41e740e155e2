import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ToggleComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-states-demo',
  standalone: true,
  imports: [CommonModule, ToggleComponent],
  template: `
    <div class="center-demo">
      <div class="demo-section">
        <div class="demo-item">
          <div class="state-comparison">
            <div class="state-item">
              <ava-toggle
                size="medium"
                title="Enabled Unchecked"
                [checked]="false"
              >
              </ava-toggle>
            </div>
            <div class="state-item">
              <ava-toggle
                size="medium"
                title="Enabled Checked"
                [checked]="true"
              >
              </ava-toggle>
            </div>
            <div class="state-item">
              <ava-toggle
                size="medium"
                title="Disabled Unchecked"
                [disabled]="true"
                [checked]="false"
              >
              </ava-toggle>
            </div>
            <div class="state-item">
              <ava-toggle
                size="medium"
                title="Disabled Checked"
                [disabled]="true"
                [checked]="true"
              >
              </ava-toggle>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .center-demo {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 60vh;
        padding: 2rem;
      }

      .demo-section {
        max-width: 870px;
        width: 100%;
        margin-left: auto;
        margin-right: auto;
        margin-top: 0;
        padding: 2rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .demo-item {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 1rem;
        margin-top: 0;
      }

      .state-comparison {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 2rem;
        justify-items: center;
        align-items: center;
        max-width: 600px;
      }

      .state-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
      }
    `,
  ],
})
export class StatesDemoComponent {
  enabledToggleChecked = false;
  disabledToggleChecked = false;

  onEnabledToggleChange(checked: boolean) {
    this.enabledToggleChecked = checked;
    console.log('Enabled toggle:', checked ? 'enabled' : 'disabled');
  }
}
