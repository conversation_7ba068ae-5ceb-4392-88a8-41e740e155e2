import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ToggleComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-sizes-demo',
  standalone: true,
  imports: [CommonModule, ToggleComponent],
  template: `
    <div class="center-demo">
      <div class="demo-section">
        <div class="demo-item">
          <div class="size-comparison">
            <div class="size-item">
              <ava-toggle
                size="small"
                title="Small"
                [checked]="true"
              ></ava-toggle>
            </div>
            <div class="size-item">
              <ava-toggle
                size="medium"
                title="Medium"
                [checked]="true"
              ></ava-toggle>
            </div>
            <div class="size-item">
              <ava-toggle
                size="large"
                title="Large"
                [checked]="true"
              ></ava-toggle>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .center-demo {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 60vh;
        padding: 2rem;
      }

      .demo-section {
        max-width: 870px;
        width: 100%;
        margin-left: auto;
        margin-right: auto;
        margin-top: 0;
        padding: 2rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .demo-item {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 1rem;
        border-radius: 8px;
      }

      .size-comparison {
        display: flex;
        flex-direction: row;
        gap: 3rem;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
      }

      .size-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
      }

      .size-label {
        font-weight: 500;
        color: #555;
        font-size: 1rem;
        text-align: center;
      }
    `,
  ],
})
export class SizesDemoComponent {
  smallToggleEnabled = false;
  mediumToggleEnabled = true;
  largeToggleEnabled = false;

  onSmallToggleChange(checked: boolean) {
    this.smallToggleEnabled = checked;
    console.log('Small toggle:', checked ? 'enabled' : 'disabled');
  }

  onMediumToggleChange(checked: boolean) {
    this.mediumToggleEnabled = checked;
    console.log('Medium toggle:', checked ? 'enabled' : 'disabled');
  }

  onLargeToggleChange(checked: boolean) {
    this.largeToggleEnabled = checked;
    console.log('Large toggle:', checked ? 'enabled' : 'disabled');
  }
}
