import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ToggleComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-forms-demo',
  standalone: true,
  imports: [CommonModule, FormsModule, ToggleComponent],
  template: `
    <div class="center-demo">
      <div class="demo-section">
        <h3>Toggle in Forms</h3>
        <p class="description">
          Using toggle components within forms and reactive forms.
        </p>

        <div class="demo-item">
          <h4>Form Integration</h4>
          <form (ngSubmit)="onSubmit()" #toggleForm="ngForm">
            <div class="form-group">
              <ava-toggle
                size="medium"
                title="Email Notifications"
                position="left"
                [(ngModel)]="formData.emailNotifications"
                name="emailNotifications"
              >
              </ava-toggle>
            </div>

            <div class="form-group">
              <ava-toggle
                size="medium"
                title="SMS Notifications"
                position="left"
                [(ngModel)]="formData.smsNotifications"
                name="smsNotifications"
              >
              </ava-toggle>
            </div>

            <div class="form-group">
              <ava-toggle
                size="medium"
                title="Push Notifications"
                position="left"
                [(ngModel)]="formData.pushNotifications"
                name="pushNotifications"
              >
              </ava-toggle>
            </div>

            <div class="form-group">
              <ava-toggle
                size="medium"
                title="Marketing Communications"
                position="left"
                [(ngModel)]="formData.marketingCommunications"
                name="marketingCommunications"
              >
              </ava-toggle>
            </div>

            <button type="submit" class="submit-btn">Submit Form</button>
          </form>

          <div class="form-data-display">
            <h5>Form Data:</h5>
            <pre>{{ formData | json }}</pre>
          </div>
        </div>

        <div class="demo-item">
          <h4>Form Validation</h4>
          <form (ngSubmit)="onSubmitWithValidation()" #validationForm="ngForm">
            <div class="form-group">
              <ava-toggle
                size="medium"
                title="Accept Terms and Conditions"
                position="left"
                [(ngModel)]="validationData.acceptTerms"
                name="acceptTerms"
                required
              >
              </ava-toggle>
              <div
                *ngIf="validationForm.submitted && !validationData.acceptTerms"
                class="error-message"
              >
                You must accept the terms and conditions to continue.
              </div>
            </div>

            <div class="form-group">
              <ava-toggle
                size="medium"
                title="Subscribe to Newsletter"
                position="left"
                [(ngModel)]="validationData.subscribeNewsletter"
                name="subscribeNewsletter"
              >
              </ava-toggle>
            </div>

            <button type="submit" class="submit-btn">
              Submit with Validation
            </button>
          </form>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .center-demo {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background-color: white;
        padding: 20px;
      }

      .demo-section {
        max-width: 800px;
        width: 100%;
        margin-left: auto;
        margin-right: auto;

        h3 {
          text-align: center;
          margin-bottom: 10px;
          color: #333;
          font-size: 24px;
        }

        .description {
          text-align: center;
          margin-bottom: 30px;
          color: #666;
          font-size: 16px;
        }
      }

      .demo-item {
        margin-bottom: 40px;
        padding: 20px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        background-color: #fafafa;

        h4 {
          margin-bottom: 15px;
          color: #555;
          font-size: 18px;
        }
      }

      .form-group {
        margin-bottom: 20px;
      }

      .submit-btn {
        background-color: #007bff;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        margin-top: 10px;
      }

      .submit-btn:hover {
        background-color: #0056b3;
      }

      .form-data-display {
        margin-top: 20px;

        h5 {
          margin-bottom: 10px;
          color: #555;
          font-size: 16px;
        }

        pre {
          background-color: #f5f5f5;
          border: 1px solid #ddd;
          border-radius: 4px;
          padding: 10px;
          font-size: 12px;
          overflow-x: auto;
        }
      }

      .error-message {
        color: #dc3545;
        font-size: 12px;
        margin-top: 5px;
      }
    `,
  ],
})
export class FormsDemoComponent {
  formData = {
    emailNotifications: true,
    smsNotifications: false,
    pushNotifications: true,
    marketingCommunications: false,
  };

  validationData = {
    acceptTerms: false,
    subscribeNewsletter: true,
  };

  onSubmit() {
    console.log('Form submitted:', this.formData);
    alert('Form submitted! Check console for data.');
  }

  onSubmitWithValidation() {
    if (!this.validationData.acceptTerms) {
      alert('Please accept the terms and conditions.');
      return;
    }

    console.log('Validation form submitted:', this.validationData);
    alert('Validation form submitted successfully!');
  }
}
