import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ToggleComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-basic-usage-demo',
  standalone: true,
  imports: [CommonModule, ToggleComponent],
  template: `
    <div class="center-demo">
      <div class="demo-section">
        <div class="demo-item">
          <ava-toggle
            size="medium"
            title="Enable Notifications"
            position="left"
            [checked]="notificationsEnabled"
            (checkedChange)="onNotificationsChange($event)"
          >
          </ava-toggle>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .center-demo {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 60vh;
        padding: 2rem;
      }

      .demo-section {
        max-width: 870px;
        width: 100%;
        margin-left: auto;
        margin-right: auto;
        margin-top: 0;
        padding: 2rem;
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .demo-item {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 1rem;
        border-radius: 8px;
      }
    `,
  ],
})
export class BasicUsageDemoComponent {
  notificationsEnabled = false;
  darkModeEnabled = true;
  simpleToggleEnabled = false;

  onNotificationsChange(checked: boolean) {
    this.notificationsEnabled = checked;
    console.log('Notifications:', checked ? 'enabled' : 'disabled');
  }

  onDarkModeChange(checked: boolean) {
    this.darkModeEnabled = checked;
    console.log('Dark mode:', checked ? 'enabled' : 'disabled');
  }

  onSimpleToggleChange(checked: boolean) {
    this.simpleToggleEnabled = checked;
    console.log('Simple toggle:', checked ? 'enabled' : 'disabled');
  }
}
