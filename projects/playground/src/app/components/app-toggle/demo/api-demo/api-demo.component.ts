import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

interface ApiProperty {
  name: string;
  type: string;
  default: string;
  description: string;
}

interface ApiEvent {
  name: string;
  type: string;
  description: string;
}

@Component({
  selector: 'ava-api-demo',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="center-demo">
      <div class="demo-section">
        <h3>Toggle API Reference</h3>
        <p class="description">
          Complete API documentation for the toggle component.
        </p>

        <div class="demo-item">
          <h4>Input Properties</h4>
          <div class="api-table">
            <div class="api-header">
              <span class="api-name">Property</span>
              <span class="api-type">Type</span>
              <span class="api-default">Default</span>
              <span class="api-description">Description</span>
            </div>
            <div *ngFor="let prop of inputProperties" class="api-row">
              <span class="api-name">{{ prop.name }}</span>
              <span class="api-type">{{ prop.type }}</span>
              <span class="api-default">{{ prop.default }}</span>
              <span class="api-description">{{ prop.description }}</span>
            </div>
          </div>
        </div>

        <div class="demo-item">
          <h4>Output Events</h4>
          <div class="api-table">
            <div class="api-header">
              <span class="api-name">Event</span>
              <span class="api-type">Type</span>
              <span class="api-description">Description</span>
            </div>
            <div *ngFor="let event of outputEvents" class="api-row">
              <span class="api-name">{{ event.name }}</span>
              <span class="api-type">{{ event.type }}</span>
              <span class="api-description">{{ event.description }}</span>
            </div>
          </div>
        </div>

        <div class="demo-item">
          <h4>Type Definitions</h4>
          <div class="type-definitions">
            <div class="type-item">
              <h5>ToggleSize</h5>
              <code>'small' | 'medium' | 'large'</code>
              <p>Available sizes for the toggle component.</p>
            </div>
            <div class="type-item">
              <h5>TogglePosition</h5>
              <code>'left' | 'right'</code>
              <p>Available positions for the toggle label.</p>
            </div>
          </div>
        </div>

        <div class="demo-item">
          <h4>Usage Example</h4>
          <div class="code-example">
            <h5>Basic Usage:</h5>
            <pre><code>&lt;ava-toggle
  size="medium"
  title="Enable Notifications"
  position="left"
  [checked]="notificationsEnabled"
  (checkedChange)="onToggleChange($event)"&gt;
&lt;/ava-toggle&gt;</code></pre>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .center-demo {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background-color: white;
        padding: 20px;
      }

      .demo-section {
        max-width: 1000px;
        width: 100%;
        margin-left: auto;
        margin-right: auto;

        h3 {
          text-align: center;
          margin-bottom: 10px;
          color: #333;
          font-size: 24px;
        }

        .description {
          text-align: center;
          margin-bottom: 30px;
          color: #666;
          font-size: 16px;
        }
      }

      .demo-item {
        margin-bottom: 40px;
        padding: 20px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        background-color: #fafafa;

        h4 {
          margin-bottom: 15px;
          color: #555;
          font-size: 18px;
        }
      }

      .api-table {
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
      }

      .api-header {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 2fr;
        background-color: #f8f9fa;
        font-weight: bold;
        padding: 12px;
        border-bottom: 1px solid #ddd;
      }

      .api-row {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 2fr;
        padding: 12px;
        border-bottom: 1px solid #eee;
      }

      .api-row:last-child {
        border-bottom: none;
      }

      .api-name {
        font-family: monospace;
        color: #007bff;
      }

      .api-type {
        font-family: monospace;
        color: #6c757d;
      }

      .api-default {
        font-family: monospace;
        color: #28a745;
      }

      .api-description {
        color: #333;
      }

      .type-definitions {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      .type-item {
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 4px;
        border-left: 4px solid #007bff;
      }

      .type-item h5 {
        margin-bottom: 8px;
        color: #333;
        font-size: 16px;
      }

      .type-item code {
        display: block;
        background-color: #e9ecef;
        padding: 8px;
        border-radius: 4px;
        font-family: monospace;
        margin-bottom: 8px;
      }

      .type-item p {
        color: #666;
        margin: 0;
      }

      .code-example {
        background-color: #f8f9fa;
        border-radius: 4px;
        padding: 15px;
      }

      .code-example h5 {
        margin-bottom: 10px;
        color: #333;
      }

      .code-example pre {
        background-color: #2d3748;
        color: #e2e8f0;
        padding: 15px;
        border-radius: 4px;
        overflow-x: auto;
        margin: 0;
      }

      .code-example code {
        font-family: 'Courier New', monospace;
        font-size: 14px;
      }
    `,
  ],
})
export class ApiDemoComponent {
  inputProperties: ApiProperty[] = [
    {
      name: 'size',
      type: 'ToggleSize',
      default: "'medium'",
      description: 'Sets the size of the toggle component.',
    },
    {
      name: 'title',
      type: 'string',
      default: "''",
      description: 'The title displayed next to the toggle.',
    },
    {
      name: 'position',
      type: 'TogglePosition',
      default: "'left'",
      description:
        'Defines the position of the title relative to the toggle switch.',
    },
    {
      name: 'disabled',
      type: 'boolean',
      default: 'false',
      description: 'Disables the toggle when set to true.',
    },
    {
      name: 'checked',
      type: 'boolean',
      default: 'false',
      description:
        'Defines whether the toggle is checked (true) or not (false).',
    },
    {
      name: 'animation',
      type: 'boolean',
      default: 'true',
      description: 'Enables or disables animation during state transitions.',
    },
    {
      name: 'showIcons',
      type: 'boolean',
      default: 'false',
      description: 'Enables icon display inside the toggle slider.',
    },
    {
      name: 'uncheckedIcon',
      type: 'string',
      default: "'x'",
      description: 'Icon name to display when toggle is unchecked.',
    },
    {
      name: 'checkedIcon',
      type: 'string',
      default: "'check'",
      description: 'Icon name to display when toggle is checked.',
    },
  ];

  outputEvents: ApiEvent[] = [
    {
      name: 'checkedChange',
      type: 'EventEmitter<boolean>',
      description: 'Emitted when the checked state changes.',
    },
  ];
}
