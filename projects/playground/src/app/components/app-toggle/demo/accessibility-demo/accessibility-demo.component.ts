import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ToggleComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-accessibility-demo',
  standalone: true,
  imports: [CommonModule, ToggleComponent],
  template: `
    <div class="center-demo">
      <div class="demo-section">
        <div class="demo-item">
          <h4 style="color: var(--color-text-primary)">Keyboard Navigation</h4>
          <p class="accessibility-info">
            Use Tab to focus on toggles, Space or Enter to toggle state.
          </p>
          <ava-toggle
            size="medium"
            title="Keyboard Accessible Toggle"
            position="left"
            [checked]="keyboardToggleEnabled"
            (checkedChange)="onKeyboardToggleChange($event)"
          >
          </ava-toggle>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .center-demo {
        display: flex;
        min-height: 100vh;
        padding: 20px;
      }

      .demo-section {
        max-width: 800px;
        width: 100%;
        margin-left: auto;
        margin-right: auto;

        h3 {
          text-align: center;
          margin-bottom: 10px;
          color: #333;
          font-size: 24px;
        }

        .description {
          text-align: center;
          margin-bottom: 30px;
          color: #666;
          font-size: 16px;
        }
      }

      .demo-item {
        margin-bottom: 40px;
        padding: 20px;
        margin-top: 0;

        h4 {
          margin-bottom: 15px;
          color: #555;
          font-size: 18px;
        }

        .status-text {
          margin-top: 15px;
          color: #666;
          font-weight: 500;
        }
      }

      .accessibility-info {
        margin-top: 10px;
        padding: 10px;
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
        color: #1976d2;
        font-size: 14px;
        border-radius: 4px;
      }
    `,
  ],
})
export class AccessibilityDemoComponent {
  keyboardToggleEnabled = false;
  screenReaderToggleEnabled = true;
  highContrastToggleEnabled = false;
  focusToggleEnabled = true;

  onKeyboardToggleChange(checked: boolean) {
    this.keyboardToggleEnabled = checked;
    console.log('Keyboard toggle:', checked ? 'enabled' : 'disabled');
  }

  onScreenReaderToggleChange(checked: boolean) {
    this.screenReaderToggleEnabled = checked;
    console.log('Screen reader toggle:', checked ? 'enabled' : 'disabled');
  }

  onHighContrastToggleChange(checked: boolean) {
    this.highContrastToggleEnabled = checked;
    console.log('High contrast toggle:', checked ? 'enabled' : 'disabled');
  }

  onFocusToggleChange(checked: boolean) {
    this.focusToggleEnabled = checked;
    console.log('Focus toggle:', checked ? 'enabled' : 'disabled');
  }
}
