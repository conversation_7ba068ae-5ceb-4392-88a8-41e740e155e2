import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ToggleComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-animation-demo',
  standalone: true,
  imports: [CommonModule, ToggleComponent],
  template: `
    <div class="center-demo">
      <div class="demo-section">
        <h3>Toggle Animation</h3>
        <p class="description">
          Toggle components with and without animation effects.
        </p>

        <div class="demo-item">
          <h4>With Animation (Default)</h4>
          <ava-toggle
            size="medium"
            title="Animated Toggle"
            position="left"
            [animation]="true"
            [checked]="animatedToggleEnabled"
            (checkedChange)="onAnimatedToggleChange($event)"
          >
          </ava-toggle>
          <p class="status-text">
            Status: {{ animatedToggleEnabled ? 'Enabled' : 'Disabled' }}
          </p>
        </div>

        <div class="demo-item">
          <h4>Without Animation</h4>
          <ava-toggle
            size="medium"
            title="Non-Animated Toggle"
            position="left"
            [animation]="false"
            [checked]="nonAnimatedToggleEnabled"
            (checkedChange)="onNonAnimatedToggleChange($event)"
          >
          </ava-toggle>
          <p class="status-text">
            Status: {{ nonAnimatedToggleEnabled ? 'Enabled' : 'Disabled' }}
          </p>
        </div>

        <div class="demo-item">
          <h4>Animation Comparison</h4>
          <div class="animation-comparison">
            <div class="animation-item">
              <span class="animation-label">With Animation:</span>
              <ava-toggle size="medium" [animation]="true" [checked]="true">
              </ava-toggle>
            </div>
            <div class="animation-item">
              <span class="animation-label">Without Animation:</span>
              <ava-toggle size="medium" [animation]="false" [checked]="true">
              </ava-toggle>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .center-demo {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background-color: white;
        padding: 20px;
      }

      .demo-section {
        max-width: 800px;
        width: 100%;
        margin-left: auto;
        margin-right: auto;

        h3 {
          text-align: center;
          margin-bottom: 10px;
          color: #333;
          font-size: 24px;
        }

        .description {
          text-align: center;
          margin-bottom: 30px;
          color: #666;
          font-size: 16px;
        }
      }

      .demo-item {
        margin-bottom: 40px;
        padding: 20px;
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        background-color: #fafafa;

        h4 {
          margin-bottom: 15px;
          color: #555;
          font-size: 18px;
        }

        .status-text {
          margin-top: 15px;
          color: #666;
          font-weight: 500;
        }
      }

      .animation-comparison {
        display: flex;
        flex-direction: column;
        gap: 15px;
      }

      .animation-item {
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .animation-label {
        min-width: 150px;
        font-weight: 500;
        color: #555;
      }
    `,
  ],
})
export class AnimationDemoComponent {
  animatedToggleEnabled = false;
  nonAnimatedToggleEnabled = true;

  onAnimatedToggleChange(checked: boolean) {
    this.animatedToggleEnabled = checked;
    console.log('Animated toggle:', checked ? 'enabled' : 'disabled');
  }

  onNonAnimatedToggleChange(checked: boolean) {
    this.nonAnimatedToggleEnabled = checked;
    console.log('Non-animated toggle:', checked ? 'enabled' : 'disabled');
  }
}
