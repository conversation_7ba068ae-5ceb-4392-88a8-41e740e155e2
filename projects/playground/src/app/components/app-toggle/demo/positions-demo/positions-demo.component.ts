import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ToggleComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-positions-demo',
  standalone: true,
  imports: [CommonModule, ToggleComponent],
  template: `
    <div class="center-demo">
      <div class="demo-section">
        <div class="demo-item">
          <div class="position-comparison">
            <div class="position-item">
              <ava-toggle
                size="medium"
                title="Left Position"
                position="left"
                [checked]="true"
              >
              </ava-toggle>
            </div>
            <div class="position-item">
              <ava-toggle
                size="medium"
                title="Right Position"
                position="right"
                [checked]="false"
              >
              </ava-toggle>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .center-demo {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        padding: 20px;
      }

      .demo-section {
        max-width: 800px;
        width: 100%;
        margin-left: auto;
        margin-right: auto;

        h3 {
          text-align: center;
          margin-bottom: 10px;
          color: #333;
          font-size: 24px;
        }

        .description {
          text-align: center;
          margin-bottom: 30px;
          color: #666;
          font-size: 16px;
        }
      }

      .demo-item {
        margin-bottom: 40px;
        padding: 20px;
        margin-top: 0;

        h4 {
          margin-bottom: 15px;
          color: #555;
          font-size: 18px;
        }

        .status-text {
          margin-top: 15px;
          color: #666;
          font-weight: 500;
        }
      }

      .position-comparison {
        display: flex;
        flex-direction: column;
        gap: 20px;
      }

      .position-item {
        display: flex;
        align-items: center;
        justify-content: center;
      }
    `,
  ],
})
export class PositionsDemoComponent {
  leftLabelEnabled = true;
  rightLabelEnabled = false;
  noLabelEnabled = false;

  onLeftLabelChange(checked: boolean) {
    this.leftLabelEnabled = checked;
    console.log('Left label toggle:', checked ? 'enabled' : 'disabled');
  }

  onRightLabelChange(checked: boolean) {
    this.rightLabelEnabled = checked;
    console.log('Right label toggle:', checked ? 'enabled' : 'disabled');
  }

  onNoLabelChange(checked: boolean) {
    this.noLabelEnabled = checked;
    console.log('No label toggle:', checked ? 'enabled' : 'disabled');
  }
}
