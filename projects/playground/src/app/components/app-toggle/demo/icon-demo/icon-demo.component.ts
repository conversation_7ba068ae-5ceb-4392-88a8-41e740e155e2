import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ToggleComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-toggle-icon-demo',
  standalone: true,
  imports: [CommonModule, ToggleComponent],
  template: `
    <div class="center-demo">
      <div class="demo-section">
        <div class="demo-item">
          <ava-toggle
            size="large"
            title="Power Toggle"
            position="left"
            [showIcons]="true"
            uncheckedIcon="power"
            checkedIcon="check"
            [checked]="iconToggle2Enabled"
            [animation]="true"
            (checkedChange)="onIconToggle2($event)"
          >
          </ava-toggle>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      .center-demo {
        display: flex;
        justify-content: center;
        // align-items: center;
        min-height: 60vh;
        padding: 2rem;
      }
      .demo-section {
        max-width: 870px;
        width: 100%;
        margin-left: auto;
        margin-right: auto;
        margin-top: 0;
        padding: 2rem;
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      .demo-item {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1.5rem;
      }
    `,
  ],
})
export class IconDemoComponent {
  iconToggleEnabled = false;
  iconToggle2Enabled = true;
  iconToggle3Enabled = false;

  onIconToggle(checked: boolean) {
    this.iconToggleEnabled = checked;
  }
  onIconToggle2(checked: boolean) {
    this.iconToggle2Enabled = checked;
  }
  onIconToggle3(checked: boolean) {
    this.iconToggle3Enabled = checked;
  }
}
