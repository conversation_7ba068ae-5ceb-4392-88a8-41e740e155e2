/* Demo Navigation */
.nav-links {
  text-align: center;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  padding: 3rem 0;
  border-bottom: 1px solid #cbd5e1;

  h3 {
    font-size: 2rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 2rem;
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .nav-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;

    .nav-link {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 1.5rem;
      background: rgba(255, 255, 255, 0.8);
      border: 2px solid transparent;
      border-radius: 1rem;
      text-decoration: none;
      color: #374151;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

      &:hover {
        transform: translateY(-4px);
        border-color: #6366f1;
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
        color: #6366f1;
      }

      .nav-icon {
        font-size: 2rem;
        margin-bottom: 0.75rem;
        display: block;
      }

      .nav-text {
        font-size: 1rem;
        font-weight: 500;
        text-align: center;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .nav-links {
    padding: 2rem 0;

    h3 {
      font-size: 1.5rem;
    }

    .nav-grid {
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 1rem;

      .nav-link {
        padding: 1rem;

        .nav-icon {
          font-size: 1.5rem;
        }

        .nav-text {
          font-size: 0.9rem;
        }
      }
    }
  }
}

/* Example preview container */
.example-preview {
  margin-top: var(--spacing-5x);
  padding: var(--spacing-5x);
  border: 1px solid var(--surface-border);
  border-radius: var(--3x);
  // background-color: var(--surface-section);
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
  /* Makes it responsive */
  gap: var(--spacing-3x);
}

.status-text {
  margin-top: 8px;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.icon-showcase {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: flex-start;

  @media (min-width: 768px) {
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    gap: 2rem;
  }
}

:host {
  display: block;
  width: 100%;
}

/* Prevent horizontal scroll */
html,
body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
}

* {
  box-sizing: border-box;
}

/* Main layout */
.documentation {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
  overflow-x: hidden;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

/* Header */
.doc-header {
  h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
  }

  .description {
    font-size: 1.1rem;
    line-height: 1.6;
  }
}

/* Sections */
.doc-sections {
  margin-top: 4rem;
}

.doc-section {
  margin-bottom: 1rem;

  h2 {
    font-size: 1.8rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
  }

  p {
    font-size: 0.95rem;
    line-height: 1.5;
  }
}

/* Section header with toggle */
.section-header {
  display: flex;
  flex-direction: column;
  position: relative;
  cursor: pointer;
  padding: 1rem;
  background-color: var(--surface);
  border-radius: var(--border-radius);

  h2 {
    margin-bottom: 0.5rem;
  }

  .description-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .code-toggle {
    font-size: 0.75rem;
    color: var(--icons-action);
    cursor: pointer;
    display: flex;
    align-items: center;
    font-weight: var(--font-font-weight-medium);
    font-family: var(--font-font-family-heading);

    &:hover {
      text-decoration: underline;
    }

    span {
      margin-right: 0.5rem;
    }

    awe-icons {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      line-height: 0;
      padding: 0;
      margin: 0;
      vertical-align: middle;
      flex-shrink: 0;

      svg {
        width: 60%;
        height: 80%;
        display: block;
      }
    }
  }
}

/* Code example styles */
.code-example {
  margin-top: 1.5rem;

  .example-preview {
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    border: 1px solid var(--surface-border);
  }

  .code-block {
    position: relative;
    border-radius: 0.5rem;
    margin-top: 1rem;
    border: 1px solid var(--surface-border);
    background-color: var(--surface-ground);

    pre {
      margin: 0;
      padding: 1rem;
      border-radius: 0.25rem;
      overflow-x: auto;
    }

    .copy-button {
      position: absolute;
      top: 0.5rem;
      right: 0.5rem;
      padding: 0.5rem;
      background: transparent;
      border: none;
      cursor: pointer;
      color: var(--text-color-secondary);

      &:hover {
        color: var(--primary-color);
      }
    }
  }
}

/* API table styles */
.api-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;

  th,
  td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--surface-border);
  }

  th {
    background-color: var(--surface);
    font-weight: 600;
    color: var(--text-color-primary);
  }

  td {
    code {
      background-color: var(--surface);
      padding: 0.2rem 0.4rem;
      border-radius: var(--border-radius-sm);
      font-family: monospace;
    }
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .documentation {
    padding: 1rem;
  }
}
