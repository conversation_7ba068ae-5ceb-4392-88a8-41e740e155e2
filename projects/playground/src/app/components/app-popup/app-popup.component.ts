import {
  Component,
  <PERSON>ement<PERSON>ef,
  HostL<PERSON>ener,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
//import { PopUpComponent } from "../../../../../play-comp-library/src/lib/components/pop-up/pop-up.component";
import { CommonModule } from '@angular/common';
import { RouterLink } from '@angular/router';
import { PopupComponent } from '../../../../../play-comp-library/src/lib/components/popup/popup.component';
import { ConfirmationPopupComponent } from '../../../../../play-comp-library/src/lib/composite-components/confirmation-popup/confirmation-popup.component';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';
import { AvaTextboxComponent } from '../../../../../play-comp-library/src/lib/components/textbox/ava-textbox.component';
import { IconComponent } from '../../../../../play-comp-library/src/lib/components/icon/icon.component';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';

@Component({
  selector: 'app-app-popup',
  //imports: [PopUpComponent, IconsComponent, CommonModule],
  imports: [
    CommonModule,
    RouterLink,
    ReactiveFormsModule,
    PopupComponent,
    ConfirmationPopupComponent,
    ButtonComponent,
    AvaTextboxComponent,
    IconComponent,
  ],

  templateUrl: './app-popup.component.html',
  styleUrls: ['./app-popup.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class AppPopupComponent {
  quickStartCode = `<ava-popup [show]="show" (closed)="show = false">
  <div style="padding: 1.5rem; min-width: 200px; text-align: center;">
    <strong>Hello!</strong><br />This is a basic popup.
  </div>
</ava-popup>

// In your component:
show = false;`;

  @ViewChild('codeBlock') codeBlock!: ElementRef;

  sections = [
    {
      title: 'Info Popup',
      description:
        'Displays a confirmation message with an icon and close button.',
      showCode: false,
      popupType: 'info',
    },
    {
      title: 'Warning Popup',
      description:
        'Warns the user about a non-reversible action using an inline message format.',
      showCode: false,
      popupType: 'warning',
    },
    {
      title: 'Delete Confirmation Popup',
      description:
        'Asks the user to confirm deletion of an item with a red warning style.',
      showCode: false,
      popupType: 'delete',
    },
    {
      title: 'Feedback Popup',
      description: 'Asks for user feedback with a confirm action.',
      showCode: false,
      popupType: 'feedback',
    },
    {
      title: 'Contact Popup',
      description: 'Contact form.',
      showCode: false,
      popupType: 'contact',
    },
    {
      popupType: 'positions',
      title: 'Popup Positions',
      description:
        'Preview the ava-popup component at different screen positions including center, corners, and edges.',
    },
  ];

  popupPositions = [
    'center',
    'top',
    'bottom',
    'top-left',
    'top-right',
    'bottom-left',
    'bottom-right',
  ] as const;

  openPositionPopup: { [key: string]: boolean } = {};

  apiProps = [
    {
      name: 'menuItems',
      type: 'object | object[]',
      default: '{}',
      description:
        'The menu items to be displayed in the pop-up. Can be a single object or an array of objects.',
    },
    {
      name: 'animation',
      type: 'boolean',
      default: 'false',
      description: 'Whether to enable animation for the pop-up.',
    },
  ];
  showPopup: boolean = false;
  showInfoPopup = false;
  showWarningPopup = false;
  showDeletePopup = false;
  showSendBackPopup = false;
  showContactForm = false;
  // Demo form
  demoForm: FormGroup;

  // Demo values
  basicValue = '';
  emailValue = '';
  passwordValue = '';
  textareaValue = '';
  showPassword = false;

  constructor(private fb: FormBuilder) {
    this.demoForm = this.fb.group({
      username: ['', [Validators.required, Validators.minLength(3)]],
      email: ['', [Validators.required, Validators.email]],
      phone: [''],
      description: [''],
      website: [''],
    });
  }

  openSendBackPopup(): void {
    this.showSendBackPopup = false;
    setTimeout(() => {
      this.showSendBackPopup = true;
    }, 0); // short delay to trigger re-render
  }

  handleFeedback(text: string): void {
    console.log('User feedback received:', text);
  }

  deleteItem(): void {
    console.log('Delete action triggered');
    // Your delete logic here
  }

  // Form helpers
  getFieldError(fieldName: string): string {
    const field = this.demoForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['email']) return 'Please enter a valid email';
      if (field.errors['minlength'])
        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
    }
    return '';
  }

  hasFieldError(fieldName: string): boolean {
    const field = this.demoForm.get(fieldName);
    return !!(field?.errors && field.touched);
  }

  onSubmit(): void {
    if (this.demoForm.valid) {
      console.log('Form submitted:', this.demoForm.value);
    } else {
      console.log('Form is invalid');
      this.demoForm.markAllAsTouched();
    }
  }

  togglePasswordVisibility() {
    this.showPassword = !this.showPassword;
  }

  onEditClick() {
    alert('Edit icon clicked!');
  }

  onSendClick() {
    alert('Send icon clicked!');
  }

  onClearClick() {
    alert('Clear icon clicked!');
  }

  toggleCodeVisibility(index: number, event: MouseEvent): void {
    event.stopPropagation(); // Prevent the click event from bubbling up to the section header
    this.sections[index].showCode = !this.sections[index].showCode;
  }

  @HostListener('document:click', ['$event'])
  clickOutside(event: MouseEvent) {
    if (
      this.codeBlock &&
      !this.codeBlock.nativeElement.contains(event.target)
    ) {
      this.sections.forEach((section) => (section.showCode = false));
    }
  }

  getExampleCode(type: string): string {
    const code: Record<string, string> = {
      info: `
import { Component } from '@angular/core';
import { PopUpComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-info-popup',
  standalone: true,
  imports: [PopUpComponent],
  template: \`
    <ava-popup 
      [show]="true"
      title="Message Sent Successfully!"
      message="Thank you for contacting us.<br>We have received your message and will respond as soon as possible."
      [showHeaderIcon]="true"
      headerIconName="circle-check"
      iconColor="green"
      [showClose]="true"
      (closed)="handleClose()">
    </ava-popup>
  \`
})
export class InfoPopupComponent {
  handleClose() {
    console.log('Info popup closed');
  }
}
`,

      warning: `
import { Component } from '@angular/core';
import { PopUpComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-warning-popup',
  standalone: true,
  imports: [PopUpComponent],
  template: \`
    <ava-popup
      [show]="true"
      [showTitle]="false"
      [showHeaderIcon]="false"
      [showInlineMessage]="true"
      [inlineIconName]="'info'"
      [inlineIconSize]="48"
      [inlineIconColor]="'#007bff'"
      [inlineMessage]="'Heads up!'"
      [message]="'Deleting this item will remove it permanently from your records. This action is not reversible.'"
      [showClose]="true"
      [showCancel]="true"
      [cancelButtonLabel]="'Okay'"
      [cancelButtonVariant]="'primary'"
      [popupWidth]="'400px'"
      (closed)="handleClose()">
    </ava-popup>
  \`
})
export class WarningPopupComponent {
  handleClose() {
    console.log('Warning popup closed');
  }
}
`,

      delete: `
import { Component } from '@angular/core';
import { PopUpComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-delete-popup',
  standalone: true,
  imports: [PopUpComponent],
  template: \`
    <ava-popup
      [show]="true"
      [title]="'Delete This Item?'"
      [showTitle]="true"
      [showHeaderIcon]="true"
      [headerIconName]="'trash'"
      [iconSize]="48"
      [iconColor]="'#dc3545'"
      [message]="'Are you sure you want to delete this item?'"
      [showClose]="true"
      [showCancel]="true"
      [showConfirm]="true"
      [cancelButtonLabel]="'Cancel'"
      [confirmButtonLabel]="'Delete'"
      [confirmButtonBackground]="'#dc3545'"
      [confirmButtonVariant]="'primary'"
      [popupWidth]="'400px'"
      (closed)="handleClose()">
    </ava-popup>
  \`
})
export class DeletePopupComponent {
  handleClose() {
    console.log('Delete popup closed');
  }
}
`,

      feedback: `
import { Component } from '@angular/core';
import { ConfirmationPopupComponent } from '@awe/play-comp-library';

@Component({
  selector: 'app-feedback-popup',
  standalone: true,
  imports: [ConfirmationPopupComponent],
  template: \`
    <ava-confirmation-popup
      [show]="true"
      title="We Value Your Feedback"
      message="Please share your feedback below. Your input helps us make meaningful improvements."
      confirmationLabel="Send Back"
      (feedbackSubmitted)="handleFeedback($event)"
      (closed)="handleClose()">
    </ava-confirmation-popup>
  \`
})
export class FeedbackPopupComponent {
  handleFeedback(text: string): void {
    console.log('User feedback:', text);
  }

  handleClose(): void {
    console.log('Popup closed');
  }
}
`,
    };

    return code[type.toLowerCase()] || '// Code example not found.';
  }

  copyCode(sectionTitle: string): void {
    const codeContent = this.getExampleCode(sectionTitle);
    const textArea = document.createElement('textarea');
    textArea.value = codeContent;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    // Optionally, provide user feedback (e.g., show a tooltip or notification)
  }
}
