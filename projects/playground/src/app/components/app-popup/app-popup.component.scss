/* Demo Navigation Styles */
.popup-demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;

  h1 {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1rem;
    color: var(--ava-text-primary);
  }

  .demo-intro {
    font-size: 1.2rem;
    text-align: center;
    margin-bottom: 3rem;
    color: var(--ava-text-secondary);
    line-height: 1.6;
  }

  .demo-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
  }

  .demo-card {
    background: var(--ava-surface-primary);
    border: 1px solid var(--ava-border-subtle);
    border-radius: 12px;
    padding: 2rem;
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
    cursor: pointer;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
      border-color: var(--ava-border-interactive);
    }
  }

  .demo-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    h3 {
      margin: 0;
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--ava-text-primary);
    }
  }

  .demo-badge {
    background: var(--ava-primary-50);
    color: var(--ava-primary-600);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
  }

  .demo-description {
    margin: 0 0 1.5rem 0;
    color: var(--ava-text-secondary);
    line-height: 1.5;
  }

  .demo-preview {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 60px;
    background: var(--ava-surface-secondary);
    border-radius: 8px;

    span {
      font-size: 2rem;
    }
  }

  .code-preview-section {
    background: var(--ava-surface-secondary);
    border-radius: 12px;
    padding: 2rem;
    margin-top: 2rem;

    h2 {
      margin: 0 0 1rem 0;
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--ava-text-primary);
    }

    .code-preview {
      background: var(--ava-surface-tertiary);
      border-radius: 8px;
      padding: 1.5rem;
      overflow-x: auto;

      pre {
        margin: 0;
        font-family: "SF Mono", "Monaco", "Inconsolata", monospace;
        font-size: 0.875rem;
        line-height: 1.6;
        color: var(--ava-text-primary);

        code {
          background: none;
          padding: 0;
          border-radius: 0;
        }
      }
    }
  }
}

// :host {
//   display: block;
//   width: 100%;
// }

// /* Prevent horizontal scroll */
// html, body {
//   margin: 0;
//   padding: 0;
//   overflow-x: hidden;
//   width: 100%;
// }

// * {
//   box-sizing: border-box;
// }

// /* Main layout */
// .documentation {
//   max-width: 1200px;
//   margin: 0 auto;
//   padding: 2rem;
//   width: 100%;
//   font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
//   overflow-x: hidden;
//   z-index: 1;

//   @media (max-width: 768px) {
//     padding: 1rem;
//   }
// }

// /* Header */
// .doc-header {
//   border-bottom: 1px solid var(--neutral-200);

//   h1 {
//     font-size: 2.5rem;
//     font-weight: 600;
//     color: var(--text-primary);
//     margin-bottom: 1rem;
//   }

//   .description {
//     font-size: 1.1rem;
//     color: var(--text-color-secondary);
//     line-height: 1.6;
//   }
// }

// /* Sections */
// .doc-sections {
//   margin-top: 4rem;
// }

// .doc-section {
//   margin-bottom: 1rem;

//   h2 {
//     font-size: 1.8rem;
//     font-weight: 500;
//     color: var(--text-primary);
//     margin-bottom: 1.5rem;
//   }

//   p {
//     color: var(--text-color-secondary);
//     font-size: 0.95rem;
//     line-height: 1.5;
//   }
// }

// /* Section header with toggle */
// .section-header {
//   display: flex;
//   flex-direction: column;
//   // position: relative;
//   cursor: pointer;
//   padding: 1rem;
//   background-color: var(--surface);
//   border-radius: var(--border-radius);

//   h2 {
//     margin-bottom: 0.5rem;
//   }

//   .description-container {
//     display: flex;
//     justify-content: space-between;
//     align-items: center;
//     flex-wrap: wrap;
//     gap: 0.5rem;
//   }

//   .code-toggle {
//     font-size: 0.75rem;
//     color: var(--icons-action);
//     cursor: pointer;
//     display: flex;
//     align-items: center;
//     font-weight: var(--font-font-weight-medium);
//     font-family: var(--font-font-family-heading);

//     &:hover {
//       text-decoration: underline;
//     }

//     span {
//       margin-right: 0.5rem;
//     }

//     awe-icons {
//       display: flex;
//       align-items: center;
//       justify-content: center;
//       width: 24px;
//       height: 24px;
//       line-height: 0;
//       padding: 0;
//       margin: 0;
//       vertical-align: middle;
//       flex-shrink: 0;

//       svg {
//         width: 60%;
//         height: 80%;
//         display: block;
//       }
//     }
//   }
// }

// // Code example styles
// .code-example {
//   margin-top: 1.5rem;

//   .example-preview {
//     padding: 1.5rem;
//     border: 1px solid var(--surface-border);
//     border-radius: var(--border-radius);
//     background-color: var(--surface-section);
//     margin-bottom: 1rem;
//   }

//   .code-block {
//     // position: relative;
//     background-color: var(--surface-section);
//     border-radius: 0.5rem;
//     padding: 1rem;
//     margin-top: 1rem;

//     pre {
//       margin: 0;
//       padding: 1rem;
//       background-color: var(--surface-ground);
//       border-radius: 0.25rem;
//       overflow-x: auto;
//     }

//     .copy-button {
//       position: absolute;
//       top: 0.5rem;
//       right: 0.5rem;
//       padding: 0.5rem;
//       background: transparent;
//       border: none;
//       cursor: pointer;
//       color: var(--text-color-secondary);

//       &:hover {
//         color: var(--primary-color);
//       }
//     }
//   }
// }

// // API table styles
// .api-table {
//   width: 100%;
//   border-collapse: collapse;
//   margin-top: 1rem;

//   th, td {
//     padding: 0.75rem;
//     text-align: left;
//     border-bottom: 1px solid var(--surface-border);

//   }

//   th {
//     background-color: var(--surface);
//     font-weight: 600;
//     color: var(--text-color-primary);
//   }

//   td {
//     color: var(--text-color-secondary);

//     code {
//       background-color: var(--surface);
//       padding: 0.2rem 0.4rem;
//       border-radius: var(--border-radius-sm);
//       font-family: monospace;
//     }
//   }
// }

// // Viewport Controls
// .viewport-tabs {
//   display: flex;
//   gap: 1rem;
//   margin-bottom: 1rem;
//   border-bottom: 1px solid var(--neutral-200);
//   padding-bottom: 0.5rem;
// }
//   .viewport-tab {
//     display: flex;
//     align-items: center;
//     gap: 0.5rem;
//     padding: 0.5rem;
//     background: none;
//     color: var(--neutral-600);
//     cursor: pointer;
//     transition: all 0.2s ease;

//     &:hover {
//       color: var(--primary-600);
//     }

//     &.active {
//       color: var(--primary-600);
//       border-bottom: 2px solid var(--primary-600);
//     }

//     awe-icons {
//       font-size: 1rem;
//     }
//   }

// // Viewport Preview
// .viewport-preview {
//   border: 1px solid var(--border-color);
//   border-radius: var(--border-radius);
//   padding: 1rem;
//   margin: 1rem auto;
//   background: var(--surface);
// }
//   .preview-section {
//     margin-bottom: 1rem;

//     h4 {
//       font-size: 1rem;
//       color:#000000;
//       margin-bottom: 1.5rem;
//     }

//     &:last-child {
//       margin-bottom: 0;
//     }
//   }

// // Grid Examples
// .grid-examples {
//   margin-top: 2rem;
// }
//   .grid-section {
//     margin-bottom: 2rem;
//     position: static !important;

//     h3 {
//       font-size: 1.25rem;
//       color: #000000;
//       margin-bottom: 0.5rem;
//     }

//     .example-description {
//       font-size: 0.9rem;
//       color:#000000;
//       margin-bottom: 1.5rem;
//     }
//   }

// // Responsive Adjustments
// @media (max-width: 768px) {
//   .documentation {
//     padding: 1rem;
//   }

//   .viewport-preview {
//     padding: 1rem;
//     margin: 1rem auto;
//   }

//   .preview-section {
//     margin-bottom: 1rem;
//   }

//   .viewport-controls {
//     flex-wrap: wrap;
//   }
// }
:host {
  display: block;
  width: 100%;
}

/* Prevent horizontal scroll */
html,
body {
  margin: 0;
  padding: 0;
  overflow-x: hidden;
  width: 100%;
}

* {
  box-sizing: border-box;
}

/* Main layout */
.documentation {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  width: 100%;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
  overflow-x: hidden;

  @media (max-width: 768px) {
    padding: 1rem;
  }
}

/* Header */
.doc-header {
  h1 {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
  }

  .description {
    font-size: 1.1rem;
    line-height: 1.6;
  }
}

/* Sections */
.doc-sections {
  margin-top: 4rem;
}

.doc-section {
  margin-bottom: 1rem;

  h2 {
    font-size: 1.8rem;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
  }

  p {
    font-size: 0.95rem;
    line-height: 1.5;
  }
}

/* Section header with toggle */
.section-header {
  display: flex;
  flex-direction: column;
  position: relative;
  cursor: pointer;
  padding: 1rem;
  background-color: var(--surface);
  border-radius: var(--border-radius);

  h2 {
    margin-bottom: 0.5rem;
  }

  .description-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .code-toggle {
    font-size: 0.75rem;
    color: var(--icons-action);
    cursor: pointer;
    display: flex;
    align-items: center;
    font-weight: var(--font-font-weight-medium);
    font-family: var(--font-font-family-heading);

    &:hover {
      text-decoration: underline;
    }

    span {
      margin-right: 0.5rem;
    }

    awe-icons {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      line-height: 0;
      padding: 0;
      margin: 0;
      vertical-align: middle;
      flex-shrink: 0;

      svg {
        width: 60%;
        height: 80%;
        display: block;
      }
    }
  }
}

/* Code example styles */
.code-example {
  margin-top: 1.5rem;

  .example-preview {
    padding: 1.5rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    border: 1px solid var(--surface-border);
  }

  .code-block {
    position: relative;
    border-radius: 0.5rem;
    margin-top: 1rem;
    border: 1px solid var(--surface-border);
    background-color: var(--surface-ground);

    pre {
      margin: 0;
      padding: 1rem;
      border-radius: 0.25rem;
      overflow-x: auto;
    }

    .copy-button {
      position: absolute;
      top: 0.5rem;
      right: 0.5rem;
      padding: 0.5rem;
      background: transparent;
      border: none;
      cursor: pointer;
      color: var(--text-color-secondary);

      &:hover {
        color: var(--primary-color);
      }
    }
  }
}

/* API table styles */
.api-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;

  th,
  td {
    padding: 0.75rem;
    text-align: left;
    border-bottom: 1px solid var(--surface-border);
  }

  th {
    background-color: var(--surface);
    font-weight: 600;
    color: var(--text-color-primary);
  }

  td {
    code {
      background-color: var(--surface);
      padding: 0.2rem 0.4rem;
      border-radius: var(--border-radius-sm);
      font-family: monospace;
    }
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .documentation {
    padding: 1rem;
  }
}

.left {
  text-align: left;
}

.position-preview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.position-button {
  text-align: center;
}
