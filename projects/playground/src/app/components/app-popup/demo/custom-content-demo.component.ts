import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { PopupComponent } from '../../../../../../play-comp-library/src/lib/components/popup/popup.component';
import { ButtonComponent } from '../../../../../../play-comp-library/src/lib/components/button/button.component';

@Component({
  selector: 'ava-popup-custom-content-demo',
  standalone: true,
  imports: [CommonModule, FormsModule, PopupComponent, ButtonComponent],
  template: `
    <div class="demo-container">
      <div class="demo-actions">
        <ava-button
          label="Settings Panel"
          variant="secondary"
          size="medium"
          state="default"
          (click)="showSettingsPanel = true"
        >
        </ava-button>
      </div>

      <!-- Contact Form Popup -->
      <ava-popup [show]="showContactForm" (closed)="showContactForm = false">
        <div class="popup-content contact-form">
          <div class="popup-header">
            <div class="header-content">
              <span class="header-icon">📧</span>
              <div>
                <h3>Get in Touch</h3>
                <p>We'd love to hear from you!</p>
              </div>
            </div>
          </div>

          <div class="popup-body">
            <form
              (submit)="submitContactForm($event)"
              class="contact-form-grid"
            >
              <div class="form-group">
                <label for="name">Full Name *</label>
                <input
                  type="text"
                  id="name"
                  [(ngModel)]="contactForm.name"
                  name="name"
                  placeholder="Enter your full name"
                  required
                />
                <div class="form-hint">
                  This will be used to address you in our response
                </div>
              </div>

              <div class="form-group">
                <label for="email">Email Address *</label>
                <input
                  type="email"
                  id="email"
                  [(ngModel)]="contactForm.email"
                  name="email"
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div class="form-group">
                <label for="company">Company</label>
                <input
                  type="text"
                  id="company"
                  [(ngModel)]="contactForm.company"
                  name="company"
                  placeholder="Your company name"
                />
              </div>

              <div class="form-group">
                <label for="subject">Subject *</label>
                <select
                  id="subject"
                  [(ngModel)]="contactForm.subject"
                  name="subject"
                  required
                >
                  <option value="">Select a topic</option>
                  <option value="general">General Inquiry</option>
                  <option value="support">Technical Support</option>
                  <option value="sales">Sales</option>
                  <option value="partnership">Partnership</option>
                </select>
              </div>

              <div class="form-group full-width">
                <label for="message">Message *</label>
                <textarea
                  id="message"
                  [(ngModel)]="contactForm.message"
                  name="message"
                  placeholder="Tell us how we can help you..."
                  rows="4"
                  required
                ></textarea>
              </div>

              <div class="form-actions full-width">
                <ava-button
                  label="Send Message"
                  variant="primary"
                  size="medium"
                  state="default"
                  type="submit"
                >
                </ava-button>
                <ava-button
                  label="Cancel"
                  variant="secondary"
                  size="medium"
                  state="default"
                  (click)="showContactForm = false"
                >
                </ava-button>
              </div>
            </form>

            <div *ngIf="contactSubmitted" class="success-message">
              <span class="success-icon">✅</span>
              <div>
                <strong>Message sent successfully!</strong>
                <p>
                  Thank you {{ contactForm.name }}! We'll get back to you within
                  24 hours.
                </p>
              </div>
            </div>
          </div>
        </div>
      </ava-popup>

      <!-- Product Showcase Popup -->
      <ava-popup [show]="showProductModal" (closed)="showProductModal = false">
        <div class="popup-content product-showcase">
          <div class="popup-header">
            <h3>Featured Product</h3>
            <button class="close-btn" (click)="showProductModal = false">
              ×
            </button>
          </div>

          <div class="popup-body">
            <div class="product-image">
              <div class="image-placeholder">
                <span class="product-icon">🎨</span>
              </div>
              <div class="product-badge">New</div>
            </div>

            <div class="product-info">
              <h4>AVA Design System Pro</h4>
              <p class="product-description">
                A comprehensive design system with 150+ components, design
                tokens, and advanced theming capabilities for enterprise
                applications.
              </p>

              <div class="product-features">
                <div class="feature">
                  <span class="feature-icon">⚡</span>
                  <span>Lightning fast</span>
                </div>
                <div class="feature">
                  <span class="feature-icon">🔧</span>
                  <span>Highly customizable</span>
                </div>
                <div class="feature">
                  <span class="feature-icon">📱</span>
                  <span>Mobile optimized</span>
                </div>
              </div>

              <div class="product-pricing">
                <span class="price">$99</span>
                <span class="price-period">/month</span>
              </div>

              <div class="product-actions">
                <ava-button
                  label="Start Free Trial"
                  variant="primary"
                  size="medium"
                  state="default"
                >
                </ava-button>
                <ava-button
                  label="Learn More"
                  variant="secondary"
                  size="medium"
                  state="default"
                >
                </ava-button>
              </div>
            </div>
          </div>
        </div>
      </ava-popup>

      <!-- Settings Panel Popup -->
      <ava-popup
        [show]="showSettingsPanel"
        (closed)="showSettingsPanel = false"
      >
        <div class="popup-content settings-panel">
          <div class="popup-header">
            <h3>⚙️ User Preferences</h3>
          </div>

          <div class="popup-body">
            <div class="settings-grid">
              <div class="setting-group">
                <h5>Appearance</h5>
                <div class="setting-item">
                  <label>
                    <input type="checkbox" [(ngModel)]="settings.darkMode" />
                    <span class="checkmark"></span>
                    Dark Mode
                  </label>
                </div>
                <div class="setting-item">
                  <label>
                    <input type="checkbox" [(ngModel)]="settings.animations" />
                    <span class="checkmark"></span>
                    Enable Animations
                  </label>
                </div>
              </div>

              <div class="setting-group">
                <h5>Notifications</h5>
                <div class="setting-item">
                  <label>
                    <input
                      type="checkbox"
                      [(ngModel)]="settings.emailNotifications"
                    />
                    <span class="checkmark"></span>
                    Email Notifications
                  </label>
                </div>
                <div class="setting-item">
                  <label>
                    <input
                      type="checkbox"
                      [(ngModel)]="settings.pushNotifications"
                    />
                    <span class="checkmark"></span>
                    Push Notifications
                  </label>
                </div>
              </div>

              <div class="setting-group">
                <h5>Language & Region</h5>
                <select [(ngModel)]="settings.language" class="setting-select">
                  <option value="en">English</option>
                  <option value="es">Español</option>
                  <option value="fr">Français</option>
                  <option value="de">Deutsch</option>
                </select>
              </div>
            </div>

            <div class="settings-actions">
              <ava-button
                label="Save Changes"
                variant="primary"
                size="small"
                state="default"
                (click)="saveSettings()"
              >
              </ava-button>
              <ava-button
                label="Reset to Defaults"
                variant="secondary"
                size="small"
                state="default"
                (click)="resetSettings()"
              >
              </ava-button>
            </div>
          </div>
        </div>
      </ava-popup>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 2rem;
        text-align: center;
      }

      .demo-description {
        color: var(--ava-text-secondary);
        margin-bottom: 2rem;
        font-size: 1.1rem;
      }

      .demo-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
      }

      /* Popup Content Base Styles */
      .popup-content {
        background: var(--ava-surface-primary);
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        border: 1px solid var(--ava-border-subtle);
      }

      .popup-header {
        padding: 1.5rem;
        background: var(--ava-surface-secondary);
        border-bottom: 1px solid var(--ava-border-subtle);
        position: relative;
      }

      .popup-header h3 {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--ava-text-primary);
      }

      .popup-body {
        padding: 1.5rem;
      }

      /* Contact Form Styles */
      .contact-form {
        min-width: 500px;
        max-width: 600px;
      }

      .header-content {
        display: flex;
        align-items: center;
        gap: 1rem;
      }

      .header-icon {
        font-size: 2rem;
      }

      .header-content h3 {
        margin-bottom: 0.25rem;
      }

      .header-content p {
        margin: 0;
        color: var(--ava-text-secondary);
        font-size: 0.9rem;
      }

      .contact-form-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1.25rem;
      }

      .form-group {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      }

      .full-width {
        grid-column: 1 / -1;
      }

      .form-group label {
        font-weight: 500;
        color: var(--ava-text-primary);
        font-size: 0.9rem;
      }

      .form-group input,
      .form-group select,
      .form-group textarea {
        padding: 0.75rem;
        border: 1px solid var(--ava-border-subtle);
        border-radius: 8px;
        font-size: 0.9rem;
        transition: border-color 0.2s ease;
      }

      .form-group input:focus,
      .form-group select:focus,
      .form-group textarea:focus {
        outline: none;
        border-color: var(--ava-primary-500);
        box-shadow: 0 0 0 3px var(--ava-primary-100);
      }

      .form-hint {
        font-size: 0.8rem;
        color: var(--ava-text-tertiary);
      }

      .form-actions {
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
        margin-top: 1rem;
      }

      .success-message {
        display: flex;
        align-items: center;
        gap: 1rem;
        padding: 1rem;
        background: var(--ava-success-50);
        border: 1px solid var(--ava-success-200);
        border-radius: 8px;
        margin-top: 1rem;
      }

      .success-icon {
        font-size: 1.5rem;
      }

      .success-message strong {
        color: var(--ava-success-700);
      }

      .success-message p {
        margin: 0.25rem 0 0 0;
        color: var(--ava-success-600);
        font-size: 0.9rem;
      }

      /* Product Showcase Styles */
      .product-showcase {
        min-width: 450px;
        max-width: 500px;
      }

      .close-btn {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: none;
        border: none;
        font-size: 1.5rem;
        cursor: pointer;
        color: var(--ava-text-secondary);
        width: 2rem;
        height: 2rem;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .close-btn:hover {
        background: var(--ava-surface-tertiary);
      }

      .product-image {
        position: relative;
        margin-bottom: 1.5rem;
      }

      .image-placeholder {
        width: 100%;
        height: 200px;
        background: linear-gradient(
          135deg,
          var(--ava-primary-100),
          var(--ava-primary-200)
        );
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .product-icon {
        font-size: 4rem;
      }

      .product-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: var(--ava-success-500);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
      }

      .product-info h4 {
        margin: 0 0 1rem 0;
        font-size: 1.5rem;
        color: var(--ava-text-primary);
      }

      .product-description {
        color: var(--ava-text-secondary);
        line-height: 1.6;
        margin-bottom: 1.5rem;
      }

      .product-features {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
        margin-bottom: 1.5rem;
      }

      .feature {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        font-size: 0.9rem;
      }

      .feature-icon {
        font-size: 1.1rem;
      }

      .product-pricing {
        display: flex;
        align-items: baseline;
        gap: 0.25rem;
        margin-bottom: 1.5rem;
      }

      .price {
        font-size: 2rem;
        font-weight: 700;
        color: var(--ava-primary-600);
      }

      .price-period {
        color: var(--ava-text-secondary);
      }

      .product-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
      }

      /* Settings Panel Styles */
      .settings-panel {
        min-width: 400px;
        max-width: 450px;
      }

      .settings-grid {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
      }

      .setting-group h5 {
        margin: 0 0 1rem 0;
        font-size: 1rem;
        font-weight: 600;
        color: var(--ava-text-primary);
        border-bottom: 1px solid var(--ava-border-subtle);
        padding-bottom: 0.5rem;
      }

      .setting-item {
        margin-bottom: 0.75rem;
      }

      .setting-item label {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        cursor: pointer;
        font-size: 0.9rem;
      }

      .setting-item input[type='checkbox'] {
        width: 1.2rem;
        height: 1.2rem;
      }

      .setting-select {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid var(--ava-border-subtle);
        border-radius: 8px;
        font-size: 0.9rem;
      }

      .settings-actions {
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 1px solid var(--ava-border-subtle);
        display: flex;
        gap: 1rem;
        justify-content: flex-end;
      }
    `,
  ],
})
export class PopupCustomContentDemoComponent {
  showContactForm = false;
  showProductModal = false;
  showSettingsPanel = false;
  contactSubmitted = false;

  contactForm = {
    name: '',
    email: '',
    company: '',
    subject: '',
    message: '',
  };

  settings = {
    darkMode: false,
    animations: true,
    emailNotifications: true,
    pushNotifications: false,
    language: 'en',
  };

  submitContactForm(event: Event) {
    event.preventDefault();
    this.contactSubmitted = true;

    // Reset form after a delay
    setTimeout(() => {
      this.contactSubmitted = false;
      this.contactForm = {
        name: '',
        email: '',
        company: '',
        subject: '',
        message: '',
      };
      this.showContactForm = false;
    }, 3000);
  }

  saveSettings() {
    // Simulate saving settings
    console.log('Settings saved:', this.settings);
    this.showSettingsPanel = false;
  }

  resetSettings() {
    this.settings = {
      darkMode: false,
      animations: true,
      emailNotifications: true,
      pushNotifications: false,
      language: 'en',
    };
  }
}
