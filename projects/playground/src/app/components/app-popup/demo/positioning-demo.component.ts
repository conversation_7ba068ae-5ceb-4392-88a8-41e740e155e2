import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PopupComponent } from '../../../../../../play-comp-library/src/lib/components/popup/popup.component';
import { ButtonComponent } from '../../../../../../play-comp-library/src/lib/components/button/button.component';

// Define the allowed position types
export type PopupPosition =
  | 'center'
  | 'top-left'
  | 'top-right'
  | 'bottom-left'
  | 'bottom-right'
  | 'top'
  | 'bottom';

@Component({
  selector: 'ava-popup-positioning-demo',
  standalone: true,
  imports: [CommonModule, PopupComponent, ButtonComponent],
  template: `
    <div class="demo-container">
      <div class="positions-layout">
        <!-- First row: 4 buttons -->
        <div class="positions-row row-4">
          <div class="position-button" *ngFor="let pos of firstRowPositions">
            <ava-button
              [label]="getPositionLabel(pos)"
              variant="primary"
              size="medium"
              state="default"
              (click)="openPosition = openPosition === pos ? '' : pos"
            >
            </ava-button>
          </div>
        </div>

        <!-- Second row: 3 buttons -->
        <div class="positions-row row-3">
          <div class="position-button" *ngFor="let pos of secondRowPositions">
            <ava-button
              [label]="getPositionLabel(pos)"
              variant="primary"
              size="medium"
              state="default"
              (click)="openPosition = openPosition === pos ? '' : pos"
            >
            </ava-button>
          </div>
        </div>

        <!-- Popups for all positions -->
        <ng-container *ngFor="let pos of positions">
          <ava-popup
            [show]="openPosition === pos"
            [position]="pos"
            (closed)="openPosition = ''"
          >
            <div class="popup-content" [class]="'popup-' + pos">
              <div class="popup-header">
                <span class="position-icon">{{ getPositionIcon(pos) }}</span>
                <h4>{{ getPositionTitle(pos) }}</h4>
              </div>
              <div class="popup-body">
                <p>{{ getPositionDescription(pos) }}</p>
                <div class="position-info">
                  <span class="info-label">Position:</span>
                  <span class="info-value">{{ pos }}</span>
                </div>
              </div>
              <div class="popup-footer">
                <ava-button
                  label="Close"
                  variant="secondary"
                  size="small"
                  state="default"
                  (click)="openPosition = ''"
                >
                </ava-button>
              </div>
            </div>
          </ava-popup>
        </ng-container>
      </div>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 1000px;
        margin: 0 auto;
        padding: 2rem;
        text-align: center;
      }

      .demo-description {
        color: var(--ava-text-secondary);
        margin-bottom: 2rem;
        font-size: 1.1rem;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
      }

      .positions-layout {
        display: flex;
        flex-direction: column;
        gap: 2rem;
        margin-top: 2rem;
      }

      .positions-row {
        display: grid;
        gap: 1.5rem;
        justify-content: center;
        align-items: center;
      }

      .row-4 {
        grid-template-columns: repeat(4, minmax(180px, 1fr));
        max-width: 800px;
        margin: 0 auto;
      }

      .row-3 {
        grid-template-columns: repeat(3, minmax(180px, 1fr));
        max-width: 600px;
        margin: 0 auto;
      }

      .position-button {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;
      }

      .popup-content {
        min-width: 280px;
        max-width: 320px;
        background: var(--ava-surface-primary);
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
        border: 1px solid var(--ava-border-subtle);
      }

      .popup-header {
        padding: 1.5rem 1.25rem 1rem;
        text-align: center;
        border-bottom: 1px solid var(--ava-border-subtle);
        background: var(--ava-surface-secondary);
      }

      .position-icon {
        font-size: 2rem;
        display: block;
        margin-bottom: 0.5rem;
      }

      .popup-header h4 {
        margin: 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--ava-text-primary);
      }

      .popup-body {
        padding: 1.25rem;
      }

      .popup-body p {
        color: var(--ava-text-secondary);
        font-size: 0.9rem;
        line-height: 1.5;
        margin-bottom: 1rem;
      }

      .position-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem;
        background: var(--ava-surface-tertiary);
        border-radius: 8px;
        font-size: 0.85rem;
      }

      .info-label {
        font-weight: 500;
        color: var(--ava-text-secondary);
      }

      .info-value {
        font-family: monospace;
        background: var(--ava-primary-100);
        color: var(--ava-primary-700);
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-weight: 500;
      }

      .popup-footer {
        padding: 1rem 1.25rem;
        text-align: center;
        border-top: 1px solid var(--ava-border-subtle);
        background: var(--ava-surface-secondary);
      }

      /* Position-specific styling */
      .popup-center .popup-header {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
      }
      .popup-top .popup-header {
        background: linear-gradient(135deg, #f093fb, #f5576c);
        color: white;
      }
      .popup-bottom .popup-header {
        background: linear-gradient(135deg, #4facfe, #00f2fe);
        color: white;
      }
      .popup-top-left .popup-header {
        background: linear-gradient(135deg, #43e97b, #38f9d7);
        color: white;
      }
      .popup-top-right .popup-header {
        background: linear-gradient(135deg, #fa709a, #fee140);
        color: white;
      }
      .popup-bottom-left .popup-header {
        background: linear-gradient(135deg, #a8edea, #fed6e3);
        color: #333;
      }
      .popup-bottom-right .popup-header {
        background: linear-gradient(135deg, #ffecd2, #fcb69f);
        color: #333;
      }
    `,
  ],
})
export class PopupPositioningDemoComponent {
  positions: PopupPosition[] = [
    'center',
    'top',
    'bottom',
    'top-left',
    'top-right',
    'bottom-left',
    'bottom-right',
  ];

  // First row: 4 buttons
  firstRowPositions: PopupPosition[] = ['center', 'top', 'bottom', 'top-left'];

  // Second row: 3 buttons
  secondRowPositions: PopupPosition[] = [
    'top-right',
    'bottom-left',
    'bottom-right',
  ];

  openPosition: PopupPosition | '' = '';

  getPositionLabel(pos: PopupPosition): string {
    const labels = {
      center: 'Center',
      top: 'Top',
      bottom: 'Bottom',
      'top-left': 'Top Left',
      'top-right': 'Top Right',
      'bottom-left': 'Bottom Left',
      'bottom-right': 'Bottom Right',
    };
    return labels[pos];
  }

  getPositionIcon(pos: PopupPosition): string {
    const icons = {
      center: '🎯',
      top: '⬆️',
      bottom: '⬇️',
      'top-left': '↖️',
      'top-right': '↗️',
      'bottom-left': '↙️',
      'bottom-right': '↘️',
    };
    return icons[pos];
  }

  getPositionTitle(pos: PopupPosition): string {
    const titles = {
      center: 'Center Position',
      top: 'Top Position',
      bottom: 'Bottom Position',
      'top-left': 'Top Left Corner',
      'top-right': 'Top Right Corner',
      'bottom-left': 'Bottom Left Corner',
      'bottom-right': 'Bottom Right Corner',
    };
    return titles[pos];
  }

  getPositionDescription(pos: PopupPosition): string {
    const descriptions = {
      center:
        'Perfect for important announcements and modal dialogs that need full user attention.',
      top: "Great for notifications and alerts that shouldn't obstruct the main content.",
      bottom: 'Ideal for confirmations and actions related to content above.',
      'top-left': 'Useful for contextual help and secondary information.',
      'top-right': 'Perfect for user account menus and settings panels.',
      'bottom-left': 'Good for status updates and progress indicators.',
      'bottom-right': 'Excellent for chat widgets and support tools.',
    };
    return descriptions[pos];
  }
}
