import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PopupComponent } from '../../../../../../play-comp-library/src/lib/components/popup/popup.component';
import { ButtonComponent } from '../../../../../../play-comp-library/src/lib/components/button/button.component';

@Component({
  selector: 'ava-popup-triggers-demo',
  standalone: true,
  imports: [CommonModule, PopupComponent, ButtonComponent],
  template: `
    <div class="demo-container">
      <h2>Trigger Types (Manual Only)</h2>
      <ava-button
        label="Manual Trigger"
        variant="primary"
        size="medium"
        state="default"
        (click)="show = !show"
      ></ava-button>
      <ava-popup [show]="show" (closed)="show = false">
        <div style="padding: 1rem; min-width: 160px;">
          Popup (Manual Trigger)
        </div>
      </ava-popup>
      <p style="margin-top:2rem; color:var(--ava-text-secondary);">
        Note: Only manual trigger is supported in this implementation.
      </p>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 900px;
        margin: 0 auto;
        padding: 2rem;
        text-align: center;
      }
    `,
  ],
})
export class PopupTriggersDemoComponent {
  show = false;
}
