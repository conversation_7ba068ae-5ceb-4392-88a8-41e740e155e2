import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PopupComponent } from '../../../../../../play-comp-library/src/lib/components/popup/popup.component';
import { ButtonComponent } from '../../../../../../play-comp-library/src/lib/components/button/button.component';

@Component({
  selector: 'ava-popup-basic-usage-demo',
  standalone: true,
  imports: [CommonModule, PopupComponent, ButtonComponent],
  template: `
    <div class="demo-container">
      <ava-button
        label="Show Welcome Message"
        variant="primary"
        size="medium"
        state="default"
        (click)="show = !show"
      >
      </ava-button>

      <ava-popup [show]="show" (closed)="show = false">
        <div class="popup-content">
          <div class="popup-header">
            <div class="icon-wrapper">
              <span class="welcome-icon">👋</span>
            </div>
            <h3>Welcome to AVA Components!</h3>
          </div>
          <div class="popup-body">
            <p>
              Thank you for exploring our component library. This popup
              demonstrates the basic usage of the AVA Popup component.
            </p>
            <div class="features-list">
              <div class="feature-item">
                <span class="feature-icon">✨</span>
                <span>Easy to implement</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">🎨</span>
                <span>Customizable content</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">♿</span>
                <span>Accessible by default</span>
              </div>
            </div>
          </div>
          <div class="popup-footer">
            <ava-button
              label="Got it!"
              variant="primary"
              size="small"
              state="default"
              (click)="show = false"
            >
            </ava-button>
          </div>
        </div>
      </ava-popup>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 700px;
        margin: 0 auto;
        padding: 2rem;
        text-align: center;
      }

      .demo-description {
        color: var(--ava-text-secondary);
        margin-bottom: 2rem;
        font-size: 1.1rem;
      }

      .popup-content {
        padding: 0;
        min-width: 380px;
        max-width: 450px;
        background: var(--ava-surface-primary);
        border-radius: 12px;
        overflow: auto;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      }

      .popup-header {
        background: linear-gradient(
          135deg,
          var(--ava-primary-500),
          var(--ava-primary-600)
        );
        color: white;
        padding: 2rem 1.5rem 1.5rem;
        text-align: center;
        position: relative;
      }

      .icon-wrapper {
        margin-bottom: 1rem;
      }

      .welcome-icon {
        font-size: 3rem;
        display: inline-block;
        animation: wave 2s ease-in-out infinite;
      }

      @keyframes wave {
        0%,
        100% {
          transform: rotate(0deg);
        }
        25% {
          transform: rotate(15deg);
        }
        75% {
          transform: rotate(-15deg);
        }
      }

      .popup-header h3 {
        margin: 0;
        font-size: 1.5rem;
        font-weight: 600;
      }

      .popup-body {
        padding: 1.5rem;
      }

      .popup-body p {
        color: var(--ava-text-secondary);
        line-height: 1.6;
        margin-bottom: 1.5rem;
      }

      .features-list {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
      }

      .feature-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.5rem;
        background: var(--ava-surface-secondary);
        border-radius: 8px;
        font-size: 0.9rem;
      }

      .feature-icon {
        font-size: 1.2rem;
      }

      .popup-footer {
        padding: 1rem 1.5rem 1.5rem;
        border-top: 1px solid var(--ava-border-subtle);
        background: var(--ava-surface-secondary);
        text-align: center;
      }
    `,
  ],
})
export class PopupBasicUsageDemoComponent {
  show = false;
}
