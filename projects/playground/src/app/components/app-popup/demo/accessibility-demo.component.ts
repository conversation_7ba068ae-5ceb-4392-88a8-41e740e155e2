import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PopupComponent } from '../../../../../../play-comp-library/src/lib/components/popup/popup.component';
import { ButtonComponent } from '../../../../../../play-comp-library/src/lib/components/button/button.component';

@Component({
  selector: 'ava-popup-accessibility-demo',
  standalone: true,
  imports: [CommonModule, PopupComponent, ButtonComponent],
  template: `
    <div class="demo-container">
      <h2>Accessibility</h2>
      <ava-button
        label="Open Accessible Popup"
        variant="primary"
        size="medium"
        state="default"
        (click)="show = !show"
      ></ava-button>
      <ava-popup
        [show]="show"
        (closed)="show = false"
        aria-label="Accessible Popup"
        role="dialog"
      >
        <div style="padding: 1.5rem; min-width: 220px;">
          <p tabindex="0">This popup is fully accessible.</p>
          <ava-button
            label="Close"
            variant="secondary"
            size="small"
            state="default"
            (click)="show = false"
          ></ava-button>
        </div>
      </ava-popup>
      <p style="margin-top: 2rem; color: var(--ava-text-secondary);">
        Try opening the popup and using Tab, Shift+Tab, and Escape to test
        accessibility features.
      </p>
    </div>
  `,
  styles: [
    `
      .demo-container {
        max-width: 700px;
        margin: 0 auto;
        padding: 2rem;
        text-align: center;
      }
    `,
  ],
})
export class PopupAccessibilityDemoComponent {
  show = false;
}
