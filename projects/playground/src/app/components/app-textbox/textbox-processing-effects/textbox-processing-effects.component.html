<div class="demo-container">
  <div class="demo-section">
    <ava-textbox
      [(ngModel)]="processingValue"
      label="Processing State"
      placeholder="Processing..."
      [processing]="true"
      (textboxChange)="onProcessingChange($event)"
    ></ava-textbox>
  </div>

  <div class="demo-section">
    <ava-textbox
      [(ngModel)]="shimmerValue"
      label="Shimmer Effect"
      placeholder="Validating..."
      [processing]="true"
      processingEffect="shimmer"
      (textboxChange)="onShimmerChange($event)"
    ></ava-textbox>
  </div>

  <div class="demo-section">
    <ava-textbox
      [(ngModel)]="gradientValue"
      label="Gradient Border"
      placeholder="Submitting..."
      [processing]="true"
      [processingGradientBorder]="true"
      (textboxChange)="onGradientChange($event)"
    ></ava-textbox>
  </div>
</div>
