<div class="demo-container">
  <div class="demo-section">
    <h3 style="color: var(--color-text-primary)">With Helper Text</h3>
    <ava-textbox
      [(ngModel)]="helperValue"
      label="Comments"
      placeholder="Share your thoughts..."
      helper="Please provide detailed feedback to help us improve our service."
      (textboxChange)="onHelperChange($event)"
    ></ava-textbox>
  </div>

  <div class="demo-section">
    <h3 style="color: var(--color-text-primary)">With Error State</h3>
    <ava-textbox
      [(ngModel)]="errorStateValue"
      label="Email"
      placeholder="Enter your email..."
      error="Please enter a valid email address."
      (textboxChange)="onErrorChange($event)"
    ></ava-textbox>
  </div>

  <div class="demo-section">
    <h3 style="color: var(--color-text-primary)">Required Field</h3>
    <ava-textbox
      [(ngModel)]="requiredValue"
      label="Required Field"
      placeholder="This field is required..."
      [required]="true"
      (textboxChange)="onRequiredChange($event)"
    ></ava-textbox>
  </div>
</div>
