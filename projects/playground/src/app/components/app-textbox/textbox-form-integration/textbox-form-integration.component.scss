.demo-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.demo-section {
  margin-bottom: 3rem;
  padding: 2rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;

  h3 {
    color: #1e293b;
    margin-bottom: 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
  }

  p {
    color: #64748b;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
  }
}

.form-row {
  margin-bottom: 1.5rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;

  &.btn-primary {
    background: #3b82f6;
    color: white;

    &:hover {
      background: #2563eb;
    }
  }

  &.btn-secondary {
    background: #6b7280;
    color: white;

    &:hover {
      background: #4b5563;
    }
  }
}

.value-display {
  margin-top: 1rem;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.875rem;
  color: #475569;
}

.validation-examples {
  display: grid;
  gap: 1.5rem;
}
