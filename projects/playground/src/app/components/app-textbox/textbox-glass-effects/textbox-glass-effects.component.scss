.demo-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;
}

.demo-section {
  margin-bottom: 3rem;
  padding: 2rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;

  h3 {
    color: #1e293b;
    margin-bottom: 0.5rem;
    font-size: 1.25rem;
    font-weight: 600;
  }

  p {
    color: #64748b;
    margin-bottom: 1.5rem;
    font-size: 0.875rem;
  }

  &.glass-demo-background {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;

    h3 {
      color: white;
    }

    p {
      color: rgba(255, 255, 255, 0.8);
    }
  }
}

.glass-examples {
  display: grid;
  gap: 1.5rem;
}
