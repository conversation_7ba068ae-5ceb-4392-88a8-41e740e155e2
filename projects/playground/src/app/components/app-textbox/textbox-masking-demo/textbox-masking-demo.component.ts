import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AvaTextboxComponent } from '../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-textbox-masking-demo',
  standalone: true,
  imports: [CommonModule, FormsModule, AvaTextboxComponent],
  templateUrl: './textbox-masking-demo.component.html',
  styleUrls: ['./textbox-masking-demo.component.scss'],
})
export class TextboxMaskingDemoComponent {
  phoneValue = '';
  currencyValue = '';
  dateValue = '';
  customValue = '';

  maskPhone = '(*************';
  maskCurrency = 'separator.2';
  thousand: ',' | '' = ',';
  decimal: '.' | ',' | ['.', ','] = '.';

  maskDate = '00/00/0000';

  customMask = 'SS-0000';
  customPatterns: Record<string, { pattern: RegExp }> = {
    S: { pattern: /[A-Za-z]/ },
    '0': { pattern: /\d/ },
  };
}
