.otp-demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;

  h2 {
    color: #1e293b;
    margin-bottom: 1rem;
    font-size: 2rem;
    font-weight: 700;
  }

  > p {
    color: #64748b;
    margin-bottom: 3rem;
    font-size: 1.125rem;
    line-height: 1.6;
  }
}

.variant-section {
  margin-bottom: 4rem;

  h3 {
    color: #1e293b;
    margin-bottom: 2rem;
    font-size: 1.5rem;
    font-weight: 600;
    border-bottom: 2px solid #e2e8f0;
    padding-bottom: 0.5rem;
  }
}

.variant-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.example-item {
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background: #ffffff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
  }
}

.value-display {
  margin-top: 1rem;
  padding: 0.75rem;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  color: #475569;
  word-break: break-all;
}

.clear-btn {
  margin-top: 0.75rem;
  padding: 0.5rem 1rem;
  background: #f1f5f9;
  border: 1px solid #cbd5e1;
  border-radius: 6px;
  color: #475569;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #e2e8f0;
    border-color: #94a3b8;
  }

  &:active {
    transform: translateY(1px);
  }
}

// Responsive design
@media (max-width: 768px) {
  .otp-demo-container {
    padding: 1rem;
  }

  .variant-examples {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .example-item {
    padding: 1rem;
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .otp-demo-container {
    h2 {
      color: #f1f5f9;
    }

    > p {
      color: #94a3b8;
    }
  }

  .variant-section h3 {
    color: #f1f5f9;
    border-bottom-color: #334155;
  }

  .example-item {
    background: #ffffff;
    border-color: #334155;
  }

  .value-display {
    background: #0f172a;
    border-color: #334155;
    color: #cbd5e1;
  }

  .clear-btn {
    background: #334155;
    border-color: #475569;
    color: #cbd5e1;

    &:hover {
      background: #475569;
      border-color: #64748b;
    }
  }
}
