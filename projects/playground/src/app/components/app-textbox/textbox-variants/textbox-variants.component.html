<div class="demo-container">
  <div class="demo-section">
    <ava-textbox
      [(ngModel)]="defaultValue"
      label="Default Variant"
      placeholder="Default variant..."
      variant="default"
      (textboxChange)="onDefaultChange($event)"
    ></ava-textbox>
  </div>

  <div class="demo-section">
    <ava-textbox
      [(ngModel)]="primaryValue"
      label="Primary Variant"
      placeholder="Primary variant..."
      variant="primary"
      (textboxChange)="onPrimaryChange($event)"
    ></ava-textbox>
  </div>

  <div class="demo-section">
    <ava-textbox
      [(ngModel)]="successValue"
      label="Success Variant"
      placeholder="Success variant..."
      variant="success"
      (textboxChange)="onSuccessChange($event)"
    ></ava-textbox>
  </div>

  <div class="demo-section">
    <ava-textbox
      [(ngModel)]="errorValue"
      label="Error Variant"
      placeholder="Error variant..."
      variant="error"
      (textboxChange)="onErrorChange($event)"
    ></ava-textbox>
  </div>

  <div class="demo-section">
    <ava-textbox
      [(ngModel)]="warningValue"
      label="Warning Variant"
      placeholder="Warning variant..."
      variant="warning"
      (textboxChange)="onWarningChange($event)"
    ></ava-textbox>
  </div>

  <div class="demo-section">
    <ava-textbox
      [(ngModel)]="infoValue"
      label="Info Variant"
      placeholder="Info variant..."
      variant="info"
      (textboxChange)="onInfoChange($event)"
    ></ava-textbox>
  </div>
</div>
