<div class="demo-container">
  <div class="demo-section">
    <ava-textbox
      [(ngModel)]="currencyValue"
      label="Amount"
      placeholder="0.00"
      type="number"
      (textboxChange)="onCurrencyChange($event)"
    >
      <span slot="prefix">$</span>
      <span slot="suffix">USD</span>
    </ava-textbox>
  </div>

  <div class="demo-section">
    <ava-textbox
      [(ngModel)]="passwordValue"
      label="Password"
      [type]="showPassword ? 'text' : 'password'"
      placeholder="Enter password"
    >
      <ava-icon
        slot="icon-end"
        [iconName]="showPassword ? 'eye' : 'eye-off'"
        (click)="togglePasswordVisibility()"
      ></ava-icon>
    </ava-textbox>
  </div>

  <div class="demo-section">
    <ava-textbox
      label="With Icon Separator (Start)"
      placeholder="Search..."
      [iconSeparator]="true"
    >
      <ava-icon slot="icon-start" iconName="search"></ava-icon>
    </ava-textbox>
  </div>

  <div class="demo-section">
    <ava-textbox
      label="With Icon Separator (End)"
      placeholder="Clear..."
      [iconSeparator]="true"
    >
      <ava-icon slot="icon-end" iconName="x"></ava-icon>
    </ava-textbox>
  </div>
</div>
