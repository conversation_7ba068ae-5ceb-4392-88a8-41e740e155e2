import {
  Component,
  ViewEncapsulation,
  CUSTOM_ELEMENTS_SCHEMA,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import {
  AvaTextboxComponent,
  TextboxVariant,
  TextboxSize,
} from '../../../../../play-comp-library/src/lib/components/textbox/ava-textbox.component';
import { IconComponent } from '../../../../../play-comp-library/src/lib/components/icon/icon.component';

@Component({
  selector: 'ava-app-textbox',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RouterModule,
    AvaTextboxComponent,
    IconComponent,
  ],
  templateUrl: './app-textbox.component.html',
  styleUrls: ['./app-textbox.component.scss'],
  encapsulation: ViewEncapsulation.None,
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AppTextboxComponent {
  // Demo form
  demoForm: FormGroup;

  // Basic usage values
  basicValue = '';
  emailValue = '';

  // Variant values
  defaultValue = '';
  primaryValue = '';
  successValue = '';
  errorValue = '';
  warningValue = '';
  infoValue = '';

  // Size values
  smallValue = '';
  mediumValue = '';
  largeValue = '';

  // Icons & affixes values
  searchValue = '';
  currencyValue = '';
  emailWithIconValue = '';
  percentageValue = '';

  // States & validation values
  helperValue = '';
  errorStateValue = '';
  requiredValue = '';
  maxLengthValue = '';

  // Processing effects values
  processingValue = '';
  shimmerValue = '';
  gradientValue = '';
  customGradientValue = '';

  // Glass & effects values
  glass10Value = '';
  glass50Value = '';
  tintValue = '';
  glowValue = '';

  // Available options for demo
  variants: TextboxVariant[] = [
    'default',
    'primary',
    'success',
    'error',
    'warning',
    'info',
  ];
  sizes: TextboxSize[] = ['sm', 'md', 'lg'];

  constructor(private fb: FormBuilder) {
    this.demoForm = this.fb.group({
      username: ['', [Validators.required, Validators.minLength(3)]],
      email: ['', [Validators.required, Validators.email]],
      message: [''],
    });
  }

  // Event handlers
  clearSearch(): void {
    this.searchValue = '';
    console.log('Search cleared');
  }

  onSubmit(): void {
    if (this.demoForm.valid) {
      console.log('Form submitted:', this.demoForm.value);
    } else {
      console.log('Form is invalid');
    }
  }

  getFieldError(fieldName: string): string {
    const field = this.demoForm.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.['required']) {
        return `${fieldName} is required`;
      }
      if (field.errors?.['email']) {
        return 'Please enter a valid email address';
      }
      if (field.errors?.['minlength']) {
        return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
      }
    }
    return '';
  }
}
