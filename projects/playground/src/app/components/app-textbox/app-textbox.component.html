<div class="textbox-demo-page">
  <!-- Header -->
  <div class="demo-header">
    <div class="container">
      <div class="row">
        <div class="col-12">
          <header class="doc-header">
            <h1>Textbox Component</h1>
            <p class="description">
              A sophisticated text input component with glass morphism, content
              projection, advanced processing effects, and comprehensive form
              integration. Built with accessibility and modern design
              principles.
            </p>
          </header>
        </div>
      </div>
    </div>
  </div>

  <!-- Navigation Links to Demo Sections -->
  <div class="demo-navigation">
    <div class="container">
      <div class="row">
        <div class="col-12">
          <div class="nav-links">
            <h3>Demo Sections</h3>
            <div class="nav-grid">
              <a routerLink="/textbox/basic-usage" class="nav-link">
                <span class="nav-icon">📝</span>
                <span class="nav-text">Basic Usage</span>
              </a>
              <a routerLink="/textbox/variants" class="nav-link">
                <span class="nav-icon">🎨</span>
                <span class="nav-text">Variants</span>
              </a>
              <a routerLink="/textbox/sizes" class="nav-link">
                <span class="nav-icon">📏</span>
                <span class="nav-text">Sizes</span>
              </a>
              <a routerLink="/textbox/icons-affixes" class="nav-link">
                <span class="nav-icon">🎯</span>
                <span class="nav-text">Icons & Affixes</span>
              </a>
              <a routerLink="/textbox/states-validation" class="nav-link">
                <span class="nav-icon">⚡</span>
                <span class="nav-text">States & Validation</span>
              </a>
              <a routerLink="/textbox/processing-effects" class="nav-link">
                <span class="nav-icon">✨</span>
                <span class="nav-text">Processing Effects</span>
              </a>
              <a routerLink="/textbox/form-integration" class="nav-link">
                <span class="nav-icon">🔗</span>
                <span class="nav-text">Form Integration</span>
              </a>
              <a routerLink="/textbox/glass-effects" class="nav-link">
                <span class="nav-icon">🔮</span>
                <span class="nav-text">Glass & Effects</span>
              </a>
              <a routerLink="/textbox/phone-number" class="nav-link">
                <span class="nav-icon">📱</span>
                <span class="nav-text">Phone Number</span>
              </a>
              <a routerLink="/textbox/otp" class="nav-link">
                <span class="nav-icon">🔐</span>
                <span class="nav-text">OTP Input</span>
              </a>
              <a routerLink="/textbox/masking" class="nav-link">
                <span class="nav-icon">🔤</span>
                <span class="nav-text">Masking</span>
              </a>
              <a routerLink="/textbox/api" class="nav-link">
                <span class="nav-icon">📚</span>
                <span class="nav-text">API Reference</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Demo Sections -->
  <div class="demo-sections">
    <!-- Basic Usage -->
    <section class="demo-section basic-bg">
      <div class="container">
        <div class="section-header">
          <h2>Basic Usage</h2>
          <p>
            Simple textbox implementation with label, placeholder, and two-way
            data binding
          </p>
        </div>
        <div class="demo-content">
          <div class="row g-3">
            <div class="col-12 col-md-6">
              <ava-textbox
                label="Basic Input"
                placeholder="Enter text here"
                [(ngModel)]="basicValue"
              ></ava-textbox>
            </div>
            <div class="col-12 col-md-6">
              <ava-textbox
                label="Email Input"
                type="email"
                placeholder="<EMAIL>"
                [(ngModel)]="emailValue"
              ></ava-textbox>
            </div>
            <div class="col-12 col-md-6">
              <ava-textbox
                label="Disabled Input"
                placeholder="Cannot edit"
                [value]="'Disabled input'"
                [disabled]="true"
              ></ava-textbox>
            </div>
            <div class="col-12 col-md-6">
              <ava-textbox
                label="Read Only"
                placeholder="Cannot edit"
                [value]="'Read only value'"
                [readonly]="true"
              ></ava-textbox>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Variants -->
    <section class="demo-section variants-bg">
      <div class="container">
        <div class="section-header">
          <h2>Textbox Variants</h2>
          <p>
            Six semantic variants that control visual appearance and focus
            colors
          </p>
        </div>
        <div class="demo-content">
          <div class="row g-3">
            <div class="col-12 col-md-6 col-lg-4">
              <ava-textbox
                label="Default Variant"
                placeholder="Default variant..."
                variant="default"
                [(ngModel)]="defaultValue"
              ></ava-textbox>
            </div>
            <div class="col-12 col-md-6 col-lg-4">
              <ava-textbox
                label="Primary Variant"
                placeholder="Primary variant..."
                variant="primary"
                [(ngModel)]="primaryValue"
              ></ava-textbox>
            </div>
            <div class="col-12 col-md-6 col-lg-4">
              <ava-textbox
                label="Success Variant"
                placeholder="Success variant..."
                variant="success"
                [(ngModel)]="successValue"
              ></ava-textbox>
            </div>
            <div class="col-12 col-md-6 col-lg-4">
              <ava-textbox
                label="Error Variant"
                placeholder="Error variant..."
                variant="error"
                [(ngModel)]="errorValue"
              ></ava-textbox>
            </div>
            <div class="col-12 col-md-6 col-lg-4">
              <ava-textbox
                label="Warning Variant"
                placeholder="Warning variant..."
                variant="warning"
                [(ngModel)]="warningValue"
              ></ava-textbox>
            </div>
            <div class="col-12 col-md-6 col-lg-4">
              <ava-textbox
                label="Info Variant"
                placeholder="Info variant..."
                variant="info"
                [(ngModel)]="infoValue"
              ></ava-textbox>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Sizes -->
    <section class="demo-section sizes-bg">
      <div class="container">
        <div class="section-header">
          <h2>Textbox Sizes</h2>
          <p>Three size options to accommodate different interface densities</p>
        </div>
        <div class="demo-content">
          <div class="row g-3 align-items-end">
            <div class="col-12 col-md-4">
              <ava-textbox
                label="Small Size"
                placeholder="Small textbox..."
                size="sm"
                [(ngModel)]="smallValue"
              ></ava-textbox>
            </div>
            <div class="col-12 col-md-4">
              <ava-textbox
                label="Medium Size (Default)"
                placeholder="Medium textbox..."
                size="md"
                [(ngModel)]="mediumValue"
              ></ava-textbox>
            </div>
            <div class="col-12 col-md-4">
              <ava-textbox
                label="Large Size"
                placeholder="Large textbox..."
                size="sm"
                [(ngModel)]="largeValue"
              ></ava-textbox>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Icons & Affixes -->
    <section class="demo-section icons-bg">
      <div class="container">
        <div class="section-header">
          <h2>Icons & Affixes</h2>
          <p>
            Advanced content projection system supporting icons, prefixes, and
            suffixes
          </p>
        </div>
        <div class="demo-content">
          <div class="row g-3">
            <div class="col-12 col-md-6">
              <ava-textbox
                label="Search with Icons"
                placeholder="Search..."
                [(ngModel)]="searchValue"
              >
                <ava-icon slot="icon-start" iconName="search"></ava-icon>
                <ava-icon
                  slot="icon-end"
                  iconName="x"
                  (click)="clearSearch()"
                ></ava-icon>
              </ava-textbox>
            </div>
            <div class="col-12 col-md-6">
              <ava-textbox
                label="Currency Input"
                placeholder="0.00"
                [(ngModel)]="currencyValue"
              >
                <span slot="prefix">$</span>
                <span slot="suffix">USD</span>
              </ava-textbox>
            </div>
            <div class="col-12 col-md-6">
              <ava-textbox
                label="Email with Icon"
                type="email"
                placeholder="Enter email"
                [(ngModel)]="emailWithIconValue"
              >
                <ava-icon slot="icon-start" iconName="mail"></ava-icon>
              </ava-textbox>
            </div>
            <div class="col-12 col-md-6">
              <ava-textbox
                label="Percentage"
                placeholder="0"
                [(ngModel)]="percentageValue"
              >
                <span slot="suffix">%</span>
              </ava-textbox>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- States & Validation -->
    <section class="demo-section states-bg">
      <div class="container">
        <div class="section-header">
          <h2>States & Validation</h2>
          <p>
            Comprehensive validation system with error messages and helper text
          </p>
        </div>
        <div class="demo-content">
          <div class="row g-3">
            <div class="col-12 col-md-6">
              <ava-textbox
                label="With Helper Text"
                placeholder="Enter your comments..."
                helper="Please provide detailed feedback to help us improve our service."
                [(ngModel)]="helperValue"
              ></ava-textbox>
            </div>
            <div class="col-12 col-md-6">
              <ava-textbox
                label="With Error State"
                placeholder="Enter your email..."
                error="Please enter a valid email address."
                [(ngModel)]="errorStateValue"
              ></ava-textbox>
            </div>
            <div class="col-12 col-md-6">
              <ava-textbox
                label="Required Field"
                placeholder="This field is required..."
                [required]="true"
                [(ngModel)]="requiredValue"
              ></ava-textbox>
            </div>
            <div class="col-12 col-md-6">
              <ava-textbox
                label="Character Limit"
                placeholder="Max 50 characters..."
                [maxlength]="50"
                [(ngModel)]="maxLengthValue"
              ></ava-textbox>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Processing Effects -->
    <section class="demo-section processing-bg">
      <div class="container">
        <div class="section-header">
          <h2>Processing Effects</h2>
          <p>Advanced processing states with multiple animation options</p>
        </div>
        <div class="demo-content">
          <div class="row g-3">
            <div class="col-12 col-md-6">
              <ava-textbox
                label="Processing State"
                placeholder="Processing..."
                [processing]="true"
                [(ngModel)]="processingValue"
              ></ava-textbox>
            </div>
            <div class="col-12 col-md-6">
              <ava-textbox
                label="Shimmer Effect"
                placeholder="Validating..."
                [processing]="true"
                processingEffect="shimmer"
                [(ngModel)]="shimmerValue"
              ></ava-textbox>
            </div>
            <div class="col-12 col-md-6">
              <ava-textbox
                label="Gradient Border"
                placeholder="Submitting..."
                [processing]="true"
                [processingGradientBorder]="true"
                [(ngModel)]="gradientValue"
              ></ava-textbox>
            </div>
            <div class="col-12 col-md-6">
              <ava-textbox
                label="Custom Colors"
                placeholder="Custom processing..."
                [processing]="true"
                [processingGradientBorder]="true"
                [processingGradientColors]="['#ff6b6b', '#4ecdc4', '#45b7d1']"
                [(ngModel)]="customGradientValue"
              ></ava-textbox>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Form Integration -->
    <section class="demo-section form-bg">
      <div class="container">
        <div class="section-header">
          <h2>Form Integration</h2>
          <p>
            Complete Angular forms integration with reactive and template-driven
            forms
          </p>
        </div>
        <div class="demo-content">
          <form [formGroup]="demoForm" (ngSubmit)="onSubmit()">
            <div class="row g-3">
              <div class="col-12 col-md-6">
                <ava-textbox
                  label="Username"
                  placeholder="Enter username"
                  formControlName="username"
                  [error]="getFieldError('username')"
                  helper="3-20 characters, letters and numbers only"
                ></ava-textbox>
              </div>
              <div class="col-12 col-md-6">
                <ava-textbox
                  label="Email"
                  type="email"
                  placeholder="Enter email"
                  formControlName="email"
                  [error]="getFieldError('email')"
                  helper="We'll never share your email"
                ></ava-textbox>
              </div>
              <div class="col-12">
                <ava-textbox
                  label="Message"
                  placeholder="Enter your message..."
                  formControlName="message"
                  [rows]="3"
                ></ava-textbox>
              </div>
              <div class="col-12">
                <button type="submit" class="btn btn-primary">
                  Submit Form
                </button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </section>

    <!-- Glass & Effects -->
    <section class="demo-section glass-bg">
      <div class="container">
        <div class="section-header">
          <h2>Glass & Effects</h2>
          <p>
            Advanced glass morphism system with hover, pressed, and focus
            effects
          </p>
        </div>
        <div class="demo-content">
          <div class="glass-demo-background">
            <div class="row g-3">
              <div class="col-12 col-md-6">
                <ava-textbox
                  label="Glass 10 (Recommended)"
                  placeholder="Subtle glass effect..."
                  glassVariant="glass-10"
                  [(ngModel)]="glass10Value"
                ></ava-textbox>
              </div>
              <div class="col-12 col-md-6">
                <ava-textbox
                  label="Glass 50 (Medium)"
                  placeholder="Medium glass intensity..."
                  glassVariant="glass-50"
                  [(ngModel)]="glass50Value"
                ></ava-textbox>
              </div>
              <div class="col-12 col-md-6">
                <ava-textbox
                  label="Tint Hover Effect"
                  placeholder="Hover for tint effect..."
                  hoverEffect="tint"
                  [(ngModel)]="tintValue"
                ></ava-textbox>
              </div>
              <div class="col-12 col-md-6">
                <ava-textbox
                  label="Glow Hover Effect"
                  placeholder="Hover for glow effect..."
                  hoverEffect="glow"
                  [(ngModel)]="glowValue"
                ></ava-textbox>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</div>
