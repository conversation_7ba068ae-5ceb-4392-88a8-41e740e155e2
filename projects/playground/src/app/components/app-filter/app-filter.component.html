<div class="filter-demo-container">
  
  <!-- ===== 1. Basic Filter Example ===== -->
  <section class="demo-section">
    <h2>1. Basic Product Filter</h2>
    <div class="filter-section">
      <ava-filter
        size="md"
        title="Filter Products"
        [filterGroups]="filterGroups"
        (filterChange)="onFilterChange($event)"
        (clearAll)="onClearAllFilters()"
        (toggleFilter)="onToggleFilter($event)">
      </ava-filter>
    </div>

    <div class="results-section">
      <h3>Products ({{ filteredProducts.length }} results)</h3>
      <div class="products-grid">
        <div class="product-card" *ngFor="let product of filteredProducts">
          <h4>{{ product.name }}</h4>
          <p>Category: {{ product.category }}</p>
          <p class="price">${{ product.price }}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- ===== 2. User Management Filter Example ===== -->
  <section class="demo-section">
    <h2>2. User Management Filter</h2>
    <div class="filter-section">
      <ava-filter
        size="lg"
        title="Filter Users"
        [filterGroups]="userFilterGroups"
        [showApplyButton]="true"
        position="right"
        (filterChange)="onUserFilterChange($event)"
        (clearAll)="onClearUserFilters()"
        (apply)="onApplyUserFilters($event)">
      </ava-filter>
    </div>

    <div class="results-section">
      <h3>Users ({{ filteredUsers.length }} results)</h3>
      <div class="users-table">
        <table>
          <thead>
            <tr>
              <th>Name</th>
              <th>Status</th>
              <th>Role</th>
              <th>Department</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let user of filteredUsers">
              <td>{{ user.name }}</td>
              <td>
                <span class="status-badge" [ngClass]="'status-' + user.status">
                  {{ user.status }}
                </span>
              </td>
              <td>{{ user.role }}</td>
              <td>{{ user.department }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </section>

  <!-- ===== 3. Single Selection Filter Example ===== -->
  <section class="demo-section">
    <h2>3. Sort Filter (Single Selection)</h2>
    <div class="filter-section">
      <ava-filter
        size="sm"
        title="Sort"
        [filterGroups]="sortFilterGroups"
        [showClearAll]="false"
        (filterChange)="onSortChange($event)">
      </ava-filter>
    </div>

    <div class="results-section">
      <h3>Sorted Products</h3>
      <div class="products-grid">
        <div class="product-card" *ngFor="let product of sortedProducts; let i = index">
          <span class="order-number">#{{ i + 1 }}</span>
          <h4>{{ product.name }}</h4>
          <p>Category: {{ product.category }}</p>
          <p class="price">${{ product.price }}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- ===== 4. Advanced Filter Example ===== -->
  <section class="demo-section">
    <h2>4. Advanced Filter</h2>
    <div class="filter-section">
      <ava-filter
        size="xlg"
        title="Advanced Filter"
        [filterGroups]="advancedFilterGroups"
        [showApplyButton]="true"
        maxHeight="500px"
        width="400px"
        (filterChange)="onAdvancedFilterChange($event)"
        (clearAll)="onClearAdvancedFilters()"
        (apply)="onApplyAdvancedFilters($event)">
      </ava-filter>
    </div>

    <div class="results-section">
      <h3>Advanced Products ({{ filteredAdvancedProducts.length }} results)</h3>
      <div class="advanced-products-grid">
        <div class="advanced-product-card" *ngFor="let product of filteredAdvancedProducts">
          <h4>{{ product.name }}</h4>
          <div class="tags">
            <span class="tag" *ngFor="let tag of product.tags">{{ tag }}</span>
          </div>
          <p class="availability">{{ product.availability | titlecase }}</p>
          <p class="price">${{ product.price }}</p>
        </div>
      </div>
    </div>
  </section>

  <!-- ===== 5. All Size Variants Example ===== -->
  <section class="demo-section">
    <h2>5. Size Variants Comparison</h2>
    
    <div class="size-variants-grid">
      <!-- Small Filter -->
      <div class="size-variant">
        <h4>Small (sm)</h4>
        <ava-filter
          size="sm"
          title="Quick Filter"
          [filterGroups]="simpleFilterGroups">
        </ava-filter>
      </div>

      <!-- Medium Filter (Default) -->
      <div class="size-variant">
        <h4>Medium (md) - Default</h4>
        <ava-filter
          size="md"
          title="Filter"
          [filterGroups]="filterGroups">
        </ava-filter>
      </div>

      <!-- Large Filter -->
      <div class="size-variant">
        <h4>Large (lg)</h4>
        <ava-filter
          size="lg"
          title="Advanced Filter"
          [filterGroups]="detailedFilterGroups"
          [showApplyButton]="true">
        </ava-filter>
      </div>

      <!-- Extra Large Filter -->
      <div class="size-variant">
        <h4>Extra Large (xlg)</h4>
        <ava-filter
          size="xlg"
          title="Comprehensive Filter"
          [filterGroups]="comprehensiveFilterGroups"
          [showApplyButton]="true"
          maxHeight="600px"
          width="450px">
        </ava-filter>
      </div>
    </div>
  </section>

  <!-- ===== 6. Configuration Examples ===== -->
  <section class="demo-section">
    <h2>6. Configuration Examples</h2>
    
    <div class="config-examples">
      <!-- No Clear All Button -->
      <div class="config-example">
        <h4>No Clear All Button</h4>
        <ava-filter
          size="md"
          title="No Clear All"
          [filterGroups]="simpleFilterGroups"
          [showClearAll]="false">
        </ava-filter>
      </div>

      <!-- With Apply Button -->
      <div class="config-example">
        <h4>With Apply Button</h4>
        <ava-filter
          size="md"
          title="Apply Mode"
          [filterGroups]="simpleFilterGroups"
          [showApplyButton]="true">
        </ava-filter>
      </div>

      <!-- Right Positioned -->
      <div class="config-example">
        <h4>Right Positioned Panel</h4>
        <ava-filter
          size="md"
          title="Right Panel"
          [filterGroups]="simpleFilterGroups"
          position="right">
        </ava-filter>
      </div>

      <!-- Disabled State -->
      <div class="config-example">
        <h4>Disabled State</h4>
        <ava-filter
          size="md"
          title="Disabled Filter"
          [filterGroups]="simpleFilterGroups"
          [disabled]="true">
        </ava-filter>
      </div>
    </div>
  </section>

</div>