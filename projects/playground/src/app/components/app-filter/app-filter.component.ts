import { Component, OnInit } from '@angular/core';
import { FilterComponent } from "../../../../../play-comp-library/src/lib/composite-components/filter/filter.component";
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-app-filter',
  standalone: true,
  imports: [FilterComponent, CommonModule],
  templateUrl: './app-filter.component.html',
  styleUrl: './app-filter.component.scss'
})
export class AppFilterComponent implements OnInit {

  // ===== 1. Basic Filter Example =====
  filterGroups = [
    {
      id: 'category',
      title: 'Category',
      options: [
        { id: 'electronics', label: 'Electronics', value: 'electronics' },
        { id: 'clothing', label: 'Clothing', value: 'clothing' },
        { id: 'books', label: 'Books', value: 'books' }
      ]
    },
    {
      id: 'price',
      title: 'Price Range',
      options: [
        { id: 'under-50', label: 'Under $50', value: { min: 0, max: 50 } },
        { id: '50-100', label: '$50 - $100', value: { min: 50, max: 100 } },
        { id: 'over-100', label: 'Over $100', value: { min: 100, max: Infinity } }
      ]
    }
  ];

  products = [
    { id: 1, name: 'Laptop', category: 'electronics', price: 999 },
    { id: 2, name: 'MacBook Pro', category: 'electronics', price: 1299 },
    { id: 3, name: 'iPhone', category: 'electronics', price: 799 },
    { id: 4, name: 'T-Shirt', category: 'clothing', price: 25 },
    { id: 5, name: 'Jeans', category: 'clothing', price: 45 },
    { id: 6, name: 'Sneakers', category: 'clothing', price: 85 },
    { id: 7, name: 'Novel', category: 'books', price: 15 },
    { id: 8, name: 'Cookbook', category: 'books', price: 35 },
    { id: 9, name: 'Technical Manual', category: 'books', price: 65 }
  ];

  filteredProducts = [...this.products];

  // ===== 2. User Management Filter Example =====
  userFilterGroups = [
    {
      id: 'status',
      title: 'Status',
      options: [
        { id: 'active', label: 'Active', value: 'active', selected: true },
        { id: 'inactive', label: 'Inactive', value: 'inactive' },
        { id: 'pending', label: 'Pending', value: 'pending' }
      ]
    },
    {
      id: 'role',
      title: 'Role',
      options: [
        { id: 'admin', label: 'Admin', value: 'admin' },
        { id: 'user', label: 'User', value: 'user' },
        { id: 'moderator', label: 'Moderator', value: 'moderator' }
      ]
    },
    {
      id: 'department',
      title: 'Department',
      options: [
        { id: 'it', label: 'IT', value: 'IT' },
        { id: 'hr', label: 'HR', value: 'HR' },
        { id: 'finance', label: 'Finance', value: 'Finance' },
        { id: 'marketing', label: 'Marketing', value: 'Marketing' }
      ]
    }
  ];

  users = [
    { id: 1, name: 'John Doe', status: 'active', role: 'admin', department: 'IT' },
    { id: 2, name: 'Jane Smith', status: 'active', role: 'user', department: 'HR' },
    { id: 3, name: 'Bob Johnson', status: 'inactive', role: 'moderator', department: 'Finance' },
    { id: 4, name: 'Alice Brown', status: 'active', role: 'user', department: 'Marketing' },
    { id: 5, name: 'Charlie Wilson', status: 'pending', role: 'user', department: 'IT' },
    { id: 6, name: 'Diana Davis', status: 'active', role: 'admin', department: 'HR' }
  ];

  filteredUsers = [...this.users];

  // ===== 3. Single Selection Filter Example =====
  sortFilterGroups = [
    {
      id: 'sortBy',
      title: 'Sort By',
      multiSelect: false, // Single selection only
      options: [
        { id: 'name', label: 'Name (A-Z)', value: 'name_asc' },
        { id: 'price_low', label: 'Price (Low to High)', value: 'price_asc' },
        { id: 'price_high', label: 'Price (High to Low)', value: 'price_desc' },
        { id: 'newest', label: 'Newest First', value: 'date_desc' }
      ]
    }
  ];

  sortedProducts = [...this.products];

  // ===== 4. Advanced Filter Example =====
  advancedFilterGroups = [
    {
      id: 'tags',
      title: 'Tags',
      options: [
        { id: 'featured', label: 'Featured', value: 'featured' },
        { id: 'sale', label: 'On Sale', value: 'sale' },
        { id: 'new', label: 'New Arrival', value: 'new' }
      ]
    },
    {
      id: 'availability',
      title: 'Availability',
      options: [
        { id: 'in-stock', label: 'In Stock', value: 'in_stock' },
        { id: 'out-stock', label: 'Out of Stock', value: 'out_stock' },
        { id: 'pre-order', label: 'Pre-order', value: 'pre_order' }
      ]
    }
  ];

  advancedProducts = [
    { id: 1, name: 'Gaming Laptop', tags: ['featured', 'new'], availability: 'in_stock', price: 1299 },
    { id: 2, name: 'Wireless Mouse', tags: ['sale'], availability: 'in_stock', price: 29 },
    { id: 3, name: 'Mechanical Keyboard', tags: ['featured'], availability: 'out_stock', price: 149 },
    { id: 4, name: 'Monitor Stand', tags: ['new'], availability: 'pre_order', price: 89 }
  ];

  filteredAdvancedProducts = [...this.advancedProducts];

  // ===== 5. Size Variants Examples =====
  simpleFilterGroups = [
    {
      id: 'simple',
      title: 'Quick Filter',
      options: [
        { id: 'option1', label: 'Option 1', value: 'opt1' },
        { id: 'option2', label: 'Option 2', value: 'opt2' }
      ]
    }
  ];

  detailedFilterGroups = [
    {
      id: 'detailed1',
      title: 'Category',
      options: [
        { id: 'cat1', label: 'Category 1', value: 'cat1' },
        { id: 'cat2', label: 'Category 2', value: 'cat2' },
        { id: 'cat3', label: 'Category 3', value: 'cat3' }
      ]
    },
    {
      id: 'detailed2',
      title: 'Type',
      options: [
        { id: 'type1', label: 'Type A', value: 'typeA' },
        { id: 'type2', label: 'Type B', value: 'typeB' }
      ]
    }
  ];

  comprehensiveFilterGroups = [
    {
      id: 'comprehensive1',
      title: 'Primary Category',
      options: [
        { id: 'prim1', label: 'Primary Option 1', value: 'prim1' },
        { id: 'prim2', label: 'Primary Option 2', value: 'prim2' },
        { id: 'prim3', label: 'Primary Option 3', value: 'prim3' }
      ]
    },
    {
      id: 'comprehensive2',
      title: 'Secondary Category',
      options: [
        { id: 'sec1', label: 'Secondary Option 1', value: 'sec1' },
        { id: 'sec2', label: 'Secondary Option 2', value: 'sec2' },
        { id: 'sec3', label: 'Secondary Option 3', value: 'sec3' }
      ]
    },
    {
      id: 'comprehensive3',
      title: 'Tertiary Category',
      options: [
        { id: 'ter1', label: 'Tertiary Option 1', value: 'ter1' },
        { id: 'ter2', label: 'Tertiary Option 2', value: 'ter2' },
        { id: 'ter3', label: 'Tertiary Option 3', value: 'ter3' }
      ]
    }
  ];

  constructor() { }

  ngOnInit(): void {
    // Initialize filtered data
    this.filteredProducts = [...this.products];
    this.filteredUsers = [...this.users];
    this.sortedProducts = [...this.products];
    this.filteredAdvancedProducts = [...this.advancedProducts];
  }

  // ===== Event Handlers =====

  // 1. Basic Product Filter
  onFilterChange(filters: any): void {
    console.log('Filter changed:', filters);
    
    this.filteredProducts = this.products.filter(product => {
      // Category filter
      if (filters.category?.length > 0) {
        const categoryMatch = filters.category.some((f: any) => f.value === product.category);
        if (!categoryMatch) return false;
      }

      // Price filter
      if (filters.price?.length > 0) {
        const priceMatch = filters.price.some((f: any) => 
          product.price >= f.value.min && product.price <= f.value.max
        );
        if (!priceMatch) return false;
      }

      return true;
    });
  }

  // 2. User Management Filter
  onUserFilterChange(filters: any): void {
    console.log('User filter changed:', filters);
    
    this.filteredUsers = this.users.filter(user => {
      // Status filter
      if (filters.status?.length > 0) {
        const statusMatch = filters.status.some((f: any) => f.value === user.status);
        if (!statusMatch) return false;
      }

      // Role filter
      if (filters.role?.length > 0) {
        const roleMatch = filters.role.some((f: any) => f.value === user.role);
        if (!roleMatch) return false;
      }

      // Department filter
      if (filters.department?.length > 0) {
        const deptMatch = filters.department.some((f: any) => f.value === user.department);
        if (!deptMatch) return false;
      }

      return true;
    });
  }

  // 3. Sort Filter
  onSortChange(filters: any): void {
    console.log('Sort changed:', filters);
    
    const sortOption = filters.sortBy?.[0];
    if (sortOption) {
      this.sortProducts(sortOption.value);
    }
  }

  private sortProducts(sortBy: string): void {
    this.sortedProducts = [...this.products];
    
    switch (sortBy) {
      case 'name_asc':
        this.sortedProducts.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'price_asc':
        this.sortedProducts.sort((a, b) => a.price - b.price);
        break;
      case 'price_desc':
        this.sortedProducts.sort((a, b) => b.price - a.price);
        break;
      case 'date_desc':
        // For demo purposes, sort by ID (assuming higher ID = newer)
        this.sortedProducts.sort((a, b) => b.id - a.id);
        break;
    }
  }

  // 4. Advanced Filter
  onAdvancedFilterChange(filters: any): void {
    console.log('Advanced filter changed:', filters);
    
    this.filteredAdvancedProducts = this.advancedProducts.filter(product => {
      // Tags filter
      if (filters.tags?.length > 0) {
        const tagMatch = filters.tags.some((f: any) => 
          product.tags.includes(f.value)
        );
        if (!tagMatch) return false;
      }

      // Availability filter
      if (filters.availability?.length > 0) {
        const availabilityMatch = filters.availability.some((f: any) => 
          f.value === product.availability
        );
        if (!availabilityMatch) return false;
      }

      return true;
    });
  }

  // Clear handlers
  onClearAllFilters(): void {
    console.log('All filters cleared');
    this.filteredProducts = [...this.products];
  }

  onClearUserFilters(): void {
    console.log('User filters cleared');
    this.filteredUsers = [...this.users];
  }

  onClearAdvancedFilters(): void {
    console.log('Advanced filters cleared');
    this.filteredAdvancedProducts = [...this.advancedProducts];
  }

  // Apply handlers
  onApplyFilters(filters: any): void {
    console.log('Filters applied:', filters);
    // Additional logic for apply button if needed
  }

  onApplyUserFilters(filters: any): void {
    console.log('User filters applied:', filters);
    // Additional logic for apply button if needed
  }

  onApplyAdvancedFilters(filters: any): void {
    console.log('Advanced filters applied:', filters);
    // Additional logic for apply button if needed
  }

  // Toggle handlers
  onToggleFilter(isOpen: boolean): void {
    console.log('Filter panel toggled:', isOpen);
  }
}