.filter-demo-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;

  .demo-section {
    margin-bottom: 60px;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    padding: 30px;
    background: #fafafa;

    h2 {
      margin: 0 0 20px 0;
      color: #111827;
      font-size: 24px;
      font-weight: 600;
      border-bottom: 2px solid #6366f1;
      padding-bottom: 10px;
    }

    .filter-section {
      margin-bottom: 30px;
      padding: 20px;
      background: white;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
    }

    .results-section {
      h3 {
        margin: 0 0 20px 0;
        color: #374151;
        font-size: 18px;
        font-weight: 500;
      }
    }
  }

  // ===== Products Grid =====
  .products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;

    .product-card {
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 20px;
      transition: all 0.2s ease;
      position: relative;

      &:hover {
        border-color: #6366f1;
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.1);
        transform: translateY(-2px);
      }

      h4 {
        margin: 0 0 8px 0;
        color: #111827;
        font-size: 16px;
        font-weight: 600;
      }

      p {
        margin: 4px 0;
        color: #6b7280;
        font-size: 14px;

        &.price {
          font-size: 18px;
          font-weight: 600;
          color: #059669;
          margin-top: 12px;
        }
      }

      .order-number {
        position: absolute;
        top: 8px;
        right: 8px;
        background: #6366f1;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 600;
      }
    }
  }

  // ===== Users Table =====
  .users-table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid #e5e7eb;

    table {
      width: 100%;
      border-collapse: collapse;

      thead {
        background: #f9fafb;

        th {
          text-align: left;
          padding: 16px 20px;
          font-weight: 600;
          color: #374151;
          font-size: 14px;
          border-bottom: 1px solid #e5e7eb;
        }
      }

      tbody {
        tr {
          transition: background-color 0.2s ease;

          &:hover {
            background: #f9fafb;
          }

          &:not(:last-child) {
            border-bottom: 1px solid #f3f4f6;
          }

          td {
            padding: 16px 20px;
            color: #374151;
            font-size: 14px;

            .status-badge {
              padding: 4px 12px;
              border-radius: 16px;
              font-size: 12px;
              font-weight: 500;
              text-transform: capitalize;

              &.status-active {
                background: #d1fae5;
                color: #065f46;
              }

              &.status-inactive {
                background: #fee2e2;
                color: #991b1b;
              }

              &.status-pending {
                background: #fef3c7;
                color: #92400e;
              }
            }
          }
        }
      }
    }
  }

  // ===== Advanced Products Grid =====
  .advanced-products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;

    .advanced-product-card {
      background: white;
      border: 1px solid #e5e7eb;
      border-radius: 12px;
      padding: 24px;
      transition: all 0.2s ease;

      &:hover {
        border-color: #6366f1;
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
        transform: translateY(-4px);
      }

      h4 {
        margin: 0 0 12px 0;
        color: #111827;
        font-size: 18px;
        font-weight: 600;
      }

      .tags {
        margin-bottom: 12px;

        .tag {
          display: inline-block;
          background: #ede9fe;
          color: #6d28d9;
          padding: 4px 12px;
          border-radius: 16px;
          font-size: 12px;
          font-weight: 500;
          margin-right: 8px;
          margin-bottom: 4px;
        }
      }

      .availability {
        color: #6b7280;
        font-size: 14px;
        margin: 8px 0;
      }

      .price {
        font-size: 20px;
        font-weight: 700;
        color: #059669;
        margin: 12px 0 0 0;
      }
    }
  }

  // ===== Size Variants Grid =====
  .size-variants-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;

    .size-variant {
      background: white;
      padding: 20px;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
      text-align: center;

      h4 {
        margin: 0 0 20px 0;
        color: #374151;
        font-size: 16px;
        font-weight: 600;
        padding-bottom: 10px;
        border-bottom: 1px solid #f3f4f6;
      }
    }
  }

  // ===== Configuration Examples =====
  .config-examples {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;

    .config-example {
      background: white;
      padding: 24px;
      border-radius: 8px;
      border: 1px solid #e5e7eb;
      text-align: center;
      transition: all 0.2s ease;

      &:hover {
        border-color: #d1d5db;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      h4 {
        margin: 0 0 16px 0;
        color: #374151;
        font-size: 14px;
        font-weight: 600;
        padding-bottom: 8px;
        border-bottom: 1px solid #f3f4f6;
      }
    }
  }

  // ===== Additional Utility Classes =====
  .filter-count-badge {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: #f0f9ff;
    color: #0369a1;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 16px;
    border: 1px solid #bae6fd;

    &::before {
      content: "🔍";
      font-size: 16px;
    }
  }

  .no-results {
    text-align: center;
    padding: 60px 20px;
    color: #6b7280;
    font-size: 16px;
    background: white;
    border-radius: 12px;
    border: 2px dashed #e5e7eb;

    .no-results-icon {
      font-size: 48px;
      margin-bottom: 16px;
      opacity: 0.5;
    }

    .no-results-text {
      margin-bottom: 8px;
      font-weight: 500;
      color: #374151;
    }

    .no-results-subtext {
      font-size: 14px;
      color: #9ca3af;
    }
  }

  .loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    background: white;
    border-radius: 12px;
    border: 1px solid #e5e7eb;

    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #f3f4f6;
      border-top: 3px solid #6366f1;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      margin-left: 16px;
      color: #6b7280;
      font-size: 14px;
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  // ===== Filter Animations =====
  .fade-in {
    animation: fadeIn 0.3s ease-in-out;
  }

  .slide-up {
    animation: slideUp 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  // ===== Dark Mode Support =====
  @media (prefers-color-scheme: dark) {
    .demo-section {
      background: #fff;
      border-color: #f8f6f6;

      h2 {
        color: #000;
        border-bottom-color: #8b5cf6;
      }

      .filter-section {
        background: #111827;
        border-color: #374151;
      }

      .results-section h3 {
        color: #d1d5db;
      }
    }

    .product-card,
    .advanced-product-card,
    .size-variant,
    .config-example {
      background: #111827;
      border-color: #374151;
      color: #f9fafb;

      &:hover {
        border-color: #8b5cf6;
        box-shadow: 0 4px 12px rgba(139, 92, 246, 0.1);
      }

      h4 {
        color: #f9fafb;
      }

      p {
        color: #d1d5db;

        &.price {
          color: #10b981;
        }
      }
    }

    .users-table {
      background: #111827;
      border-color: #374151;

      table {
        thead {
          background: #1f2937;

          th {
            color: #d1d5db;
            border-bottom-color: #374151;
          }
        }

        tbody {
          tr {
            &:hover {
              background: #1f2937;
            }

            &:not(:last-child) {
              border-bottom-color: #374151;
            }

            td {
              color: #d1d5db;
            }
          }
        }
      }
    }

    .no-results {
      background: #111827;
      border-color: #374151;
      color: #9ca3af;

      .no-results-text {
        color: #d1d5db;
      }

      .no-results-subtext {
        color: #6b7280;
      }
    }
  }

  // ===== Print Styles =====
  @media print {
    .filter-section {
      display: none;
    }

    .demo-section {
      page-break-inside: avoid;
      margin-bottom: 30px;
    }

    .product-card,
    .advanced-product-card {
      box-shadow: none;
      border: 1px solid #ccc;
    }
  }

  // ===== High Contrast Mode =====
  @media (prefers-contrast: high) {
    .demo-section,
    .product-card,
    .advanced-product-card,
    .users-table,
    .size-variant,
    .config-example {
      border-width: 2px;
      border-color: #000;
    }

    .filter-count-badge {
      border-width: 2px;
      border-color: #000;
    }

    .status-badge {
      border: 2px solid #000;
    }
  }

  // ===== Reduced Motion =====
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }

    .product-card,
    .advanced-product-card {
      &:hover {
        transform: none;
      }
    }

    .loading-spinner {
      animation: none;
      border-top-color: #6366f1;
    }
  }

  // ===== Responsive Design =====
  @media (max-width: 1024px) {
    padding: 16px;
    max-width: 100%;

    .demo-section {
      padding: 24px;
      margin-bottom: 48px;
    }

    .size-variants-grid {
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
    }
  }

  @media (max-width: 768px) {
    padding: 12px;

    .demo-section {
      padding: 20px;
      margin-bottom: 40px;

      h2 {
        font-size: 22px;
      }
    }

    .filter-section {
      padding: 16px;
      margin-bottom: 24px;
    }

    .products-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .advanced-products-grid {
      grid-template-columns: 1fr;
      gap: 20px;
    }

    .size-variants-grid {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .config-examples {
      grid-template-columns: 1fr;
      gap: 16px;
    }

    .users-table {
      overflow-x: auto;
      margin: 0 -20px;
      border-radius: 0;

      table {
        min-width: 600px;
      }
    }
  }

  @media (max-width: 480px) {
    padding: 8px;

    .demo-section {
      padding: 16px;
      margin-bottom: 32px;
      border-radius: 8px;

      h2 {
        font-size: 20px;
        margin-bottom: 16px;
      }
    }

    .filter-section {
      padding: 12px;
      margin-bottom: 20px;
    }

    .results-section h3 {
      font-size: 16px;
      margin-bottom: 16px;
    }

    .product-card,
    .advanced-product-card {
      padding: 16px;
    }

    .size-variant,
    .config-example {
      padding: 16px;
    }

    .users-table {
      margin: 0 -16px;
      
      table {
        thead th,
        tbody td {
          padding: 12px 16px;
          font-size: 13px;
        }
      }
    }

    .filter-count-badge {
      font-size: 12px;
      padding: 6px 12px;
    }
  }

  @media (max-width: 320px) {
    padding: 4px;

    .demo-section {
      padding: 12px;
      margin-bottom: 24px;
    }

    .product-card,
    .advanced-product-card,
    .size-variant,
    .config-example {
      padding: 12px;
    }

    .products-grid,
    .advanced-products-grid {
      gap: 12px;
    }
  }
}