.demo-page {
  min-height: 100vh;
  background-color: var(--color-background-primary);
}

.demo-header {
  background-color: var(--color-background-primary);
  border-bottom: 1px solid var(--color-border-subtle);
  padding: var(--global-spacing-8, 4rem) 0 var(--global-spacing-6, 3rem);

  h1 {
    font-size: var(--global-font-size-4xl, 2.5rem);
    font-weight: var(--global-font-weight-bold, 700);
    margin: 0 0 var(--global-spacing-2, 0.5rem) 0;
    color: var(--color-text-primary);
  }

  p {
    font-size: var(--global-font-size-lg, 1.125rem);
    color: var(--color-text-secondary);
    margin: 0;
    line-height: var(--global-line-height-relaxed, 1.625);
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--global-spacing-4, 1.5rem);
}

.demo-grid {
  display: grid;
  gap: var(--global-spacing-6, 3rem);
  margin-top: var(--global-spacing-6, 3rem);
  margin-bottom: var(--global-spacing-6, 3rem);
}

.demo-controls {
  display: flex;
  gap: var(--global-spacing-3, 0.75rem);
  margin-bottom: var(--global-spacing-4, 1.5rem);
  flex-wrap: wrap;
  align-items: center;
}

.table-demo {
  margin-top: var(--global-spacing-4, 1.5rem);
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--global-spacing-4, 1.5rem);
  margin-top: var(--global-spacing-4, 1.5rem);
}

.feature-item {
  padding: var(--global-spacing-4, 1.5rem);
  border: 1px solid var(--color-border-subtle);
  border-radius: var(--global-radius-md, 8px);
  background-color: var(--color-background-secondary);

  h4 {
    margin: 0 0 var(--global-spacing-2, 0.5rem) 0;
    font-size: var(--global-font-size-lg, 1.125rem);
    font-weight: var(--global-font-weight-semibold, 600);
    color: var(--color-text-primary);
  }

  p {
    margin: 0;
    font-size: var(--global-font-size-sm, 0.875rem);
    color: var(--color-text-secondary);
    line-height: var(--global-line-height-relaxed, 1.625);
  }
}

.config-grid {
  display: grid;
  gap: var(--global-spacing-3, 0.75rem);
  margin-top: var(--global-spacing-4, 1.5rem);
}

.config-item {
  padding: var(--global-spacing-3, 0.75rem);
  border-left: 3px solid var(--color-brand-primary);
  background-color: var(--color-background-secondary);
  border-radius: 0 var(--global-radius-sm, 4px) var(--global-radius-sm, 4px) 0;

  strong {
    color: var(--color-text-primary);
    font-weight: var(--global-font-weight-semibold, 600);
  }

  p {
    margin: var(--global-spacing-1, 0.25rem) 0 0 0;
    font-size: var(--global-font-size-sm, 0.875rem);
    color: var(--color-text-secondary);
  }
}

// Responsive design
@media (max-width: 768px) {
  .demo-header {
    padding: var(--global-spacing-6, 3rem) 0 var(--global-spacing-4, 1.5rem);

    h1 {
      font-size: var(--global-font-size-3xl, 2rem);
    }

    p {
      font-size: var(--global-font-size-base, 1rem);
    }
  }

  .demo-controls {
    flex-direction: column;
    align-items: stretch;

    ava-button {
      width: 100%;
    }
  }

  .feature-grid {
    grid-template-columns: 1fr;
  }

  .container {
    padding: 0 var(--global-spacing-3, 0.75rem);
  }
}

// Card styling overrides
::ng-deep {
  ava-card {
    .card-header {
      h3 {
        margin: 0 0 var(--global-spacing-1, 0.25rem) 0;
        font-size: var(--global-font-size-xl, 1.25rem);
        font-weight: var(--global-font-weight-semibold, 600);
        color: var(--color-text-primary);
      }

      p {
        margin: 0;
        font-size: var(--global-font-size-sm, 0.875rem);
        color: var(--color-text-secondary);
      }
    }
  }
}
