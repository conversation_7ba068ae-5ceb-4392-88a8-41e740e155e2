import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  CustomDataGridComponent,
  GLAccount,
  CustomDataGridConfig,
  ButtonComponent,
  CardComponent,
  CardHeaderComponent,
  CardContentComponent
} from '../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'app-custom-data-grid',
  standalone: true,
  imports: [
    CommonModule,
    CustomDataGridComponent,
    CardComponent,
    CardHeaderComponent,
    CardContentComponent
  ],
  templateUrl: './app-custom-data-grid.component.html',
  styleUrl: './app-custom-data-grid.component.scss'
})
export class AppCustomDataGridComponent {

  // Sample GL Account data matching the image
  glAccounts: GLAccount[] = [
    {
      id: '1',
      accountNumber: '5001 - 125',
      accountName: 'Rent Expense',
      debits: 400,
      credits: null
    },
    {
      id: '2',
      accountNumber: '1001 - 001',
      accountName: 'Cash',
      debits: null,
      credits: 600
    },
    {
      id: '3',
      accountNumber: '1001 - 001',
      accountName: 'Cash',
      debits: 100,
      credits: null
    }
  ];

  // Configuration for the table
  tableConfig: CustomDataGridConfig = {
    showDeleteAction: true,
    isLoading: false,
    emptyMessage: 'No GL accounts found',
    currencySymbol: '$',
    deleteIconName: 'trash-2'
  };

  // Loading state for demo
  isLoading = false;

  onDeleteAccount(account: GLAccount): void {
    console.log('Delete account:', account);
    // Remove the account from the array
    this.glAccounts = this.glAccounts.filter(acc => acc.id !== account.id);
  }

  onAccountClick(account: GLAccount): void {
    console.log('Account clicked:', account);
    // Handle account click - could navigate to detail view
  }

  // Demo methods
  addSampleAccount(): void {
    const newAccount: GLAccount = {
      id: (this.glAccounts.length + 1).toString(),
      accountNumber: `${2000 + this.glAccounts.length} - ${100 + this.glAccounts.length}`,
      accountName: 'Sample Account',
      debits: Math.random() > 0.5 ? Math.floor(Math.random() * 1000) : null,
      credits: Math.random() > 0.5 ? Math.floor(Math.random() * 1000) : null
    };
    this.glAccounts = [...this.glAccounts, newAccount];
  }

  clearAllAccounts(): void {
    this.glAccounts = [];
  }

  toggleLoading(): void {
    this.isLoading = !this.isLoading;
    this.tableConfig = {
      ...this.tableConfig,
      isLoading: this.isLoading
    };
  }

  changeDeleteIcon(): void {
    const icons = ['trash-2', 'x', 'minus-circle', 'delete'];
    const currentIndex = icons.indexOf(this.tableConfig.deleteIconName || 'trash-2');
    const nextIndex = (currentIndex + 1) % icons.length;
    this.tableConfig = {
      ...this.tableConfig,
      deleteIconName: icons[nextIndex]
    };
  }

  resetToSampleData(): void {
    this.glAccounts = [
      {
        id: '1',
        accountNumber: '5001 - 125',
        accountName: 'Rent Expense',
        debits: 400,
        credits: null
      },
      {
        id: '2',
        accountNumber: '1001 - 001',
        accountName: 'Cash',
        debits: null,
        credits: 600
      },
      {
        id: '3',
        accountNumber: '1001 - 001',
        accountName: 'Cash',
        debits: 100,
        credits: null
      }
    ];
  }
}
