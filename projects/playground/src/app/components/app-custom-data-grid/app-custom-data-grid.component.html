<div class="demo-page">
  <!-- Header Section -->
  <div class="demo-header">
    <div class="container">
      <h1>Custom Data Grid</h1>
      <p>A composite component for displaying General Ledger account information with debits, credits, and actions using icons instead of buttons.</p>
    </div>
  </div>

  <!-- Demo Content -->
  <div class="container">
    <div class="demo-grid">
      
      <!-- Basic Usage -->
      <ava-card>
        <div header>
          <ava-card-header>
            <h3>Basic Usage</h3>
            <p>Display GL accounts with sortable columns and delete icons</p>
          </ava-card-header>
        </div>
        
        <div content>
           

            <!-- Custom Data Grid -->
            <div class="table-demo">
              <ava-custom-data-grid
                [accounts]="glAccounts"
                [config]="tableConfig"
                (deleteAccount)="onDeleteAccount($event)"
                (accountClick)="onAccountClick($event)">
              </ava-custom-data-grid>
            </div>
        </div>
      </ava-card>

      <!-- Features -->
      <ava-card>
        <div header>
          <ava-card-header>
            <h3>Features</h3>
            <p>Key capabilities of the Custom Data Grid component</p>
          </ava-card-header>
        </div>
        
        <div content>
          <ava-card-content>
            <div class="feature-grid">
              <div class="feature-item">
                <h4>📊 Data Display</h4>
                <p>Shows GL account number, name, debits, and credits with proper formatting</p>
              </div>
              
              <div class="feature-item">
                <h4>🗂️ Sortable Columns</h4>
                <p>All columns are sortable for easy data organization</p>
              </div>
              
              <div class="feature-item">
                <h4>🗑️ Delete Icons</h4>
                <p>Individual delete icons for each account row (configurable icon name)</p>
              </div>
              
              <div class="feature-item">
                <h4>💰 Currency Formatting</h4>
                <p>Automatic currency formatting with dash for zero/null values</p>
              </div>
              
              <div class="feature-item">
                <h4>🎯 Click Events</h4>
                <p>Account number and name are clickable for navigation</p>
              </div>
              
              <div class="feature-item">
                <h4>📱 Responsive</h4>
                <p>Adapts to different screen sizes with mobile-friendly design</p>
              </div>
            </div>
          </ava-card-content>
        </div>
      </ava-card>

      <!-- Configuration -->
      <ava-card>
        <div header>
          <ava-card-header>
            <h3>Configuration Options</h3>
            <p>Customize the table behavior and appearance</p>
          </ava-card-header>
        </div>
        
        <div content>
          <ava-card-content>
            <div class="config-grid">
              <div class="config-item">
                <strong>showDeleteAction:</strong> boolean
                <p>Show or hide delete action icons</p>
              </div>
              
              <div class="config-item">
                <strong>isLoading:</strong> boolean
                <p>Display loading state with spinner</p>
              </div>
              
              <div class="config-item">
                <strong>emptyMessage:</strong> string
                <p>Custom message when no data is available</p>
              </div>
              
              <div class="config-item">
                <strong>currencySymbol:</strong> string
                <p>Currency symbol for amount formatting (default: $)</p>
              </div>
              
              <div class="config-item">
                <strong>deleteIconName:</strong> string
                <p>Icon name for delete action (default: trash-2)</p>
              </div>
            </div>
          </ava-card-content>
        </div>
      </ava-card>

    </div>
  </div>
</div>
