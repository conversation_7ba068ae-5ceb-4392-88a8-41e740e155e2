.flip-card-demo {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;

  .demo-section {
    margin-bottom: 4rem;
    display: flex;
    justify-content: center;

    .demo-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 2rem;
      justify-items: center;
      align-items: center;
      max-width: 1000px;
      margin: 0 auto;

      @media (max-width: 768px) {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }
    }

    .demo-item {
      text-align: center;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
