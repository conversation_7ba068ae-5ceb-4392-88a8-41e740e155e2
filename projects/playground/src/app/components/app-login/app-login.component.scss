.login-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;

  .demo-header {
    text-align: center;
    margin-bottom: 32px;

    h2 {
      font-size: 2.5rem;
      font-weight: 700;
      color: #212529;
      margin: 0 0 16px 0;
    }

    p {
      font-size: 1.125rem;
      color: #6c757d;
      margin: 0;
      line-height: 1.6;
    }
  }

  .controls-card {
    margin-bottom: 32px;

    .controls-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 24px;
    }

    .control-group {
      h4 {
        font-size: 1.125rem;
        font-weight: 600;
        color: #212529;
        margin: 0 0 16px 0;
        padding-bottom: 8px;
        border-bottom: 2px solid #e9ecef;
      }

      .button-group {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }

      .toggle-group {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
        gap: 8px;
      }

      .providers-group {
        .providers-list {
          margin-top: 12px;
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .provider-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 8px 12px;
          background: #f8f9fa;
          border-radius: 6px;
          border: 1px solid #e9ecef;

          span {
            flex: 1;
            font-size: 0.875rem;
            color: #495057;
          }
        }
      }

      .validation-group {
        .validation-item {
          label {
            display: block;
            font-size: 0.875rem;
            color: #495057;
            margin-bottom: 8px;
          }

          input[type="range"] {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #e9ecef;
            outline: none;
            -webkit-appearance: none;

            &::-webkit-slider-thumb {
              -webkit-appearance: none;
              appearance: none;
              width: 18px;
              height: 18px;
              border-radius: 50%;
              background: #007bff;
              cursor: pointer;
            }

            &::-moz-range-thumb {
              width: 18px;
              height: 18px;
              border-radius: 50%;
              background: #007bff;
              cursor: pointer;
              border: none;
            }
          }
        }
      }
    }
  }

  .demo-section {
    margin-bottom: 32px;

    .demo-content {
      min-height: 600px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: #f8f9fa;
      border-radius: 8px;
      padding: 20px;
    }
  }

  .event-log-card {
    margin-bottom: 32px;

    .event-content {
      background: #f8f9fa;
      border-radius: 6px;
      padding: 16px;
      max-height: 300px;
      overflow-y: auto;

      pre {
        margin: 0;
        font-size: 0.875rem;
        color: #495057;
        white-space: pre-wrap;
        word-break: break-word;
      }
    }
  }

  .features-card {
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
    }

    .feature-item {
      display: flex;
      align-items: flex-start;
      gap: 12px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;
      transition: all 0.3s ease;

      &:hover {
        background: #e9ecef;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .feature-content {
        flex: 1;

        strong {
          display: block;
          font-size: 1rem;
          font-weight: 600;
          color: #212529;
          margin-bottom: 4px;
        }

        font-size: 0.875rem;
        color: #6c757d;
        line-height: 1.5;
      }
    }
  }

  // Responsive Design
  @media (max-width: 768px) {
    padding: 16px;

    .demo-header {
      h2 {
        font-size: 2rem;
      }

      p {
        font-size: 1rem;
      }
    }

    .controls-card {
      .controls-grid {
        grid-template-columns: 1fr;
        gap: 20px;
      }

      .control-group {
        .toggle-group {
          grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        }
      }
    }

    .features-card {
      .features-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }
    }
  }

  @media (max-width: 480px) {
    padding: 12px;

    .demo-header {
      h2 {
        font-size: 1.75rem;
      }
    }

    .controls-card {
      .control-group {
        .button-group {
          flex-direction: column;
        }

        .toggle-group {
          grid-template-columns: 1fr;
        }
      }
    }

    .feature-item {
      flex-direction: column;
      text-align: center;

      .feature-content {
        text-align: left;
      }
    }
  }

  // Animation for controls
  .control-group {
    animation: fadeInUp 0.6s ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  // Stagger animation for feature items
  .feature-item {
    animation: fadeInUp 0.6s ease-out;
    animation-fill-mode: both;

    @for $i from 1 through 10 {
      &:nth-child(#{$i}) {
        animation-delay: #{$i * 0.1}s;
      }
    }
  }

  // Custom scrollbar for event log
  .event-content {
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
  }

  // Focus states for accessibility
  .control-group button:focus,
  .validation-group input:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
  }

  // Loading state for demo section
  .demo-section.loading {
    .demo-content {
      position: relative;

      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 8px;
        z-index: 1;
      }
    }
  }
}
