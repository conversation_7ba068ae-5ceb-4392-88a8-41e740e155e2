<div class="login-demo">
  <div class="demo-header">
    <h2>Login Component</h2>
    <p>
      A comprehensive login component with customizable themes, layouts, social
      login options, and form validation.
    </p>
  </div>

  <!-- Configuration Controls -->
  <div class="controls-card">
    <ava-card>
      <div header>
        <h3>Configuration Controls</h3>
      </div>
      <div content>
        <div class="controls-grid">
          <!-- Theme Controls -->
          <div class="control-group">
            <h4>Theme</h4>
            <div class="button-group">
              <ava-button
                *ngFor="let theme of themes"
                (click)="changeTheme(theme.value)"
                [variant]="
                  config.theme === theme.value ? 'primary' : 'secondary'
                "
                size="small"
                [label]="theme.label"
              >
              </ava-button>
            </div>
          </div>

          <!-- Layout Controls -->
          <div class="control-group">
            <h4>Layout</h4>
            <div class="button-group">
              <ava-button
                *ngFor="let layout of layouts"
                (click)="changeLayout(layout.value)"
                [variant]="
                  config.layout === layout.value ? 'primary' : 'secondary'
                "
                size="small"
                [label]="layout.label"
              >
              </ava-button>
            </div>
          </div>

          <!-- Section Toggles -->
          <div class="control-group">
            <h4>Display Sections</h4>
            <div class="toggle-group">
              <ava-button
                (click)="toggleSection('showLogo')"
                [variant]="config.showLogo ? 'success' : 'secondary'"
                size="small"
                [label]="config.showLogo ? 'Logo: On' : 'Logo: Off'"
                [iconName]="config.showLogo ? 'check' : 'x'"
                [iconSize]="12"
              >
              </ava-button>
              <ava-button
                (click)="toggleSection('showTitle')"
                [variant]="config.showTitle ? 'success' : 'secondary'"
                size="small"
                [label]="config.showTitle ? 'Title: On' : 'Title: Off'"
                [iconName]="config.showTitle ? 'check' : 'x'"
                [iconSize]="12"
              >
              </ava-button>
              <ava-button
                (click)="toggleSection('showSubtitle')"
                [variant]="config.showSubtitle ? 'success' : 'secondary'"
                size="small"
                [label]="config.showSubtitle ? 'Subtitle: On' : 'Subtitle: Off'"
                [iconName]="config.showSubtitle ? 'check' : 'x'"
                [iconSize]="12"
              >
              </ava-button>
              <ava-button
                (click)="toggleSection('showRememberMe')"
                [variant]="config.showRememberMe ? 'success' : 'secondary'"
                size="small"
                [label]="
                  config.showRememberMe ? 'Remember Me: On' : 'Remember Me: Off'
                "
                [iconName]="config.showRememberMe ? 'check' : 'x'"
                [iconSize]="12"
              >
              </ava-button>
              <ava-button
                (click)="toggleSection('showForgotPassword')"
                [variant]="config.showForgotPassword ? 'success' : 'secondary'"
                size="small"
                [label]="
                  config.showForgotPassword
                    ? 'Forgot Password: On'
                    : 'Forgot Password: Off'
                "
                [iconName]="config.showForgotPassword ? 'check' : 'x'"
                [iconSize]="12"
              >
              </ava-button>
              <ava-button
                (click)="toggleSection('showSignUp')"
                [variant]="config.showSignUp ? 'success' : 'secondary'"
                size="small"
                [label]="config.showSignUp ? 'Sign Up: On' : 'Sign Up: Off'"
                [iconName]="config.showSignUp ? 'check' : 'x'"
                [iconSize]="12"
              >
              </ava-button>
              <ava-button
                (click)="toggleSection('showSocialLogin')"
                [variant]="config.showSocialLogin ? 'success' : 'secondary'"
                size="small"
                [label]="
                  config.showSocialLogin
                    ? 'Social Login: On'
                    : 'Social Login: Off'
                "
                [iconName]="config.showSocialLogin ? 'check' : 'x'"
                [iconSize]="12"
              >
              </ava-button>
              <ava-button
                (click)="toggleSection('showDivider')"
                [variant]="config.showDivider ? 'success' : 'secondary'"
                size="small"
                [label]="config.showDivider ? 'Divider: On' : 'Divider: Off'"
                [iconName]="config.showDivider ? 'check' : 'x'"
                [iconSize]="12"
              >
              </ava-button>
            </div>
          </div>

          <!-- State Controls -->
          <div class="control-group">
            <h4>Component States</h4>
            <div class="button-group">
              <ava-button
                (click)="toggleLoading()"
                [variant]="loading ? 'warning' : 'primary'"
                size="small"
                [label]="loading ? 'Stop Loading' : 'Start Loading'"
                [iconName]="loading ? 'pause' : 'play'"
                [iconSize]="14"
              >
              </ava-button>
              <ava-button
                (click)="toggleDisabled()"
                [variant]="disabled ? 'success' : 'secondary'"
                size="small"
                [label]="disabled ? 'Enable' : 'Disable'"
                [iconName]="disabled ? 'unlock' : 'lock'"
                [iconSize]="14"
              >
              </ava-button>
              <ava-button
                (click)="setError()"
                variant="danger"
                size="small"
                label="Set Error"
                iconName="alert-triangle"
                [iconSize]="14"
              >
              </ava-button>
              <ava-button
                (click)="clearError()"
                variant="info"
                size="small"
                label="Clear Error"
                iconName="x-circle"
                [iconSize]="14"
              >
              </ava-button>
            </div>
          </div>

          <!-- Social Providers -->
          <div class="control-group">
            <h4>Social Providers</h4>
            <div class="providers-group">
              <ava-button
                (click)="addSocialProvider()"
                variant="success"
                size="small"
                label="Add Provider"
                iconName="plus"
                [iconSize]="14"
              >
              </ava-button>
              <div class="providers-list">
                <div
                  *ngFor="let provider of config.socialProviders"
                  class="provider-item"
                >
                  <ava-icon
                    [iconName]="provider.icon"
                    [iconSize]="16"
                    [style.color]="provider.color"
                  ></ava-icon>
                  <span>{{ provider.name }}</span>
                  <ava-button
                    (click)="removeSocialProvider(provider.id)"
                    variant="danger"
                    size="small"
                    label="Remove"
                    iconName="trash-2"
                    [iconSize]="12"
                  >
                  </ava-button>
                </div>
              </div>
            </div>
          </div>

          <!-- Validation Rules -->
          <div class="control-group">
            <h4>Validation Rules</h4>
            <div class="validation-group">
              <div class="validation-item">
                <label for="minPasswordLength"
                  >Min Password Length:
                  {{ config.validationRules?.minPasswordLength }}</label
                >
                <input
                  id="minPasswordLength"
                  type="range"
                  [min]="4"
                  [max]="16"
                  [value]="config.validationRules?.minPasswordLength || 8"
                  (input)="changeMinPasswordLength($event)"
                />
              </div>
            </div>
          </div>

          <!-- Demo Actions -->
          <div class="control-group">
            <h4>Demo Actions</h4>
            <div class="button-group">
              <ava-button
                (click)="fillDemoCredentials()"
                variant="info"
                size="small"
                label="Show Demo Credentials"
                iconName="info"
                [iconSize]="14"
              >
              </ava-button>
            </div>
          </div>
        </div>
      </div>
    </ava-card>
  </div>

  <!-- Login Component Demo -->
  <div class="demo-section">
    <ava-card>
      <div header>
        <h3>Interactive Login Form</h3>
        <p>Try different configurations and test the login functionality</p>
      </div>
      <div content>
        <ava-login
          [config]="config"
          [loading]="loading"
          [disabled]="disabled"
          [errorMessage]="errorMessage"
          (login)="onLogin($event)"
          (socialLogin)="onSocialLogin($event)"
          (forgotPassword)="onForgotPassword($event)"
          (signUp)="onSignUp()"
          (loginEvent)="onLoginEvent($event)"
        >
        </ava-login>
      </div>
    </ava-card>
  </div>

  <!-- Event Log -->
  <div class="event-log-card" *ngIf="lastEvent">
    <ava-card>
      <div header>
        <h3>Event Log</h3>
        <p>Real-time events from the login component</p>
      </div>
      <div content>
        <div class="event-content">
          <pre>{{ lastEvent | json }}</pre>
        </div>
      </div>
    </ava-card>
  </div>

  <!-- Features Card -->
  <div class="features-card">
    <ava-card>
      <div header>
        <h3>Features Demonstrated</h3>
        <p>Comprehensive list of capabilities</p>
      </div>
      <div content>
        <div class="features-grid">
          <div class="feature-item">
            <ava-icon
              iconName="user"
              [iconSize]="20"
              iconColor="#007bff"
            ></ava-icon>
            <div class="feature-content">
              <strong>User Authentication:</strong> Email and password login
              with validation
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="shield"
              [iconSize]="20"
              iconColor="#28a745"
            ></ava-icon>
            <div class="feature-content">
              <strong>Form Validation:</strong> Real-time validation with custom
              error messages
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="eye"
              [iconSize]="20"
              iconColor="#ffc107"
            ></ava-icon>
            <div class="feature-content">
              <strong>Password Toggle:</strong> Show/hide password functionality
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="share-2"
              [iconSize]="20"
              iconColor="#17a2b8"
            ></ava-icon>
            <div class="feature-content">
              <strong>Social Login:</strong> Multiple social provider
              integration
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="palette"
              [iconSize]="20"
              iconColor="#6f42c1"
            ></ava-icon>
            <div class="feature-content">
              <strong>Multiple Themes:</strong> Default, minimal, modern, and
              professional themes
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="layout"
              [iconSize]="20"
              iconColor="#fd7e14"
            ></ava-icon>
            <div class="feature-content">
              <strong>Layout Options:</strong> Centered, left-aligned, and split
              layouts
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="settings"
              [iconSize]="20"
              iconColor="#e83e8c"
            ></ava-icon>
            <div class="feature-content">
              <strong>Customizable:</strong> Configurable sections, labels, and
              validation rules
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="smartphone"
              [iconSize]="20"
              iconColor="#6c757d"
            ></ava-icon>
            <div class="feature-content">
              <strong>Responsive Design:</strong> Adapts to different screen
              sizes and devices
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="zap"
              [iconSize]="20"
              iconColor="#dc3545"
            ></ava-icon>
            <div class="feature-content">
              <strong>Event Handling:</strong> Comprehensive event system for
              all interactions
            </div>
          </div>
          <div class="feature-item">
            <ava-icon
              iconName="check-circle"
              [iconSize]="20"
              iconColor="#20c997"
            ></ava-icon>
            <div class="feature-content">
              <strong>Accessibility:</strong> ARIA labels, keyboard navigation,
              and screen reader support
            </div>
          </div>
        </div>
      </div>
    </ava-card>
  </div>
</div>
