import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-states-demo',
  standalone: true,
  imports: [CommonModule, ButtonComponent],
  template: `
    <div class="demo-section center-demo">
      <div class="button-group">
        <ava-button
          label="Default State"
          variant="primary"
          size="medium"
          [pill]="true"
        ></ava-button>
        <ava-button
          label="Processing State"
          variant="primary"
          [processing]="true"
          size="medium"
          [pill]="true"
          iconName="loader"
          iconColor="white"
          iconPosition="left"
        ></ava-button>
        <ava-button
          label="Disabled State"
          variant="primary"
          [disabled]="true"
          size="medium"
          [pill]="true"
        ></ava-button>
      </div>
    </div>
  `,
  styles: [
    `
      .center-demo {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 60vh;
      }
      .demo-section {
        margin-bottom: 2rem;
        padding: 2rem;
        max-width: 870px;
        margin-top: 0;
      }
      .description {
        color: #666;
        margin-bottom: 1rem;
      }
      .button-group {
        display: flex;
        gap: 1rem; // Reduced from 1.5rem since buttons now have 4px margins
        flex-wrap: wrap;
        justify-content: center;
      }
    `,
  ],
})
export class StatesDemoComponent {}
