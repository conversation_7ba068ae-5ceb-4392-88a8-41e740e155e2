import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ButtonComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-sizes-demo',
  standalone: true,
  imports: [CommonModule, ButtonComponent],
  template: `
    <div class="demo-section center-demo">
      <div class="button-group">
        <ava-button label="XSmall" variant="primary" size="xsmall"></ava-button>
        <ava-button label="Small" variant="primary" size="small"></ava-button>
        <ava-button label="Medium" variant="primary" size="medium"></ava-button>
        <ava-button label="Large" variant="primary" size="large"></ava-button>
        <ava-button label="XLarge" variant="primary" size="xlarge"></ava-button>
      </div>
    </div>
  `,
  styles: [
    `
      .center-demo {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 60vh;
      }
      .demo-section {
        margin-bottom: 2rem;
        padding: 2rem;
        max-width: 870px;
        margin-top: 0;
      }
      .description {
        color: #666;
        margin-bottom: 1rem;
      }
      .button-group {
        display: flex;
        gap: 1rem; // Reduced from 1.5rem since buttons now have 4px margins
        flex-wrap: wrap;
        justify-content: center;
        align-items: center;
      }
    `,
  ],
})
export class SizesDemoComponent {}
