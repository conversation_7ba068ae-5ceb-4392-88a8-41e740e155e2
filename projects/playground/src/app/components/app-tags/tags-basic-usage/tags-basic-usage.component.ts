import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AvaTagComponent } from '../../../../../../play-comp-library/src/lib/components/tags/tags.component';

interface BasicExample {
  title: string;
  description: string;
  tags: {
    label: string;
    color?:
    | 'default'
    | 'primary'
    | 'success'
    | 'warning'
    | 'error'
    | 'info'
    | 'custom';
    variant?: 'filled' | 'outlined';
    size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
    icon?: string;
    iconPosition?: 'start' | 'end';
    iconColor?: string;
    customStyle?: Record<string, string>;
    pill?: boolean;
  }[];
}

@Component({
  selector: 'ava-tags-basic-usage',
  standalone: true,
  imports: [CommonModule, AvaTagComponent],
  templateUrl: './tags-basic-usage.component.html',
  styleUrls: ['./tags-basic-usage.component.scss'],
})
export class TagsBasicUsageComponent {
  basicExamples: BasicExample[] = [
    {
      title: 'Simple Tags',
      description: 'Basic tag implementations with labels and default styling.',
      tags: [
        { label: 'Default Tag' },
        { label: 'Primary Tag', color: 'primary' },
        { label: 'Success Tag', color: 'success', pill: true },
        { label: 'Info Tag', color: 'info', pill: true, variant: 'outlined' },
      ],
    },
  ];

  onTagClick(label: string) {
    console.log(`Tag clicked: ${label}`);
    alert(`Demo: Tag "${label}" was clicked!`);
  }

  onTagRemove(label: string) {
    console.log(`Tag removed: ${label}`);
    alert(`Demo: Tag "${label}" was removed!`);
  }

  getBasicGuidelines() {
    return [
      'Use semantic colors for consistent meaning across your application',
      'Choose appropriate sizes based on interface hierarchy and density',
      'Prefer outlined variants for secondary information',
      'Use icons to enhance visual recognition and meaning',
      'Ensure sufficient color contrast for accessibility',
    ];
  }
}
