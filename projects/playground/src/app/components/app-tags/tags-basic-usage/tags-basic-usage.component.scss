.demo-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.demo-section {
  margin-bottom: 3rem;

  h3 {
    color: #1f2937;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  p {
    color: #6b7280;
    margin-bottom: 1.5rem;
    line-height: 1.6;
  }
}

.basic-examples {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.example-item {
  padding: 1.5rem;

  h4 {
    color: #374151;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  p {
    color: #6b7280;
    margin-bottom: 1rem;
    font-size: 0.875rem;
  }
}

.tag-demo {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  align-items: center;
  justify-content: center;
  min-height: 2.5rem;
  padding: 0.5rem;
  border-radius: 6px;
}

.use-cases {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.use-case {
  background: #ffffff;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  h4 {
    color: #374151;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }

  p {
    color: #6b7280;
    margin-bottom: 1rem;
    font-size: 0.875rem;
  }
}

.tag-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  align-items: center;
  justify-content: center;
  min-height: 2rem;
  padding: 0.5rem;
  border-radius: 6px;
}

.implementation-notes {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.note {
  background: #ffffff;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  h4 {
    color: #374151;
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  li {
    color: #6b7280;
    padding: 0.25rem 0;
    position: relative;
    padding-left: 1.5rem;

    &::before {
      content: "•";
      color: #3b82f6;
      font-weight: bold;
      position: absolute;
      left: 0;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .demo-container {
    padding: 1rem;
  }

  .use-cases {
    grid-template-columns: 1fr;
  }

  .implementation-notes {
    grid-template-columns: 1fr;
  }

  .tag-demo,
  .tag-group {
    gap: 0.5rem;
  }
}

// Accessibility improvements
ava-tag {
  &:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
  }

  &:hover {
    transform: translateY(-1px);
    transition: transform 0.2s ease;
  }
}

// Animation for demo interactions
.tag-demo ava-tag,
.tag-group ava-tag {
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }
}
