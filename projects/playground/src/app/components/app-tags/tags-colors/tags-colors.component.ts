import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AvaTagComponent } from '../../../../../../play-comp-library/src/lib/components/tags/tags.component';

@Component({
  selector: 'ava-tags-colors',
  standalone: true,
  imports: [CommonModule, AvaTagComponent],
  templateUrl: './tags-colors.component.html',
  styleUrls: ['./tags-colors.component.scss'],
})
export class TagsColorsComponent {
  onTagClick(tagName: string): void {
    console.log(`${tagName} tag clicked!`);
  }
}
