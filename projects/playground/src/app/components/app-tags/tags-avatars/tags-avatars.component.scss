.demo-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-section {
  display: flex;
  justify-content: center;
  min-height: 60vh;

}

.avatar-examples {
  .avatar-group {
    margin-bottom: 2rem;
    padding: 1.5rem;


    h4 {
      color: #333;
      margin-bottom: 0.5rem;
      font-size: 1.2rem;
      font-weight: 500;
    }

    p {
      color: #666;
      margin-bottom: 1rem;
      font-size: 0.9rem;
    }

    .tag-examples {
      display: flex;
      flex-wrap: wrap;
      gap: 0.75rem;
      align-items: center;
    }
  }
}

.use-case-examples {
  .use-case-group {
    margin-bottom: 2rem;
    padding: 1.5rem;


    h4 {
      color: #333;
      margin-bottom: 0.5rem;
      font-size: 1.2rem;
      font-weight: 500;
    }

    p {
      color: #666;
      margin-bottom: 1rem;
      font-size: 0.9rem;
    }

    .tag-examples {
      display: flex;
      flex-wrap: wrap;
      gap: 0.75rem;
      align-items: center;
    }
  }
}

.guidelines-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;

  .guideline-card {
    padding: 1.5rem;


    h4 {
      color: #333;
      margin-bottom: 1rem;
      font-size: 1.1rem;
      font-weight: 500;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        color: #666;
        margin-bottom: 0.5rem;
        padding-left: 1.5rem;
        position: relative;
        line-height: 1.5;

        &::before {
          content: "•";
          color: #007bff;
          font-weight: bold;
          position: absolute;
          left: 0;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .demo-container {
    padding: 1rem;
  }

  .guidelines-grid {
    grid-template-columns: 1fr;
  }

  .tag-examples {
    gap: 0.5rem;
  }
}
