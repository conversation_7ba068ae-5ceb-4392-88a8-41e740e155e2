import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AvaTagComponent } from '../../../../../../play-comp-library/src/lib/components/tags/tags.component';

interface Tag {
  id: number;
  label: string;
  color: 'default' | 'primary' | 'success' | 'warning' | 'error' | 'info';
}

@Component({
  selector: 'ava-tags-interactive',
  standalone: true,
  imports: [CommonModule, AvaTagComponent],
  templateUrl: './tags-interactive.component.html',
  styleUrls: ['./tags-interactive.component.scss'],
})
export class TagsInteractiveComponent {
  tags: Tag[] = [
    { id: 1, label: 'JavaScript', color: 'primary' },
    { id: 2, label: 'Angular', color: 'error' },
    { id: 3, label: 'TypeScript', color: 'info' },
    { id: 4, label: 'CSS', color: 'success' },
    { id: 5, label: 'HTML', color: 'warning' },
  ];

  onTagClick(tagName: string): void {
    console.log(`${tagName} tag clicked!`);
  }

  onTagRemove(tagToRemove: Tag): void {
    this.tags = this.tags.filter((tag) => tag.id !== tagToRemove.id);
    console.log(`${tagToRemove.label} tag removed!`);
  }

  trackByTag(index: number, tag: Tag): number {
    return tag.id;
  }
}
