import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AvaTagComponent } from '../../../../../../play-comp-library/src/lib/components/tags/tags.component';

@Component({
  selector: 'ava-tags-variants',
  standalone: true,
  imports: [CommonModule, AvaTagComponent],
  templateUrl: './tags-variants.component.html',
  styleUrls: ['./tags-variants.component.scss'],
})
export class TagsVariantsComponent {
  onTagClick(tagName: string): void {
    console.log(`${tagName} tag clicked!`);
  }
}
