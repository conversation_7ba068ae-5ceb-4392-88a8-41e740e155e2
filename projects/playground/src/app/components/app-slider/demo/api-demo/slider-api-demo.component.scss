.center-demo {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 1rem;
}

.demo-section {
  max-width: 870px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.demo-item {
  padding: 1rem;
  border-radius: 8px;

  h4 {
    margin-bottom: 20px;
    color: #555;
    font-size: 18px;
  }

  .property-grid {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .property-item {
      display: flex;
      flex-direction: column;
      gap: 10px;

      label {
        font-weight: 500;
        color: #555;
      }

      small {
        color: #888;
        font-style: italic;
      }
    }
  }

  .api-table {
    overflow-x: auto;

    table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;

      th,
      td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #e0e0e0;
      }

      th {
        background-color: #f5f5f5;
        font-weight: 600;
        color: #333;
      }

      td {
        color: #666;

        &:first-child {
          font-weight: 500;
          color: #555;
        }
      }

      tr:hover {
        background-color: #f9f9f9;
      }
    }
  }
}
