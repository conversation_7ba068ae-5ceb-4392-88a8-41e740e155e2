.center-demo {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
}

.demo-section {
  max-width: 800px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;

  h3 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
    font-size: 24px;
  }

  .demo-description {
    text-align: center;
    margin-bottom: 30px;
    color: #666;
    font-size: 16px;
    line-height: 1.5;
  }
}

.demo-item {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;

  h4 {
    margin-bottom: 20px;
    color: #555;
    font-size: 18px;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 10px;
  }

  h5 {
    margin-bottom: 10px;
    color: #444;
    font-size: 16px;
    font-weight: 600;
  }

  p {
    margin-bottom: 15px;
    color: #666;
    font-weight: 500;
  }
}

.variant-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;

  .variant-item {
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background-color: white;

    .variant-info {
      margin-top: 15px;
      display: flex;
      align-items: center;
      gap: 15px;

      .variant-badge {
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        &.default {
          background-color: #e8f5e8;
          color: #2e7d32;
          border: 1px solid #c8e6c9;
        }

        &.input {
          background-color: #fff3e0;
          color: #f57c00;
          border: 1px solid #ffcc02;
        }
      }

      .variant-description {
        color: #666;
        font-size: 14px;
        font-style: italic;
      }
    }
  }
}

.size-examples {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;

  .size-item {
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background-color: white;

    .size-info {
      margin-top: 15px;
      display: flex;
      align-items: center;
      gap: 15px;

      .size-badge {
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        &.small {
          background-color: #e3f2fd;
          color: #1976d2;
          border: 1px solid #bbdefb;
        }

        &.medium {
          background-color: #f3e5f5;
          color: #7b1fa2;
          border: 1px solid #e1bee7;
        }
      }

      .size-description {
        color: #666;
        font-size: 14px;
        font-style: italic;
      }
    }
  }
}

.range-examples {
  display: grid;
  grid-template-columns: 1fr;
  gap: 25px;

  .range-item {
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background-color: white;

    .range-info {
      margin-top: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .range-label,
      .range-step {
        color: #666;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}

.precision-examples {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;

  .precision-item {
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background-color: white;

    .precision-info {
      margin-top: 15px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .precision-label,
      .precision-step {
        color: #666;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}

.features-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;

  .feature-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 15px;
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 6px;

    .feature-icon {
      color: #4caf50;
      font-size: 18px;
      font-weight: bold;
    }

    .feature-text {
      color: #555;
      font-size: 14px;
      font-weight: 500;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .variant-comparison,
  .size-examples,
  .precision-examples {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .features-list {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .demo-section {
    max-width: 100%;
    padding: 0 10px;
  }
}
