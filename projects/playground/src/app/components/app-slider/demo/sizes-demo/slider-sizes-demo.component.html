<div class="center-demo">
  <div class="demo-section">
    <h3>Slider Sizes Demo</h3>
    <p class="demo-description">
      This demo showcases the different size variants available for the slider component.
    </p>

    <div class="demo-item">
      <h4>Size Comparison - Default Type</h4>
      <div class="size-comparison">
        <div class="size-item">
          <span class="size-label">Small Size</span>
          <ava-slider 
            [value]="50" 
            [min]="0" 
            [max]="100"
            [step]="1"
            size="small"
            type="default"
            [showTooltip]="true"
          > </ava-slider>
        </div>
        <div class="size-item">
          <span class="size-label">Medium Size</span>
          <ava-slider 
            [value]="50" 
            [min]="0" 
            [max]="100"
            [step]="1"
            size="medium"
            type="default"
            [showTooltip]="true"
          > </ava-slider>
        </div>
      </div>
    </div>

    <div class="demo-item">
      <h4>Size Comparison - Input Type</h4>
      <div class="size-comparison">
        <div class="size-item">
          <span class="size-label">Small Size</span>
          <ava-slider 
            [value]="50" 
            [min]="0" 
            [max]="100"
            [step]="1"
            size="small"
            type="input"
          > </ava-slider>
        </div>
        <div class="size-item">
          <span class="size-label">Medium Size</span>
          <ava-slider 
            [value]="50" 
            [min]="0" 
            [max]="100"
            [step]="1"
            size="medium"
            type="input"
          > </ava-slider>
        </div>
      </div>
    </div>

    <div class="demo-item">
      <h4>Interactive Size Examples</h4>
      <div class="interactive-examples">
        <div class="example-item">
          <h5>Small Size Slider</h5>
          <p>Current value: {{ smallValue() }}</p>
          <ava-slider
            [value]="smallValue()"
            [min]="0"
            [max]="100"
            [step]="5"
            size="small"
            type="default"
            [showTooltip]="true"
            (valueChange)="onSmallChange($event)"
          >
          </ava-slider>
          <div class="size-info">
            <span class="size-badge small">Small</span>
            <span class="size-description">Compact slider ideal for tight spaces and forms</span>
          </div>
        </div>

        <div class="example-item">
          <h5>Medium Size Slider (Default)</h5>
          <p>Current value: {{ mediumValue() }}</p>
          <ava-slider
            [value]="mediumValue()"
            [min]="0"
            [max]="100"
            [step]="5"
            size="medium"
            type="default"
            [showTooltip]="true"
            (valueChange)="onMediumChange($event)"
          >
          </ava-slider>
          <div class="size-info">
            <span class="size-badge medium">Medium</span>
            <span class="size-description">Standard size for most use cases</span>
          </div>
        </div>
      </div>
    </div>

    <div class="demo-item">
      <h4>Size with Different Ranges</h4>
      <div class="size-comparison">
        <div class="size-item">
          <span class="size-label">Small (0-50)</span>
          <ava-slider 
            [value]="25" 
            [min]="0" 
            [max]="50"
            [step]="1"
            size="small"
            type="default"
            [showTooltip]="true"
          > </ava-slider>
        </div>
        <div class="size-item">
          <span class="size-label">Medium (0-200)</span>
          <ava-slider 
            [value]="100" 
            [min]="0" 
            [max]="200"
            [step]="5"
            size="medium"
            type="default"
            [showTooltip]="true"
          > </ava-slider>
        </div>
      </div>
    </div>
  </div>
</div>
