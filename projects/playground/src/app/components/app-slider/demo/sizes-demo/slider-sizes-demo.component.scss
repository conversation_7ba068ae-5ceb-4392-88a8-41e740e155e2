.center-demo {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: white;
  padding: 20px;
}

.demo-section {
  max-width: 800px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;

  h3 {
    text-align: center;
    margin-bottom: 20px;
    color: #333;
    font-size: 24px;
  }

  .demo-description {
    text-align: center;
    margin-bottom: 30px;
    color: #666;
    font-size: 16px;
    line-height: 1.5;
  }
}

.demo-item {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;

  h4 {
    margin-bottom: 20px;
    color: #555;
    font-size: 18px;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 10px;
  }

  h5 {
    margin-bottom: 10px;
    color: #444;
    font-size: 16px;
    font-weight: 600;
  }

  p {
    margin-bottom: 15px;
    color: #666;
    font-weight: 500;
  }
}

.size-comparison {
  display: flex;
  flex-direction: column;
  gap: 20px;

  .size-item {
    display: flex;
    align-items: center;
    gap: 15px;

    .size-label {
      min-width: 120px;
      font-weight: 500;
      color: #555;
    }
  }
}

.interactive-examples {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;

  .example-item {
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background-color: white;

    .size-info {
      margin-top: 15px;
      display: flex;
      align-items: center;
      gap: 15px;

      .size-badge {
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        &.small {
          background-color: #e3f2fd;
          color: #1976d2;
          border: 1px solid #bbdefb;
        }

        &.medium {
          background-color: #f3e5f5;
          color: #7b1fa2;
          border: 1px solid #e1bee7;
        }
      }

      .size-description {
        color: #666;
        font-size: 14px;
        font-style: italic;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .interactive-examples {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .demo-section {
    max-width: 100%;
    padding: 0 10px;
  }
}
