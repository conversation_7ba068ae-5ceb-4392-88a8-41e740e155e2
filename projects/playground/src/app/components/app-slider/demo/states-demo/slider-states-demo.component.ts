import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SliderComponent } from '../../../../../../../play-comp-library/src/public-api';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'ava-slider-states-demo',
  standalone: true,
  imports: [CommonModule, SliderComponent, FormsModule],
  templateUrl: './slider-states-demo.component.html',
  styleUrls: ['./slider-states-demo.component.scss'],
})
export class SliderStatesDemoComponent {
  enabledValue = 50;
  disabledValue = 30;
  readonlyValue = 70;

  onEnabledChange(value: number) {
    console.log('Enabled slider value:', value);
  }

  onDisabledChange(value: number) {
    console.log('Disabled slider value:', value);
  }

  onReadonlyChange(value: number) {
    console.log('Readonly slider value:', value);
  }
}
