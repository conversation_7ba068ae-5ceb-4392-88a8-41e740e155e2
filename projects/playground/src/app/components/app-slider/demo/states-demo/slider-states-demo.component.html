<div class="center-demo">
  <div class="demo-section">
    <h3>Slider Examples</h3>

    <div class="demo-item">
      <h4>Basic Slider</h4>
      <p>Current value: {{ enabledValue }}</p>
      <ava-slider
        [value]="enabledValue"
        [min]="0"
        [max]="100"
        [step]="5"
        (valueChange)="onEnabledChange($event)"
      >
      </ava-slider>
    </div>

    <div class="demo-item">
      <h4>Custom Range Slider</h4>
      <p>Current value: {{ disabledValue }}</p>
      <ava-slider
        [value]="disabledValue"
        [min]="0"
        [max]="200"
        [step]="10"
        (valueChange)="onDisabledChange($event)"
      >
      </ava-slider>
    </div>

    <div class="demo-item">
      <h4>Decimal Step Slider</h4>
      <p>Current value: {{ readonlyValue }}</p>
      <ava-slider
        [value]="readonlyValue"
        [min]="0"
        [max]="1"
        [step]="0.1"
        (valueChange)="onReadonlyChange($event)"
      >
      </ava-slider>
    </div>

    <div class="demo-item">
      <h4>Comparison Examples</h4>
      <div class="state-comparison">
        <div class="state-item">
          <span class="state-label">Basic</span>
          <ava-slider [value]="60" [min]="0" [max]="100"> </ava-slider>
        </div>
        <div class="state-item">
          <span class="state-label">Custom Range</span>
          <ava-slider [value]="60" [min]="0" [max]="200"> </ava-slider>
        </div>
        <div class="state-item">
          <span class="state-label">Decimal</span>
          <ava-slider [value]="0.6" [min]="0" [max]="1" [step]="0.1">
          </ava-slider>
        </div>
      </div>
    </div>
  </div>
</div>
