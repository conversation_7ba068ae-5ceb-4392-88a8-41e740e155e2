import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SliderComponent } from '../../../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'ava-icon-thumb-demo',
  standalone: true,
  imports: [CommonModule, SliderComponent],
  templateUrl: './icon-thumb-demo.component.html',
  styleUrls: ['./icon-thumb-demo.component.scss'],
})
export class IconThumbDemoComponent {
  volumeValue = 50;
  brightnessValue = 75;
  priceValue = 300;
  tempValue = 20;

  onVolumeChange(value: number) {
    this.volumeValue = value;
  }

  onBrightnessChange(value: number) {
    this.brightnessValue = value;
  }

  onPriceChange(value: number) {
    this.priceValue = value;
  }

  onTempChange(value: number) {
    this.tempValue = value;
  }
}
