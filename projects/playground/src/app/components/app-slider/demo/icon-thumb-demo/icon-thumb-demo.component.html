<div class="demo-container">
  <h2>Icon Thumb Slider Demo</h2>

  <div class="slider-group">
    <h3>Volume Control</h3>
    <ava-slider
      [min]="0"
      [max]="100"
      [value]="volumeValue"
      [handleIcon]="'volume-2'"
      [iconStart]="'volume-x'"
      [iconEnd]="'volume-2'"
      (valueChange)="onVolumeChange($event)"
    ></ava-slider>
    <p>Volume: {{ volumeValue }}%</p>
  </div>

  <div class="slider-group">
    <h3>Brightness Control</h3>
    <ava-slider
      [min]="0"
      [max]="100"
      [value]="brightnessValue"
      [handleIcon]="'sun'"
      [iconStart]="'moon'"
      [iconEnd]="'sun'"
      (valueChange)="onBrightnessChange($event)"
    ></ava-slider>
    <p>Brightness: {{ brightnessValue }}%</p>
  </div>

  <div class="slider-group">
    <h3>Price Range</h3>
    <ava-slider
      [min]="0"
      [max]="1000"
      [step]="10"
      [value]="priceValue"
      [handleIcon]="'dollar-sign'"
      (valueChange)="onPriceChange($event)"
    ></ava-slider>
    <p>Price: ${{ priceValue }}</p>
  </div>

  <div class="slider-group">
    <h3>Temperature Control</h3>
    <ava-slider
      [min]="-10"
      [max]="50"
      [value]="tempValue"
      [handleIcon]="'thermometer'"
      [iconStart]="'snowflake'"
      [iconEnd]="'flame'"
      (valueChange)="onTempChange($event)"
    ></ava-slider>
    <p>Temperature: {{ tempValue }}°C</p>
  </div>
</div>
