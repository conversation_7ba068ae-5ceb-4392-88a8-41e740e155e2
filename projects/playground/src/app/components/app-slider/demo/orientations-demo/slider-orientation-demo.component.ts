import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SliderComponent } from '../../../../../../../play-comp-library/src/public-api';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'ava-slider-orientation-demo',
  standalone: true,
  imports: [CommonModule, SliderComponent, FormsModule],
  templateUrl: './slider-orientation-demo.component.html',
  styleUrls: ['./slider-orientation-demo.component.scss'],
})
export class SliderOrientationDemoComponent {
  horizontalValue = 50;
  verticalValue = 75;
  decimalValue = 0.5;

  onHorizontalChange(value: number) {
    console.log('Horizontal slider value:', value);
  }

  onVerticalChange(value: number) {
    console.log('Vertical slider value:', value);
  }

  onDecimalChange(value: number) {
    console.log('Decimal slider value:', value);
  }
}
