.center-demo {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: white;
  padding: 20px;
}

.demo-section {
  max-width: 800px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;

  h3 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
    font-size: 24px;
  }
}

.demo-item {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fafafa;

  h4 {
    margin-bottom: 10px;
    color: #555;
    font-size: 18px;
  }

  p {
    margin-bottom: 15px;
    color: #666;
    font-weight: 500;
  }

  &.vertical-container {
    .vertical-slider-wrapper {
      display: flex;
      align-items: center;
      gap: 20px;

      p {
        margin-bottom: 0;
        min-width: 120px;
      }
    }
  }
}
