import { Component, Input } from '@angular/core';
import { ButtonComponent, DialogService } from 'play-comp-library';

@Component({
  selector: 'app-proper-sso-dialog',
  standalone: true,
  imports: [ButtonComponent],
  template: `
    <div>
      <div dialog-header>
        <h3>SSO Login ({{variant}} size)</h3>
      </div>
      <div dialog-body>
        <div class="sso-buttons">
          <ava-button
            [label]="'Google SSO Login'"
            [size]="variant"
            variant="primary"
            (userClick)="onSsoLogin()">
          </ava-button>
          <ava-button
            [label]="'Microsoft SSO Login'"
            [size]="variant"
            variant="primary"
            (userClick)="onSsoLogin()">
          </ava-button>
          <ava-button
            [label]="'Facebook SSO Login'"
            [size]="variant"
            variant="primary"
            (userClick)="onSsoLogin()">
          </ava-button>
        </div>
      </div>
      <div dialog-footer>
        <ava-button 
          label="Cancel" 
          variant="secondary" 
          [size]="variant"
          (userClick)="onClose()">
        </ava-button>
      </div>
    </div>
  `,
  styles: [`
    .sso-buttons {
      display: flex;
      flex-direction: column;
      gap: 1rem;
      align-items: center;
    }
  `]
})
export class ProperSsoDialogComponent {
  @Input() variant: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';

  constructor(private dialogService: DialogService) {}

  onSsoLogin() {
    // Add your SSO login logic here
    console.log('SSO Login clicked for size:', this.variant);
    this.dialogService.close();
  }

  onClose() {
    this.dialogService.close();
  }
}
