import { Component } from '@angular/core';
import { ButtonComponent, DialogService, IconComponent } from 'play-comp-library';
import { AvaTextboxComponent } from '../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'app-proper-feedback-modal',
  standalone: true,
  imports: [ButtonComponent, IconComponent, AvaTextboxComponent],
  template: `
  <div style="padding:24px">
    <div dialog-header>
      <h3
        style="color: #3B3F46;
        font-family: Mulish;
        font-size: 24px;
        font-style: normal;
        font-weight: 700;
        line-height:28px;"
      >
        Heading
      </h3>
    </div>
    <div dialog-body>
      <div style="text-align: center; margin:24px 0px">
        <ava-icon
          iconName="circle-check"
          iconSize="70px"
          iconColor="green"
        ></ava-icon>
      </div>
      <p
        style="align-self: stretch;
        color: #6B7280;
        text-align: center;
        font-family: Inter;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        margin-bottom:24px"
      >
        This agent will be sent back for corrections and modifications. Kindly
        comment what needs to be done
      </p>
      <div style="margin-bottom:24px">
        <ava-textbox
          label="Feedback Input"
          variant="default"
          placeholder="Enter text here"
          size="xl"
        ></ava-textbox>
      </div>
    </div>
    <div dialog-footer style="display:flex; gap:12px">
      <ava-button
        label="Label"
        variant="secondary"
        size="medium"
        (userClick)="onClose()"
        height="52px"
        width="165px"
      ></ava-button>
      <ava-button
        label="Label"
        variant="primary"
        size="medium"
        height="52px"
        (userClick)="onClose()"
        width="165px"
      ></ava-button>
    </div>
    </div>
  `
})
export class ProperFeedbackModalComponent {
  constructor(private dialogService: DialogService) {}
  onClose() {
    this.dialogService.close();
  }
}
