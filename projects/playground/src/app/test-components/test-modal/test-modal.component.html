<div class="t-container">
  <!-- SSO Login Variants Section -->
  <div class="con">
    <h2>SSO Login Variants</h2>
    <div class="col-12 col-sm-auto">
      <ava-button label="Extra Small SSO" variant="primary" (userClick)="openSSOLogin('xs')"></ava-button>
      <ava-button label="Small SSO" variant="primary" (userClick)="openSSOLogin('sm')"></ava-button>
      <ava-button label="Medium SSO" variant="primary" (userClick)="openSSOLogin('md')"></ava-button>
      <ava-button label="Large SSO" variant="primary" (userClick)="openSSOLogin('lg')"></ava-button>
      <ava-button label="Extra Large SSO" variant="primary" (userClick)="openSSOLogin('xl')"></ava-button>
    </div>
  </div>

  <!-- Modal Variants Section -->
  <div class="con">
    <h2>Modal Examples</h2>
    <div class="col-12 col-sm-auto">
      <ava-button label="Proper Simple Modal" variant="secondary" (userClick)="openProperSimpleModal()"></ava-button>
      <ava-button label="Proper Feedback Modal" variant="success" (userClick)="openProperFeedbackModal()"></ava-button>
      <ava-button label="Proper Scrollable Modal" variant="secondary" (userClick)="openProperScrollableModal()"></ava-button>
    </div>
  </div>
</div>
