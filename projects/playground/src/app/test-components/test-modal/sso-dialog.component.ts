import { Component, Input } from '@angular/core';
import { DialogService } from 'play-comp-library';
import { SSOLoginComponent } from '../../../../../play-comp-library/src/lib/composite-components/sso-login/sso-login.component';

@Component({
  selector: 'app-sso-dialog',
  standalone: true,
  imports: [SSOLoginComponent],
  template: `
    <div style="padding: 24px;">
      <ava-sso-login
        [variant]="variant"
        (login)="onLogin($event)"
        (ssoLogin)="onSSOLogin()"
        (forgotPassword)="onForgotPassword($event)"
        (troubleSignin)="onTroubleSignin()"
      ></ava-sso-login>
    </div>
  `
})
export class SSODialogComponent {
  @Input() variant: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';

  constructor(private dialogService: DialogService) {}

  onLogin(credentials: any) {
    console.log('Login credentials:', credentials);
    this.dialogService.close();
  }

  onSSOLogin() {
    console.log('SSO Login clicked');
    this.dialogService.close();
  }

  onForgotPassword(username: string) {
    console.log('Forgot password for:', username);
  }

  onTroubleSignin() {
    console.log('Trouble signing in clicked');
  }
}
