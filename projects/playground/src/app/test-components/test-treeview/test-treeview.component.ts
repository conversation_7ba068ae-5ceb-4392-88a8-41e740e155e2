import { Component, OnInit } from '@angular/core';
import { TreeviewComponent } from '../../../../../play-comp-library/src/lib/components/treeview/treeview.component';

export interface TreeNode {
  id?: string | number;
  name: string;
  icon?: string;
  expanded?: boolean;
  selected?: boolean;
  level?: number;
  children?: TreeNode[];
}

@Component({
  selector: 'app-test-treeview',
  standalone: true,
  imports: [TreeviewComponent],
  templateUrl: './test-treeview.component.html',
  styleUrls: ['./test-treeview.component.scss']
})
export class TestTreeviewComponent implements OnInit {
  treeData1: TreeNode[] = [
    {
      "id": "1",
      "name": "Engineering",
      "icon": "folder",
      "expanded": false,
      "selected": false,
      "children": [
        {
          "id": "1.1",
          "name": "Frontend",
          "icon": "folder",
          "selected": false
        },
        {
          "id": "1.2",
          "name": "Backend",
          "icon": "folder",
          "selected": false
        }
      ]
    },
    {
      "id": "2",
      "name": "Mobile",
      "icon": "folder",
      "expanded": false,
      "selected": false,
      "children": [
        {
          "id": "2.1",
          "name": "UI",
          "icon": "folder",
          "selected": false
        },
        {
          "id": "2.2",
          "name": "Sap",
          "icon": "folder",
          "selected": false
        }
      ]
    },
    {
      "id": "3",
      "name": "Marketing",
      "icon": "folder",
      "selected": false
    },
    {
      "id": "4",
      "name": "Operations",
      "icon": "folder",
      "selected": false
    }
  ];


  treeData2: TreeNode[] = [
    {
      "id": "1",
      "name": "Engineering",
      "icon": "folder",
      "expanded": false,
      "selected": false,
      "children": [
        {
          "id": "1.1",
          "name": "Frontend",
          "icon": "folder",
          "selected": false
        },
        {
          "id": "1.2",
          "name": "Backend",
          "icon": "folder",
          "selected": false
        }
      ]
    },
    {
      "id": "2",
      "name": "Mobile",
      "icon": "folder",
      "expanded": false,
      "selected": false,
      "children": [
        {
          "id": "2.1",
          "name": "UI",
          "icon": "folder",
          "selected": false
        },
        {
          "id": "2.2",
          "name": "Sap",
          "icon": "folder",
          "selected": false
        }
      ]
    },
    {
      "id": "3",
      "name": "Marketing",
      "icon": "folder",
      "selected": false
    },
    {
      "id": "4",
      "name": "Operations",
      "icon": "folder",
      "selected": false
    }
  ];



  treeData3: TreeNode[] = [
    {
      "id": "1",
      "name": "Engineering",
      "icon": "folder",
      "expanded": false,
      "selected": false,
      "children": [
        {
          "id": "1.1",
          "name": "Frontend",
          "icon": "folder",
          "selected": false
        },
        {
          "id": "1.2",
          "name": "Backend",
          "icon": "folder",
          "selected": false
        }
      ]
    },
    {
      "id": "2",
      "name": "Mobile",
      "icon": "folder",
      "expanded": false,
      "selected": false,
      "children": [
        {
          "id": "2.1",
          "name": "UI",
          "icon": "folder",
          "selected": false
        },
        {
          "id": "2.2",
          "name": "Sap",
          "icon": "folder",
          "selected": false
        }
      ]
    },
    {
      "id": "3",
      "name": "Marketing",
      "icon": "folder",
      "selected": false
    },
    {
      "id": "4",
      "name": "Operations",
      "icon": "folder",
      "selected": false
    }
  ];



  treeData4: TreeNode[] = [
    {
      "id": "1",
      "name": "Engineering",
      "icon": "folder",
      "expanded": false,
      "selected": false,
      "children": [
        {
          "id": "1.1",
          "name": "Frontend",
          "icon": "folder",
          "selected": false
        },
        {
          "id": "1.2",
          "name": "Backend",
          "icon": "folder",
          "selected": false
        }
      ]
    },
    {
      "id": "2",
      "name": "Mobile",
      "icon": "folder",
      "expanded": false,
      "selected": false,
      "children": [
        {
          "id": "2.1",
          "name": "UI",
          "icon": "folder",
          "selected": false
        },
        {
          "id": "2.2",
          "name": "Sap",
          "icon": "folder",
          "selected": false
        }
      ]
    },
    {
      "id": "3",
      "name": "Marketing",
      "icon": "folder",
      "selected": false
    },
    {
      "id": "4",
      "name": "Operations",
      "icon": "folder",
      "selected": false
    }
  ];

  treeData5: TreeNode[] = [
    {
      "id": "1",
      "name": "Engineering",
      "icon": "folder",
      "expanded": false,
      "selected": false,
      "children": [
        {
          "id": "1.1",
          "name": "Frontend",
          "icon": "folder",
          "selected": false
        },
        {
          "id": "1.2",
          "name": "Backend",
          "icon": "folder",
          "selected": false
        }
      ]
    },
    {
      "id": "2",
      "name": "Mobile",
      "icon": "folder",
      "expanded": false,
      "selected": false,
      "children": [
        {
          "id": "2.1",
          "name": "UI",
          "icon": "folder",
          "selected": false
        },
        {
          "id": "2.2",
          "name": "Sap",
          "icon": "folder",
          "selected": false
        }
      ]
    },
    {
      "id": "3",
      "name": "Marketing",
      "icon": "folder",
      "selected": false
    },
    {
      "id": "4",
      "name": "Operations",
      "icon": "folder",
      "selected": false
    }
  ];

  treeData6: TreeNode[] = [
    {
      "id": "1",
      "name": "Engineering",
      "icon": "folder",
      "expanded": false,
      "selected": false,
      "children": [
        {
          "id": "1.1",
          "name": "Frontend",
          "icon": "folder",
          "selected": false
        },
        {
          "id": "1.2",
          "name": "Backend",
          "icon": "folder",
          "selected": false
        }
      ]
    },
    {
      "id": "2",
      "name": "Mobile",
      "icon": "folder",
      "expanded": false,
      "selected": false,
      "children": [
        {
          "id": "2.1",
          "name": "UI",
          "icon": "folder",
          "selected": false
        },
        {
          "id": "2.2",
          "name": "Sap",
          "icon": "folder",
          "selected": false
        }
      ]
    },
    {
      "id": "3",
      "name": "Marketing",
      "icon": "folder",
      "selected": false
    },
    {
      "id": "4",
      "name": "Operations",
      "icon": "folder",
      "selected": false
    }
  ];

  treeData7: TreeNode[] = [
    {
      "id": "1",
      "name": "Engineering",
      "icon": "folder",
      "expanded": false,
      "selected": false,
      "children": [
        {
          "id": "1.1",
          "name": "Frontend",
          "icon": "folder",
          "selected": false
        },
        {
          "id": "1.2",
          "name": "Backend",
          "icon": "folder",
          "selected": false
        }
      ]
    },
    {
      "id": "2",
      "name": "Mobile",
      "icon": "folder",
      "expanded": false,
      "selected": false,
      "children": [
        {
          "id": "2.1",
          "name": "UI",
          "icon": "folder",
          "selected": false
        },
        {
          "id": "2.2",
          "name": "Sap",
          "icon": "folder",
          "selected": false
        }
      ]
    },
    {
      "id": "3",
      "name": "Marketing",
      "icon": "folder",
      "selected": false
    },
    {
      "id": "4",
      "name": "Operations",
      "icon": "folder",
      "selected": false
    }
  ];

  treeData8: TreeNode[] = [
    {
      "id": "1",
      "name": "Engineering",
      "icon": "folder",
      "expanded": false,
      "selected": false,
      "children": [
        {
          "id": "1.1",
          "name": "Frontend",
          "icon": "folder",
          "selected": false
        },
        {
          "id": "1.2",
          "name": "Backend",
          "icon": "folder",
          "selected": false
        }
      ]
    },
    {
      "id": "2",
      "name": "Mobile",
      "icon": "folder",
      "expanded": false,
      "selected": false,
      "children": [
        {
          "id": "2.1",
          "name": "UI",
          "icon": "folder",
          "selected": false
        },
        {
          "id": "2.2",
          "name": "Sap",
          "icon": "folder",
          "selected": false
        }
      ]
    },
    {
      "id": "3",
      "name": "Marketing",
      "icon": "folder",
      "selected": false
    },
    {
      "id": "4",
      "name": "Operations",
      "icon": "folder",
      "selected": false
    }
  ];

  treeData9: TreeNode[] = [
    {
      "id": "1",
      "name": "Engineering",
      "icon": "folder",
      "expanded": false,
      "selected": false,
      "children": [
        {
          "id": "1.1",
          "name": "Frontend",
          "icon": "folder",
          "selected": false
        },
        {
          "id": "1.2",
          "name": "Backend",
          "icon": "folder",
          "selected": false
        }
      ]
    },
    {
      "id": "2",
      "name": "Mobile",
      "icon": "folder",
      "expanded": false,
      "selected": false,
      "children": [
        {
          "id": "2.1",
          "name": "UI",
          "icon": "folder",
          "selected": false
        },
        {
          "id": "2.2",
          "name": "Sap",
          "icon": "folder",
          "selected": false
        }
      ]
    },
    {
      "id": "3",
      "name": "Marketing",
      "icon": "folder",
      "selected": false
    },
    {
      "id": "4",
      "name": "Operations",
      "icon": "folder",
      "selected": false
    }
  ];

  treeData10: TreeNode[] = [
    {
      "id": "1",
      "name": "Engineering",
      "icon": "folder",
      "expanded": false,
      "selected": false,
      "children": [
        {
          "id": "1.1",
          "name": "Frontend",
          "icon": "folder",
          "selected": false
        },
        {
          "id": "1.2",
          "name": "Backend",
          "icon": "folder",
          "selected": false
        }
      ]
    },
    {
      "id": "2",
      "name": "Mobile",
      "icon": "folder",
      "expanded": false,
      "selected": false,
      "children": [
        {
          "id": "2.1",
          "name": "UI",
          "icon": "folder",
          "selected": false
        },
        {
          "id": "2.2",
          "name": "Sap",
          "icon": "folder",
          "selected": false
        }
      ]
    },
    {
      "id": "3",
      "name": "Marketing",
      "icon": "folder",
      "selected": false
    },
    {
      "id": "4",
      "name": "Operations",
      "icon": "folder",
      "selected": false
    }
  ];

  ngOnInit(): void {

  }

  onSelect(node: TreeNode) {
    console.log('Selected node:', node);
    // you can do routing, highlighting, load details etc. here
  }

  onNodeSelect1(node: TreeNode) {
    // The recursive function now correctly identifies the node by its unique ID
    this.treeData1 = this.updateTreeSelection(this.treeData1, node);
  }


  onNodeSelect2(node: TreeNode) {
    // The recursive function now correctly identifies the node by its unique ID
    this.treeData2 = this.updateTreeSelection(this.treeData2, node);
  }

  onNodeSelect3(node: TreeNode) {
    // The recursive function now correctly identifies the node by its unique ID
    this.treeData3 = this.updateTreeSelection(this.treeData3, node);
  }


  onNodeSelect4(node: TreeNode) {
    // The recursive function now correctly identifies the node by its unique ID
    this.treeData4 = this.updateTreeSelection(this.treeData4, node);
  }

  onNodeSelect5(node: TreeNode) {
    // The recursive function now correctly identifies the node by its unique ID
    this.treeData5 = this.updateTreeSelection(this.treeData5, node);
  }

  onNodeSelect6(node: TreeNode) {
    // The recursive function now correctly identifies the node by its unique ID
    this.treeData6 = this.updateTreeSelection(this.treeData6, node);
  }

  onNodeSelect7(node: TreeNode) {
    // The recursive function now correctly identifies the node by its unique ID
    this.treeData7 = this.updateTreeSelection(this.treeData7, node);
  }
  onNodeSelect8(node: TreeNode) {
    // The recursive function now correctly identifies the node by its unique ID
    this.treeData8 = this.updateTreeSelection(this.treeData8, node);
  }
  onNodeSelect9(node: TreeNode) {
    // The recursive function now correctly identifies the node by its unique ID
    this.treeData9 = this.updateTreeSelection(this.treeData9, node);
  }
  onNodeSelect10(node: TreeNode) {
    // The recursive function now correctly identifies the node by its unique ID
    this.treeData10 = this.updateTreeSelection(this.treeData10, node);
  }



  private updateTreeSelection(nodes: TreeNode[], targetNode: TreeNode): TreeNode[] {
    if (!nodes) {
      return [];
    }

    return nodes.map(n => {
      const newNode: TreeNode = { ...n };

      if (newNode.children?.length) {
        newNode.children = this.updateTreeSelection(newNode.children, targetNode);
      }

      // Check if the current node's ID matches the target node's ID
      const isTargetNode = newNode.id === targetNode.id;

      // Only the target node should be selected; all others are deselected.
      newNode.selected = isTargetNode;

      return newNode;
    });
  }
}

