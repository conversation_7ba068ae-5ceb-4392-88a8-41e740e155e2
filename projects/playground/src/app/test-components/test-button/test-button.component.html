xlarge<div class="t-container">
    <div class="con">
        <div class="col-12 col-sm-auto">
            <ava-button label="ELarge" variant="primary" size="xlarge"></ava-button>
            <ava-button label="Large" variant="primary" size="large"></ava-button>
            <ava-button label="Medium" variant="primary" size="medium"></ava-button>
            <ava-button label="Small" variant="primary" size="small"></ava-button>
            <ava-button label="ESmall" variant="primary" size="xsmall"></ava-button>
        </div>

        <div class="col-12 col-sm-auto">
            <ava-button label="ELarge" variant="primary" size="xlarge" iconName="circle-check"
                iconPosition="left"></ava-button>
            <ava-button label="Large" variant="primary" size="large" iconName="circle-check"
                iconPosition="left"></ava-button>
            <ava-button label="Medium" variant="primary" size="medium" iconName="circle-check"
                iconPosition="left"></ava-button>
            <ava-button label="Small" variant="primary" size="small" iconName="circle-check"
                iconPosition="left"></ava-button>
            <ava-button label="ESmall" variant="primary" size="xsmall" iconName="circle-check"
                iconPosition="left"></ava-button>
        </div>
        <div class="col-12 col-sm-auto">
            <ava-button label="ELarge" variant="primary" size="xlarge" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button label="Large" variant="primary" size="large" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button label="Medium" variant="primary" size="medium" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button label="Small" variant="primary" size="small" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button label="ESmall" variant="primary" size="xsmall" iconName="circle-check"
                iconPosition="right"></ava-button>
        </div>

        <div class="col-12 col-sm-auto">
            <ava-button iconName="circle-check" iconPosition="only" variant="primary" iconColor="#fff"
                size="xlarge"></ava-button>
            <ava-button iconName="circle-check" iconPosition="only" variant="primary" iconColor="#fff"
                size="large"></ava-button>
            <ava-button iconName="circle-check" iconPosition="only" variant="primary" iconColor="#fff"
                size="medium"></ava-button>
            <ava-button iconName="circle-check" iconPosition="only" variant="primary" iconColor="#fff"
                size="small"></ava-button>
            <ava-button iconName="circle-check" iconPosition="only" variant="primary" iconColor="#fff"
                size="xsmall"></ava-button>
        </div>

        <div class="col-12 col-sm-auto">
            <ava-button [clear]="true" label="ELarge" variant="primary" size="xlarge" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button [clear]="true" label="Large" variant="primary" size="large" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button [clear]="true" label="Medium" variant="primary" size="medium" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button [clear]="true" label="Small" variant="primary" size="small" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button [clear]="true" label="ESmall" variant="primary" size="xsmall" iconName="circle-check"
                iconPosition="right"></ava-button>
        </div>




    </div>

    <div class="con">
        <div class="col-12 col-sm-auto">
            <ava-button label="ELarge" variant="secondary" size="xlarge"></ava-button>
            <ava-button label="Large" variant="secondary" size="large"></ava-button>
            <ava-button label="Medium" variant="secondary" size="medium"></ava-button>
            <ava-button label="Small" variant="secondary" size="small"></ava-button>
            <ava-button label="ESmall" variant="secondary" size="xsmall"></ava-button>
        </div>

        <div class="col-12 col-sm-auto">
            <ava-button label="ELarge" variant="secondary" size="xlarge" iconName="circle-check"
                iconPosition="left"></ava-button>
            <ava-button label="Large" variant="secondary" size="large" iconName="circle-check"
                iconPosition="left"></ava-button>
            <ava-button label="Medium" variant="secondary" size="medium" iconName="circle-check"
                iconPosition="left"></ava-button>
            <ava-button label="Small" variant="secondary" size="small" iconName="circle-check"
                iconPosition="left"></ava-button>
            <ava-button label="ESmall" variant="secondary" size="xsmall" iconName="circle-check"
                iconPosition="left"></ava-button>
        </div>
        <div class="col-12 col-sm-auto">
            <ava-button label="ELarge" variant="secondary" size="xlarge" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button label="Large" variant="secondary" size="large" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button label="Medium" variant="secondary" size="medium" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button label="Small" variant="secondary" size="small" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button label="ESmall" variant="secondary" size="xsmall" iconName="circle-check"
                iconPosition="right"></ava-button>
        </div>

        <div class="col-12 col-sm-auto">
            <ava-button iconName="circle-check" iconPosition="only" variant="secondary" iconColor="#fff"
                size="xlarge"></ava-button>
            <ava-button iconName="circle-check" iconPosition="only" variant="secondary" iconColor="#fff"
                size="large"></ava-button>
            <ava-button iconName="circle-check" iconPosition="only" variant="secondary" iconColor="#fff"
                size="medium"></ava-button>
            <ava-button iconName="circle-check" iconPosition="only" variant="secondary" iconColor="#fff"
                size="small"></ava-button>
            <ava-button iconName="circle-check" iconPosition="only" variant="secondary" iconColor="#fff"
                size="xsmall"></ava-button>
        </div>

        <div class="col-12 col-sm-auto">
            <ava-button [clear]="true" label="ELarge" variant="secondary" size="xlarge" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button [clear]="true" label="Large" variant="secondary" size="large" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button [clear]="true" label="Medium" variant="secondary" size="medium" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button [clear]="true" label="Small" variant="secondary" size="small" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button [clear]="true" label="ESmall" variant="secondary" size="xsmall" iconName="circle-check"
                iconPosition="right"></ava-button>
        </div>




    </div>


    <div class="con">
        <div class="col-12 col-sm-auto">
            <ava-button [pill]="true" label="ELarge" variant="primary" size="xlarge"></ava-button>
            <ava-button [pill]="true" label="Large" variant="primary" size="large"></ava-button>
            <ava-button [pill]="true" label="Medium" variant="primary" size="medium"></ava-button>
            <ava-button [pill]="true" label="Small" variant="primary" size="small"></ava-button>
            <ava-button [pill]="true" label="ESmall" variant="primary" size="xsmall"></ava-button>
        </div>

        <div class="col-12 col-sm-auto">
            <ava-button [pill]="true" label="ELarge" variant="primary" size="xlarge" iconName="circle-check"
                iconPosition="left"></ava-button>
            <ava-button [pill]="true" label="Large" variant="primary" size="large" iconName="circle-check"
                iconPosition="left"></ava-button>
            <ava-button [pill]="true" label="Medium" variant="primary" size="medium" iconName="circle-check"
                iconPosition="left"></ava-button>
            <ava-button [pill]="true" label="Small" variant="primary" size="small" iconName="circle-check"
                iconPosition="left"></ava-button>
            <ava-button [pill]="true" label="ESmall" variant="primary" size="xsmall" iconName="circle-check"
                iconPosition="left"></ava-button>
        </div>
        <div class="col-12 col-sm-auto">
            <ava-button [pill]="true" label="ELarge" variant="primary" size="xlarge" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button [pill]="true" label="Large" variant="primary" size="large" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button [pill]="true" label="Medium" variant="primary" size="medium" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button [pill]="true" label="Small" variant="primary" size="small" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button [pill]="true" label="ESmall" variant="primary" size="xsmall" iconName="circle-check"
                iconPosition="right"></ava-button>
        </div>

        <div class="col-12 col-sm-auto">
            <ava-button [pill]="true" iconName="circle-check" iconPosition="only" variant="primary" iconColor="#fff"
                size="xlarge"></ava-button>
            <ava-button [pill]="true" iconName="circle-check" iconPosition="only" variant="primary" iconColor="#fff"
                size="large"></ava-button>
            <ava-button [pill]="true" iconName="circle-check" iconPosition="only" variant="primary" iconColor="#fff"
                size="medium"></ava-button>
            <ava-button [pill]="true" iconName="circle-check" iconPosition="only" variant="primary" iconColor="#fff"
                size="small"></ava-button>
            <ava-button [pill]="true" iconName="circle-check" iconPosition="only" variant="primary" iconColor="#fff"
                size="xsmall"></ava-button>
        </div>






    </div>


    <div class="con">
        <div class="col-12 col-sm-auto">
            <ava-button [pill]="true" label="ELarge" variant="secondary" size="xlarge"></ava-button>
            <ava-button [pill]="true" label="Large" variant="secondary" size="large"></ava-button>
            <ava-button [pill]="true" label="Medium" variant="secondary" size="medium"></ava-button>
            <ava-button [pill]="true" label="Small" variant="secondary" size="small"></ava-button>
            <ava-button [pill]="true" label="ESmall" variant="secondary" size="xsmall"></ava-button>
        </div>

        <div class="col-12 col-sm-auto">
            <ava-button [pill]="true" label="ELarge" variant="secondary" size="xlarge" iconName="circle-check"
                iconPosition="left"></ava-button>
            <ava-button [pill]="true" label="Large" variant="secondary" size="large" iconName="circle-check"
                iconPosition="left"></ava-button>
            <ava-button [pill]="true" label="Medium" variant="secondary" size="medium" iconName="circle-check"
                iconPosition="left"></ava-button>
            <ava-button [pill]="true" label="Small" variant="secondary" size="small" iconName="circle-check"
                iconPosition="left"></ava-button>
            <ava-button [pill]="true" label="ESmall" variant="secondary" size="xsmall" iconName="circle-check"
                iconPosition="left"></ava-button>
        </div>
        <div class="col-12 col-sm-auto">
            <ava-button [pill]="true" label="ELarge" variant="secondary" size="xlarge" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button [pill]="true" label="Large" variant="secondary" size="large" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button [pill]="true" label="Medium" variant="secondary" size="medium" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button [pill]="true" label="Small" variant="secondary" size="small" iconName="circle-check"
                iconPosition="right"></ava-button>
            <ava-button [pill]="true" label="ESmall" variant="secondary" size="xsmall" iconName="circle-check"
                iconPosition="right"></ava-button>
        </div>

        <div class="col-12 col-sm-auto">
            <ava-button [pill]="true" iconName="circle-check" iconPosition="only" variant="secondary" iconColor="#fff"
                size="xlarge"></ava-button>
            <ava-button [pill]="true" iconName="circle-check" iconPosition="only" variant="secondary" iconColor="#fff"
                size="large"></ava-button>
            <ava-button [pill]="true" iconName="circle-check" iconPosition="only" variant="secondary" iconColor="#fff"
                size="medium"></ava-button>
            <ava-button [pill]="true" iconName="circle-check" iconPosition="only" variant="secondary" iconColor="#fff"
                size="small"></ava-button>
            <ava-button [pill]="true" iconName="circle-check" iconPosition="only" variant="secondary" iconColor="#fff"
                size="xsmall"></ava-button>
        </div>





    </div>



</div>