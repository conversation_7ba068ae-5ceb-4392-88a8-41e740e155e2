<h6>Large (lg)</h6>
<ava-button label="Success Large" variant="success" (userClick)="showSuccessLarge()"></ava-button>
<ava-button label="Warning Large" variant="warning" (userClick)="showWarningLarge()"></ava-button>
<ava-button label="Error Large" variant="danger" (userClick)="showErrorLarge()"></ava-button>

<h6>Medium (md)</h6>
<ava-button label="Success Medium" variant="success" (userClick)="showSuccessMedium()"></ava-button>
<ava-button label="Warning Medium" variant="warning" (userClick)="showWarningMedium()"></ava-button>
<ava-button label="Error Medium" variant="danger" (userClick)="showErrorMedium()"></ava-button>

<h6>Small (sm)</h6>
<ava-button label="Success Small" variant="success" (userClick)="showSuccessSmall()"></ava-button>
<ava-button label="Warning Small" variant="warning" (userClick)="showWarningSmall()"></ava-button>
<ava-button label="Error Small" variant="danger" (userClick)="showErrorSmall()"></ava-button>

<h6>Alert with action</h6>
<ava-button label="Action Required" variant="warning" (userClick)="showActionRequiredAlert()"></ava-button>
<ava-button label="Completed Successfully" variant="success"
  (userClick)="showCompletedSuccessfullyAlert()"></ava-button>
<ava-button label="Completed Successfully" variant="success"
  (userClick)="showCompletedSuccessfullyAlert2()"></ava-button>

<h6>Default Border Color Examples (Primary Color)</h6>
<h6>Confirmation Dialogs</h6>
<ava-button label="Confirmation With Border" variant="primary" (userClick)="showConfirmationWithBorder()"></ava-button>
<ava-button label="Confirmation Without Border" variant="secondary"
  (userClick)="showConfirmationWithoutBorder()"></ava-button>

<h6>Custom Dialogs (Default Variant)</h6>
<ava-button label="Custom With Default Border" variant="primary"
  (userClick)="showCustomWithDefaultBorder()"></ava-button>
<ava-button label="Custom Without Border" variant="secondary" (userClick)="showCustomWithoutBorder()"></ava-button>

<h6>Feedback Dialogs</h6>
<ava-button label="Feedback With Default Border" variant="primary"
  (userClick)="showFeedbackWithDefaultBorder()"></ava-button>
<ava-button label="Feedback Without Border" variant="secondary" (userClick)="showFeedbackWithoutBorder()"></ava-button>

<h6>Loading Dialogs</h6>
<ava-button label="Loading With Default Border" variant="primary"
  (userClick)="showLoadingWithDefaultBorder()"></ava-button>
<ava-button label="Loading Without Border" variant="secondary" (userClick)="showLoadingWithoutBorder()"></ava-button>

<h6>Info Dialogs</h6>
<ava-button label="Info With Default Border" variant="primary" (userClick)="showInfoWithDefaultBorder()"></ava-button>
<ava-button label="Info Without Border" variant="secondary" (userClick)="showInfoWithoutBorder()"></ava-button>