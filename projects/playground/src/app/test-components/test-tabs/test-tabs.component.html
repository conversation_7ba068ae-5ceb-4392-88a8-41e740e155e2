<!-- <PERSON><PERSON> Variant Squre -->
<div class="test-tabs-container">
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItems" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="rounded"
            [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="rounded"
            [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="md" [activeTabId]="activeTabId" buttonShape="rounded"
            [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="sm" [activeTabId]="activeTabId" buttonShape="rounded"
            [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="xs" [activeTabId]="activeTabId" buttonShape="rounded"
            [bordered]="true" [showContentPanels]="false"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItems" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="rounded"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="rounded"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="md" [activeTabId]="activeTabId" buttonShape="rounded"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="sm" [activeTabId]="activeTabId" buttonShape="rounded"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="xs" [activeTabId]="activeTabId" buttonShape="rounded"
            [showContentPanels]="false"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItems" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="rounded"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="rounded"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="md" [activeTabId]="activeTabId" buttonShape="rounded"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="sm" [activeTabId]="activeTabId" buttonShape="rounded"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="xs" [activeTabId]="activeTabId" buttonShape="rounded"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItems" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="rounded"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="rounded"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="md" [activeTabId]="activeTabId" buttonShape="rounded"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="sm" [activeTabId]="activeTabId" buttonShape="rounded"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="xs" [activeTabId]="activeTabId" buttonShape="rounded"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false"></ava-tabs>
    </div>
</div>


<div class="test-tabs-container">
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="rounded"
            [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="rounded"
            [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="md" [activeTabId]="activeTabId"
            buttonShape="rounded" [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="sm" [activeTabId]="activeTabId"
            buttonShape="rounded" [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="xs" [activeTabId]="activeTabId"
            buttonShape="rounded" [bordered]="true" [showContentPanels]="false"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="rounded"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="rounded"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="md" [activeTabId]="activeTabId"
            buttonShape="rounded" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="sm" [activeTabId]="activeTabId"
            buttonShape="rounded" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="xs" [activeTabId]="activeTabId"
            buttonShape="rounded" [showContentPanels]="false"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="rounded"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="rounded"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="md" [activeTabId]="activeTabId"
            buttonShape="rounded" [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="sm" [activeTabId]="activeTabId"
            buttonShape="rounded" [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="xs" [activeTabId]="activeTabId"
            buttonShape="rounded" [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="rounded"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="rounded"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="md" [activeTabId]="activeTabId"
            buttonShape="rounded" [activeButtonTabStyles]="customActiveButtonTabStyles"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="sm" [activeTabId]="activeTabId"
            buttonShape="rounded" [activeButtonTabStyles]="customActiveButtonTabStyles"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="xs" [activeTabId]="activeTabId"
            buttonShape="rounded" [activeButtonTabStyles]="customActiveButtonTabStyles"
            [showContentPanels]="false"></ava-tabs>
    </div>
</div>


<div class="test-tabs-container">
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="rounded"
            iconPosition="end" [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="rounded"
            iconPosition="end" [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="md" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="rounded" [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="sm" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="rounded" [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="xs" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="rounded" [bordered]="true" [showContentPanels]="false"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="rounded"
            iconPosition="end" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="rounded"
            iconPosition="end" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="md" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="rounded" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="sm" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="rounded" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="xs" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="rounded" [showContentPanels]="false"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="rounded"
            iconPosition="end" [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="rounded"
            iconPosition="end" [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="md" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="rounded" [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="sm" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="rounded" [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="xs" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="rounded" [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="rounded"
            iconPosition="end" [activeButtonTabStyles]="customActiveButtonTabStyles"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="rounded"
            iconPosition="end" [activeButtonTabStyles]="customActiveButtonTabStyles"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="md" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="rounded" [activeButtonTabStyles]="customActiveButtonTabStyles"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="sm" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="rounded" [activeButtonTabStyles]="customActiveButtonTabStyles"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="xs" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="rounded" [activeButtonTabStyles]="customActiveButtonTabStyles"
            [showContentPanels]="false"></ava-tabs>
    </div>
</div>

<!-- Button Variant pill -->
<div class="test-tabs-container">
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItems" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="pill"
            [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="pill"
            [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="md" [activeTabId]="activeTabId" buttonShape="pill"
            [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="sm" [activeTabId]="activeTabId" buttonShape="pill"
            [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="xs" [activeTabId]="activeTabId" buttonShape="pill"
            [bordered]="true" [showContentPanels]="false"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItems" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="pill"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="pill"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="md" [activeTabId]="activeTabId" buttonShape="pill"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="sm" [activeTabId]="activeTabId" buttonShape="pill"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="xs" [activeTabId]="activeTabId" buttonShape="pill"
            [showContentPanels]="false"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItems" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="pill"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="pill"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="md" [activeTabId]="activeTabId" buttonShape="pill"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="sm" [activeTabId]="activeTabId" buttonShape="pill"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="xs" [activeTabId]="activeTabId" buttonShape="pill"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItems" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="pill"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="pill"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="md" [activeTabId]="activeTabId" buttonShape="pill"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="sm" [activeTabId]="activeTabId" buttonShape="pill"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItems" variant="button" size="xs" [activeTabId]="activeTabId" buttonShape="pill"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false"></ava-tabs>
    </div>
</div>


<div class="test-tabs-container">
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="pill"
            [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="pill"
            [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="md" [activeTabId]="activeTabId" buttonShape="pill"
            [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="sm" [activeTabId]="activeTabId" buttonShape="pill"
            [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="xs" [activeTabId]="activeTabId" buttonShape="pill"
            [bordered]="true" [showContentPanels]="false"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="pill"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="pill"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="md" [activeTabId]="activeTabId" buttonShape="pill"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="sm" [activeTabId]="activeTabId" buttonShape="pill"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="xs" [activeTabId]="activeTabId" buttonShape="pill"
            [showContentPanels]="false"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="pill"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="pill"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="md" [activeTabId]="activeTabId" buttonShape="pill"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="sm" [activeTabId]="activeTabId" buttonShape="pill"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="xs" [activeTabId]="activeTabId" buttonShape="pill"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="pill"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="pill"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="md" [activeTabId]="activeTabId" buttonShape="pill"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="sm" [activeTabId]="activeTabId" buttonShape="pill"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="xs" [activeTabId]="activeTabId" buttonShape="pill"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false"></ava-tabs>
    </div>
</div>


<div class="test-tabs-container">
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="pill"
            iconPosition="end" [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="pill"
            iconPosition="end" [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="md" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="pill" [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="sm" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="pill" [bordered]="true" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="xs" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="pill" [bordered]="true" [showContentPanels]="false"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="pill"
            iconPosition="end" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="pill"
            iconPosition="end" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="md" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="pill" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="sm" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="pill" [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="xs" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="pill" [showContentPanels]="false"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="pill"
            iconPosition="end" [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="pill"
            iconPosition="end" [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="md" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="pill" [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="sm" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="pill" [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="xs" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="pill" [activeButtonTabStyles]="customActiveButtonTabStyles" [bordered]="true"
            [showContentPanels]="false"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="xl" [activeTabId]="activeTabId" buttonShape="pill"
            iconPosition="end" [activeButtonTabStyles]="customActiveButtonTabStyles"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon" variant="button" size="lg" [activeTabId]="activeTabId" buttonShape="pill"
            iconPosition="end" [activeButtonTabStyles]="customActiveButtonTabStyles"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="md" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="pill" [activeButtonTabStyles]="customActiveButtonTabStyles"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="sm" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="pill" [activeButtonTabStyles]="customActiveButtonTabStyles"
            [showContentPanels]="false"></ava-tabs>
        <ava-tabs [tabs]="tabItemsWithIcon_2" variant="button" size="xs" [activeTabId]="activeTabId" iconPosition="end"
            buttonShape="pill" [activeButtonTabStyles]="customActiveButtonTabStyles"
            [showContentPanels]="false"></ava-tabs>
    </div>
</div>


<div class="test-tabs-container-iconOnly">
    <div class="tabs-container">
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlySquare" size="xl"
            [showContentPanels]="false" [bordered]="true" (tabChange)="onTabChange($event, 'icon')"
            ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlySquare" size="lg"
            [showContentPanels]="false" [bordered]="true" (tabChange)="onTabChange($event, 'icon')"
            ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlySquare" size="md"
            [showContentPanels]="false" [bordered]="true" (tabChange)="onTabChange($event, 'icon')"
            ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlySquare" size="sm"
            [showContentPanels]="false" [bordered]="true" (tabChange)="onTabChange($event, 'icon')"
            ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlySquare" size="xs"
            [showContentPanels]="false" [bordered]="true" (tabChange)="onTabChange($event, 'icon')"
            ariaLabel="Icon-only tabs"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlySquare" size="xl"
            [showContentPanels]="false" [bordered]="false" (tabChange)="onTabChange($event, 'icon')"
            ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlySquare" size="lg"
            [showContentPanels]="false" [bordered]="false" (tabChange)="onTabChange($event, 'icon')"
            ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlySquare" size="md"
            [showContentPanels]="false" [bordered]="false" (tabChange)="onTabChange($event, 'icon')"
            ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlySquare" size="sm"
            [showContentPanels]="false" [bordered]="false" (tabChange)="onTabChange($event, 'icon')"
            ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlySquare" size="xs"
            [showContentPanels]="false" [bordered]="false" (tabChange)="onTabChange($event, 'icon')"
            ariaLabel="Icon-only tabs"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlyCircle" size="xl"
            [showContentPanels]="false" [bordered]="true" (tabChange)="onTabChange($event, 'icon')"
            ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlyCircle" size="lg"
            [showContentPanels]="false" [bordered]="true" (tabChange)="onTabChange($event, 'icon')"
            ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlyCircle" size="md"
            [showContentPanels]="false" [bordered]="true" (tabChange)="onTabChange($event, 'icon')"
            ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlyCircle" size="sm"
            [showContentPanels]="false" [bordered]="true" (tabChange)="onTabChange($event, 'icon')"
            ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlyCircle" size="xs"
            [showContentPanels]="false" [bordered]="true" (tabChange)="onTabChange($event, 'icon')"
            ariaLabel="Icon-only tabs"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlyCircle" size="xl"
            [showContentPanels]="false" [bordered]="false" (tabChange)="onTabChange($event, 'icon')"
            ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlyCircle" size="lg"
            [showContentPanels]="false" [bordered]="false" (tabChange)="onTabChange($event, 'icon')"
            ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlyCircle" size="md"
            [showContentPanels]="false" [bordered]="false" (tabChange)="onTabChange($event, 'icon')"
            ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlyCircle" size="sm"
            [showContentPanels]="false" [bordered]="false" (tabChange)="onTabChange($event, 'icon')"
            ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlyCircle" size="xs"
            [showContentPanels]="false" [bordered]="false" (tabChange)="onTabChange($event, 'icon')"
            ariaLabel="Icon-only tabs"></ava-tabs>
    </div>
</div>


<div class="test-tabs-container-iconOnly">
    <div class="tabs-container">
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlySquare" size="xl"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false" [bordered]="true"
            (tabChange)="onTabChange($event, 'icon')" ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlySquare" size="lg"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false" [bordered]="true"
            (tabChange)="onTabChange($event, 'icon')" ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlySquare" size="md"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false" [bordered]="true"
            (tabChange)="onTabChange($event, 'icon')" ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlySquare" size="sm"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false" [bordered]="true"
            (tabChange)="onTabChange($event, 'icon')" ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlySquare" size="xs"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false" [bordered]="true"
            (tabChange)="onTabChange($event, 'icon')" ariaLabel="Icon-only tabs"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlySquare" size="xl"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false" [bordered]="false"
            (tabChange)="onTabChange($event, 'icon')" ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlySquare" size="lg"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false" [bordered]="false"
            (tabChange)="onTabChange($event, 'icon')" ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlySquare" size="md"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false" [bordered]="false"
            (tabChange)="onTabChange($event, 'icon')" ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlySquare" size="sm"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false" [bordered]="false"
            (tabChange)="onTabChange($event, 'icon')" ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlySquare" size="xs"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false" [bordered]="false"
            (tabChange)="onTabChange($event, 'icon')" ariaLabel="Icon-only tabs"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlyCircle" size="xl"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false" [bordered]="true"
            (tabChange)="onTabChange($event, 'icon')" ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlyCircle" size="lg"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false" [bordered]="true"
            (tabChange)="onTabChange($event, 'icon')" ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlyCircle" size="md"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false" [bordered]="true"
            (tabChange)="onTabChange($event, 'icon')" ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlyCircle" size="sm"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false" [bordered]="true"
            (tabChange)="onTabChange($event, 'icon')" ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlyCircle" size="xs"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false" [bordered]="true"
            (tabChange)="onTabChange($event, 'icon')" ariaLabel="Icon-only tabs"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlyCircle" size="xl"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false" [bordered]="false"
            (tabChange)="onTabChange($event, 'icon')" ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlyCircle" size="lg"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false" [bordered]="false"
            (tabChange)="onTabChange($event, 'icon')" ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlyCircle" size="md"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false" [bordered]="false"
            (tabChange)="onTabChange($event, 'icon')" ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlyCircle" size="sm"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false" [bordered]="false"
            (tabChange)="onTabChange($event, 'icon')" ariaLabel="Icon-only tabs"></ava-tabs>
        <ava-tabs [tabs]="iconOnlyTabs" [activeTabId]="activeTabId" variant="iconOnlyCircle" size="xs"
            [activeButtonTabStyles]="customActiveButtonTabStyles" [showContentPanels]="false" [bordered]="false"
            (tabChange)="onTabChange($event, 'icon')" ariaLabel="Icon-only tabs"></ava-tabs>
    </div>
</div>


<div class="test-tabs-container">
    <div class="tabs-container">
        <ava-tabs [tabs]="basicTabs" [activeTabId]="activeTabId" variant="default" size="xl" [showContentPanels]="false"
            (tabChange)="onTabChange($event, 'basic')" ariaLabel="Basic navigation tabs"></ava-tabs>
        <ava-tabs [tabs]="basicTabs" [activeTabId]="activeTabId" variant="default" size="lg" [showContentPanels]="false"
            (tabChange)="onTabChange($event, 'basic')" ariaLabel="Basic navigation tabs"></ava-tabs>
        <ava-tabs [tabs]="basicTabs" [activeTabId]="activeTabId" variant="default" size="md" [showContentPanels]="false"
            (tabChange)="onTabChange($event, 'basic')" ariaLabel="Basic navigation tabs"></ava-tabs>
        <ava-tabs [tabs]="basicTabs" [activeTabId]="activeTabId" variant="default" size="sm" [showContentPanels]="false"
            (tabChange)="onTabChange($event, 'basic')" ariaLabel="Basic navigation tabs"></ava-tabs>
        <ava-tabs [tabs]="basicTabs" [activeTabId]="activeTabId" variant="default" size="xs" [showContentPanels]="false"
            (tabChange)="onTabChange($event, 'basic')" ariaLabel="Basic navigation tabs"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]=" basicTabs_2" [activeTabId]="activeTabId" variant="default" size="xl"
            [showContentPanels]="false" (tabChange)="onTabChange($event, 'basic')"
            ariaLabel="Basic navigation tabs"></ava-tabs>
        <ava-tabs [tabs]=" basicTabs_2" [activeTabId]="activeTabId" variant="default" size="lg"
            [showContentPanels]="false" (tabChange)="onTabChange($event, 'basic')"
            ariaLabel="Basic navigation tabs"></ava-tabs>
        <ava-tabs [tabs]=" basicTabs_3" [activeTabId]="activeTabId" variant="default" size="md"
            [showContentPanels]="false" (tabChange)="onTabChange($event, 'basic')"
            ariaLabel="Basic navigation tabs"></ava-tabs>
        <ava-tabs [tabs]=" basicTabs_3" [activeTabId]="activeTabId" variant="default" size="sm"
            [showContentPanels]="false" (tabChange)="onTabChange($event, 'basic')"
            ariaLabel="Basic navigation tabs"></ava-tabs>
        <ava-tabs [tabs]=" basicTabs_3" [activeTabId]="activeTabId" variant="default" size="xs"
            [showContentPanels]="false" (tabChange)="onTabChange($event, 'basic')"
            ariaLabel="Basic navigation tabs"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="basicTabs_2" [activeTabId]="activeTabId" variant="default" size="xl" [iconPosition]="'end'"
            [showContentPanels]="false" (tabChange)="onTabChange($event, 'basic')"
            ariaLabel="Basic navigation tabs"></ava-tabs>
        <ava-tabs [tabs]="basicTabs_2" [activeTabId]="activeTabId" variant="default" size="lg" [iconPosition]="'end'"
            [showContentPanels]="false" (tabChange)="onTabChange($event, 'basic')"
            ariaLabel="Basic navigation tabs"></ava-tabs>
        <ava-tabs [tabs]="basicTabs_3" [activeTabId]="activeTabId" variant="default" size="md" [iconPosition]="'end'"
            [showContentPanels]="false" (tabChange)="onTabChange($event, 'basic')"
            ariaLabel="Basic navigation tabs"></ava-tabs>
        <ava-tabs [tabs]="basicTabs_3" [activeTabId]="activeTabId" variant="default" size="sm" [iconPosition]="'end'"
            [showContentPanels]="false" (tabChange)="onTabChange($event, 'basic')"
            ariaLabel="Basic navigation tabs"></ava-tabs>
        <ava-tabs [tabs]="basicTabs_3" [activeTabId]="activeTabId" variant="default" size="xs" [iconPosition]="'end'"
            [showContentPanels]="false" (tabChange)="onTabChange($event, 'basic')"
            ariaLabel="Basic navigation tabs"></ava-tabs>
    </div>
    <div class="tabs-container">
        <ava-tabs [tabs]="basicTabs_4" [activeTabId]="activeTabId" variant="default" size="xl"
            [showContentPanels]="false" (tabChange)="onTabChange($event, 'basic')"
            ariaLabel="Basic navigation tabs"></ava-tabs>
        <ava-tabs [tabs]="basicTabs_4" [activeTabId]="activeTabId" variant="default" size="lg"
            [showContentPanels]="false" (tabChange)="onTabChange($event, 'basic')"
            ariaLabel="Basic navigation tabs"></ava-tabs>
        <ava-tabs [tabs]="basicTabs_5" [activeTabId]="activeTabId" variant="default" size="md"
            [showContentPanels]="false" (tabChange)="onTabChange($event, 'basic')"
            ariaLabel="Basic navigation tabs"></ava-tabs>
        <ava-tabs [tabs]="basicTabs_5" [activeTabId]="activeTabId" variant="default" size="sm"
            [showContentPanels]="false" (tabChange)="onTabChange($event, 'basic')"
            ariaLabel="Basic navigation tabs"></ava-tabs>
        <ava-tabs [tabs]="basicTabs_5" [activeTabId]="activeTabId" variant="default" size="xs"
            [showContentPanels]="false" (tabChange)="onTabChange($event, 'basic')"
            ariaLabel="Basic navigation tabs"></ava-tabs>
    </div>
</div>