import { Component } from '@angular/core';
import { TabItem } from '../../../../../play-comp-library/src/public-api';
import { TabsComponent } from '../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'app-test-tabs',
  imports: [TabsComponent],
  templateUrl: './test-tabs.component.html',
  styleUrl: './test-tabs.component.scss'
})
export class TestTabsComponent {

  tabItems: TabItem[] = [
    { id: 'tab1', label: 'Tab1', content: 'Content 1' },
    { id: 'tab2', label: 'Tab2', content: 'Content 2' },
    { id: 'tab3', label: 'Tab3', content: 'Content 1' },
    { id: 'tab4', label: 'Tab4', content: 'Content 2' },
  ];
  tabItemsWithIcon: TabItem[] = [
    { id: 'tab1', label: 'Tab1', content: 'Content 1', iconName: 'circle-check' },
    { id: 'tab2', label: 'Tab2', content: 'Content 2', iconName: 'circle-check' },
    { id: 'tab3', label: 'Tab3', content: 'Content 1', iconName: 'circle-check' },
    { id: 'tab4', label: 'Tab4', content: 'Content 2', iconName: 'circle-check' },
  ];
  // Button variant tabs for xs,sm and md sizes
  tabItemsWithIcon_2: TabItem[] = [
    { id: 'tab1', label: 'Tab1', content: 'Content 1', iconName: 'chevron-right' },
    { id: 'tab2', label: 'Tab2', content: 'Content 2', iconName: 'chevron-right' },
    { id: 'tab3', label: 'Tab3', content: 'Content 1', iconName: 'chevron-right' },
    { id: 'tab4', label: 'Tab4', content: 'Content 2', iconName: 'chevron-right' },
  ];
  basicTabs: TabItem[] = [
    { id: 'tab1', label: 'Tab1', content: 'Content 1' },
    { id: 'tab2', label: 'Tab2', content: 'Content 2' },
    { id: 'tab3', label: 'Tab3', content: 'Content 1' },
    { id: 'tab4', label: 'Tab4', content: 'Content 2' },
  ];
  basicTabs_2: TabItem[] = [
    { id: 'tab1', label: 'Tab1', content: 'Content 1', iconName: 'circle-check' },
    { id: 'tab2', label: 'Tab2', content: 'Content 2', iconName: 'circle-check' },
    { id: 'tab3', label: 'Tab3', content: 'Content 1', iconName: 'circle-check' },
    { id: 'tab4', label: 'Tab4', content: 'Content 2', iconName: 'circle-check' },
  ];
  basicTabs_3: TabItem[] = [
    { id: 'tab1', label: 'Tab1', content: 'Content 1', iconName: 'chevron-right' },
    { id: 'tab2', label: 'Tab2', content: 'Content 2', iconName: 'chevron-right' },
    { id: 'tab3', label: 'Tab3', content: 'Content 1', iconName: 'chevron-right' },
    { id: 'tab4', label: 'Tab4', content: 'Content 2', iconName: 'chevron-right' },
  ];
  basicTabs_4: TabItem[] = [
    { id: 'tab1', label: '', content: 'Content 1', iconName: 'circle-check' },
    { id: 'tab2', label: '', content: 'Content 2', iconName: 'circle-check' },
    { id: 'tab3', label: '', content: 'Content 1', iconName: 'circle-check' },
    { id: 'tab4', label: '', content: 'Content 2', iconName: 'circle-check' },
  ];
  basicTabs_5: TabItem[] = [
    { id: 'tab1', label: '', content: 'Content 1', iconName: 'chevron-right' },
    { id: 'tab2', label: '', content: 'Content 2', iconName: 'chevron-right' },
    { id: 'tab3', label: '', content: 'Content 1', iconName: 'chevron-right' },
    { id: 'tab4', label: '', content: 'Content 2', iconName: 'chevron-right' },
  ];
  iconOnlyTabs: TabItem[] = [
    { id: 'icon1', label: 'circle-check', iconName: 'circle-check' },
    { id: 'icon2', label: 'circle-check', iconName: 'circle-check' },
    { id: 'icon3', label: 'circle-check', iconName: 'circle-check' },
    { id: 'icon4', label: 'circle-check', iconName: 'circle-check' },

  ];
  iconOnlyTabs_2: TabItem[] = [
    { id: 'icon1', label: 'chevron-right', iconName: 'chevron-right' },
    { id: 'icon2', label: 'chevron-right', iconName: 'chevron-right' },
    { id: 'icon3', label: 'chevron-right', iconName: 'chevron-right' },
    { id: 'icon4', label: 'chevron-right', iconName: 'chevron-right' },
  ];
  activeTabId = 'tab1';

  customActiveButtonTabStyles = {
    background: 'linear-gradient(135deg, rgba(233, 30, 99, 1) 0%, rgba(156, 39, 176, 1) 100%)',
  };

  activeTabIds = {
    basic: 'home',
    button: 'overview',
    icon: 'dashboard',
    advanced: 'projects',
    scrollable: 'tab1',
  };

  onTabChange(tab: TabItem, context: string): void {
    console.log(`Tab changed in ${context}:`, tab);
    switch (context) {
      case 'basic':
        this.activeTabIds.basic = tab.id;
        break;
      case 'button':
        this.activeTabIds.button = tab.id;
        break;
      case 'icon':
        this.activeTabIds.icon = tab.id;
        break;
      case 'advanced':
        this.activeTabIds.advanced = tab.id;
        break;
      case 'scrollable':
        this.activeTabIds.scrollable = tab.id;
        break;
    }
  }
}


