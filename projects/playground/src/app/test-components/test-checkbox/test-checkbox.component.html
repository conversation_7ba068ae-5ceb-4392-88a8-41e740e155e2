<div class="t-container">
    <div class="checkbox-grid">
        <ava-checkbox variant="default" size="small"></ava-checkbox>
        <ava-checkbox variant="default" size="medium"></ava-checkbox>
        <ava-checkbox variant="default" size="large"></ava-checkbox>

        <ava-checkbox variant="default" size="small" [isChecked]="true"></ava-checkbox>
        <ava-checkbox variant="default" size="medium" [isChecked]="true"></ava-checkbox>
        <ava-checkbox variant="default" size="large" [isChecked]="true"></ava-checkbox>

        <ava-checkbox variant="default" size="small" [indeterminate]="true"></ava-checkbox>
        <ava-checkbox variant="default" size="medium" [indeterminate]="true"></ava-checkbox>
        <ava-checkbox variant="default" size="large" [indeterminate]="true"></ava-checkbox>

        <ava-checkbox variant="with-bg" size="small"></ava-checkbox>
        <ava-checkbox variant="with-bg" size="medium"></ava-checkbox>
        <ava-checkbox variant="with-bg" size="large"></ava-checkbox>

        <ava-checkbox variant="with-bg" size="small" [isChecked]="true"></ava-checkbox>
        <ava-checkbox variant="with-bg" size="medium" [isChecked]="true"></ava-checkbox>
        <ava-checkbox variant="with-bg" size="large" [isChecked]="true"></ava-checkbox>
        <ava-checkbox variant="animated" size="small"></ava-checkbox>
        <ava-checkbox variant="animated" size="medium"></ava-checkbox>
        <ava-checkbox variant="animated" size="large"></ava-checkbox>
        <ava-checkbox variant="animated" size="small" [isChecked]="true"></ava-checkbox>
        <ava-checkbox variant="animated" size="medium" [isChecked]="true"></ava-checkbox>
        <ava-checkbox variant="animated" size="large" [isChecked]="true"></ava-checkbox>
    </div>

    <div class="checkbox-list">
        <ava-checkbox variant="default" size="medium" label="Label"></ava-checkbox>
        <ava-checkbox variant="default" size="medium" [disable]="true"></ava-checkbox>
        <ava-checkbox variant="default" size="medium" [disable]="true" [isChecked]="true"></ava-checkbox>
        <ava-checkbox variant="default" size="medium" [disable]="true" [indeterminate]="true"></ava-checkbox>
    </div>
</div>
