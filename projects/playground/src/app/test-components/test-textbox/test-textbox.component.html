<div class="t-container">
    <div class="each">
        <div>
            <p>Extra large</p>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter text here" size="xl"></ava-textbox>
            <ava-textbox label="Label Name" value="Enter text here" variant="success" placeholder="Enter text here"
                size="xl"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here"
                placeholder="Enter text here" size="xl"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder=" Enter text here" [disabled]="true"
                size="xl"></ava-textbox>

            <p>Large</p>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter text here" size="lg"></ava-textbox>
            <ava-textbox label="Label Name" variant="success" placeholder="Enter text here" size="lg"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here"
                placeholder="Enter text here" size="lg"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter text here" [disabled]="true"
                size="lg"></ava-textbox>
            <p>Medium</p>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter text here" size="md"></ava-textbox>
            <ava-textbox label="Label Name" variant="success" placeholder="Enter text here" size="md"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here"
                placeholder=" Enter text here" size="md"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter text here" [disabled]="true"
                size="md"></ava-textbox>
            <p>Small</p>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter text here" size="sm"></ava-textbox>
            <ava-textbox label="Label Name" variant="success" placeholder="Enter text here" size="sm"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here"
                placeholder=" Enter text here" size="sm"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter text here" [disabled]="true"
                size="sm"></ava-textbox>
            <p>Extra Small</p>

            <ava-textbox label="Label Name" variant="default" placeholder="Enter text here" size="xs"></ava-textbox>
            <ava-textbox label="Label Name" variant="success" placeholder="Enter text here" size="xs"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here"
                placeholder=" Enter text here" size="xs"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter text here" [disabled]="true"
                size="xs"></ava-textbox>
        </div>


        <div class="phide">
            <p>Extra large</p>
            <ava-textbox label="Label Name" inputKind="phone" inputKindLabel="US" labelPosition="start"
                variant="default" placeholder="Enter Ph. No." size="xl"></ava-textbox>
            <ava-textbox label="Label Name" value="***********" variant="success" placeholder="Enter Ph. No."
                inputKind="phone" inputKindLabel="US" labelPosition="start" size="xl"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here" placeholder="Enter Ph. No."
                inputKind="phone" inputKindLabel="US" labelPosition="start" size="xl"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter Ph. No." inputKind="currency"
                inputKindLabel="US" labelPosition="start" size="xl" [disabled]="true"></ava-textbox>



            <p>Large</p>
            <ava-textbox label="Label Name" inputKind="phone" inputKindLabel="US" labelPosition="start"
                variant="default" placeholder="Enter Ph. No." size="lg"></ava-textbox>
            <ava-textbox label="Label Name" value="***********" variant="success" placeholder="Enter Ph. No."
                inputKind="phone" inputKindLabel="US" labelPosition="start" size="lg"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here" placeholder="Enter Ph. No."
                inputKind="phone" inputKindLabel="US" labelPosition="start" size="lg"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter Ph. No." inputKind="phone"
                inputKindLabel="US" labelPosition="start" size="lg" [disabled]="true"></ava-textbox>

            <p>Medium</p>
            <ava-textbox label="Label Name" inputKind="phone" inputKindLabel="US" labelPosition="start"
                variant="default" placeholder="Enter Ph. No." size="md"></ava-textbox>
            <ava-textbox label="Label Name" value="***********" variant="success" placeholder="Enter Ph. No."
                inputKind="phone" inputKindLabel="US" labelPosition="start" size="md"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here" placeholder="Enter Ph. No."
                inputKind="phone" inputKindLabel="US" labelPosition="start" size="md"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter Ph. No." inputKind="phone"
                inputKindLabel="US" labelPosition="start" size="md" [disabled]="true"></ava-textbox>
            <p>Small</p>
            <ava-textbox label="Label Name" inputKind="phone" inputKindLabel="US" labelPosition="start"
                variant="default" placeholder="Enter Ph. No." size="sm"></ava-textbox>
            <ava-textbox label="Label Name" value="***********" variant="success" placeholder="Enter Ph. No."
                inputKind="phone" inputKindLabel="US" labelPosition="start" size="sm"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here" placeholder="Enter Ph. No."
                inputKind="phone" inputKindLabel="US" labelPosition="start" size="sm"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter Ph. No." inputKind="phone"
                inputKindLabel="US" labelPosition="start" size="sm" [disabled]="true"></ava-textbox>
            <p>Extra Small</p>

            <ava-textbox label="Label Name" inputKind="phone" inputKindLabel="US" labelPosition="start"
                variant="default" placeholder="Enter Ph. No." size="xs"></ava-textbox>
            <ava-textbox label="Label Name" value="***********" variant="success" placeholder="Enter Ph. No."
                inputKind="phone" inputKindLabel="US" labelPosition="start" size="xs"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here" placeholder="Enter Ph. No."
                inputKind="phone" inputKindLabel="US" labelPosition="start" size="xs"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter Ph. No." inputKind="phone"
                inputKindLabel="US" labelPosition="start" size="xs" [disabled]="true"></ava-textbox>
        </div>


        <div class="phide">
            <p>Extra large</p>
            <ava-textbox label="Label Name" inputKind="currency" inputKindLabel="USD" labelPosition="end"
                variant="default" placeholder="100000" size="xl"></ava-textbox>
            <ava-textbox label="Label Name" value="100000" variant="success" placeholder="100000" inputKind="currency"
                inputKindLabel="USD" labelPosition="end" size="xl"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here" placeholder="100000"
                inputKind="currency" inputKindLabel="USD" labelPosition="end" size="xl"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="100000" inputKind="currency"
                inputKindLabel="USD" labelPosition="end" size="xl" [disabled]="true"></ava-textbox>



            <p>Large</p>
            <ava-textbox label="Label Name" inputKind="currency" inputKindLabel="USD" labelPosition="end"
                variant="default" placeholder="100000" size="lg"></ava-textbox>
            <ava-textbox label="Label Name" value="100000" variant="success" placeholder="100000" inputKind="currency"
                inputKindLabel="USD" labelPosition="end" size="lg"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here" placeholder="100000"
                inputKind="currency" inputKindLabel="USD" labelPosition="end" size="lg"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="100000" inputKind="currency"
                inputKindLabel="USD" labelPosition="end" size="lg" [disabled]="true"></ava-textbox>

            <p>Medium</p>
            <ava-textbox label="Label Name" inputKind="currency" inputKindLabel="USD" labelPosition="end"
                variant="default" placeholder="100000" size="md"></ava-textbox>
            <ava-textbox label="Label Name" value="100000" variant="success" placeholder="100000" inputKind="currency"
                inputKindLabel="USD" labelPosition="end" size="md"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here" placeholder="100000"
                inputKind="currency" inputKindLabel="USD" labelPosition="end" size="md"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="100000" inputKind="currency"
                inputKindLabel="USD" labelPosition="end" size="md" [disabled]="true"></ava-textbox>
            <p>Small</p>
            <ava-textbox label="Label Name" inputKind="currency" inputKindLabel="USD" labelPosition="end"
                variant="default" placeholder="100000" size="sm"></ava-textbox>
            <ava-textbox label="Label Name" value="100000" variant="success" placeholder="100000" inputKind="currency"
                inputKindLabel="USD" labelPosition="end" size="sm"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here" placeholder="100000"
                inputKind="currency" inputKindLabel="USD" labelPosition="end" size="sm"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="100000" inputKind="currency"
                inputKindLabel="USD" labelPosition="end" size="sm" [disabled]="true"></ava-textbox>
            <p>Extra Small</p>

            <ava-textbox label="Label Name" inputKind="currency" inputKindLabel="USD" labelPosition="end"
                variant="default" placeholder="100000" size="xs"></ava-textbox>
            <ava-textbox label="Label Name" value="100000" variant="success" placeholder="100000" inputKind="currency"
                inputKindLabel="USD" labelPosition="end" size="xs"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here" placeholder="100000"
                inputKind="currency" inputKindLabel="USD" labelPosition="end" size="xs"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="100000" inputKind="currency"
                inputKindLabel="USD" labelPosition="end" size="xs" [disabled]="true"></ava-textbox>
        </div>

        <div class="phide">
            <p>Extra large</p>
            <ava-textbox label="Label Name" inputKind="email" inputKindLabel="" labelPosition="start" variant="default"
                placeholder="Enter email here" size="xl"></ava-textbox>
            <ava-textbox label="Label Name" value="<EMAIL>" variant="success" placeholder="Enter email here"
                inputKind="email" inputKindLabel="" labelPosition="start" size="xl"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here"
                placeholder="Enter email here" inputKind="email" inputKindLabel="" labelPosition="start"
                size="xl"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter email here" inputKind="email"
                inputKindLabel="" labelPosition="start" size="xl" [disabled]="true"></ava-textbox>



            <p>Large</p>
            <ava-textbox label="Label Name" inputKind="email" inputKindLabel="" labelPosition="start" variant="default"
                placeholder="Enter email here" size="lg"></ava-textbox>
            <ava-textbox label="Label Name" value="<EMAIL>" variant="success" placeholder="Enter email here"
                inputKind="email" inputKindLabel="" labelPosition="start" size="lg"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here"
                placeholder="Enter email here" inputKind="email" inputKindLabel="" labelPosition="start"
                size="lg"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter email here" inputKind="email"
                inputKindLabel="" labelPosition="start" size="lg" [disabled]="true"></ava-textbox>

            <p>Medium</p>
            <ava-textbox label="Label Name" inputKind="email" inputKindLabel="" labelPosition="start" variant="default"
                placeholder="Enter email here" size="md"></ava-textbox>
            <ava-textbox label="Label Name" value="<EMAIL>" variant="success" placeholder="Enter email here"
                inputKind="email" inputKindLabel="" labelPosition="start" size="md"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here"
                placeholder="Enter email here" inputKind="email" inputKindLabel="" labelPosition="start"
                size="md"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter email here" inputKind="curemailrency"
                inputKindLabel="" labelPosition="start" size="md" [disabled]="true"></ava-textbox>
            <p>Small</p>
            <ava-textbox label="Label Name" inputKind="email" inputKindLabel="" labelPosition="start" variant="default"
                placeholder="Enter email here" size="sm"></ava-textbox>
            <ava-textbox label="Label Name" value="<EMAIL>" variant="success" placeholder="Enter email here"
                inputKind="email" inputKindLabel="" labelPosition="start" size="sm"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here"
                placeholder="Enter email here" inputKind="email" inputKindLabel="" labelPosition="start"
                size="sm"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter email here" inputKind="email"
                inputKindLabel="" labelPosition="start" size="sm" [disabled]="true"></ava-textbox>
            <p>Extra Small</p>

            <ava-textbox label="Label Name" inputKind="email" inputKindLabel="" labelPosition="start" variant="default"
                placeholder="Enter email here" size="xs"></ava-textbox>
            <ava-textbox label="Label Name" value="<EMAIL>" variant="success" placeholder="Enter email here"
                inputKind="email" inputKindLabel="" labelPosition="start" size="xs"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here"
                placeholder="Enter email here" inputKind="email" inputKindLabel="" labelPosition="start"
                size="xs"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter email here" inputKind="email"
                inputKindLabel="" labelPosition="start" size="xs" [disabled]="true"></ava-textbox>
        </div>


        <div class="phide l">
            <p>Extra large</p>
            <ava-textbox label="Label Name" inputKind="password" inputKindLabel="" labelPosition="end" variant="default"
                placeholder="Enter password here" size="xl"></ava-textbox>
            <ava-textbox label="Label Name" value="123456789" variant="success" placeholder="Enter password here"
                inputKind="password" inputKindLabel="" labelPosition="end" size="xl"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here"
                placeholder="Enter password here" inputKind="password" inputKindLabel="" labelPosition="end"
                size="xl"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter password here" inputKind="password"
                inputKindLabel="" labelPosition="end" size="xl" [disabled]="true"></ava-textbox>



            <p>Large</p>
            <ava-textbox label="Label Name" inputKind="password" inputKindLabel="" labelPosition="end" variant="default"
                placeholder="Enter password here" size="lg"></ava-textbox>
            <ava-textbox label="Label Name" value="123456789" variant="success" placeholder="Enter password here"
                inputKind="password" inputKindLabel="" labelPosition="end" size="lg"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here"
                placeholder="Enter password here" inputKind="password" inputKindLabel="" labelPosition="end"
                size="lg"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter password here" inputKind="password"
                inputKindLabel="" labelPosition="end" size="lg" [disabled]="true"></ava-textbox>

            <p>Medium</p>
            <ava-textbox label="Label Name" inputKind="password" inputKindLabel="" labelPosition="end" variant="default"
                placeholder="Enter password here" size="md"></ava-textbox>
            <ava-textbox label="Label Name" value="123456789" variant="success" placeholder="Enter password here"
                inputKind="password" inputKindLabel="" labelPosition="end" size="md"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here"
                placeholder="Enter password here" inputKind="password" inputKindLabel="" labelPosition="end"
                size="md"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter password here"
                inputKind="curemailrency" inputKindLabel="" labelPosition="end" size="md"
                [disabled]="true"></ava-textbox>
            <p>Small</p>
            <ava-textbox label="Label Name" inputKind="password" inputKindLabel="" labelPosition="end" variant="default"
                placeholder="Enter password here" size="sm"></ava-textbox>
            <ava-textbox label="Label Name" value="123456789" variant="success" placeholder="Enter password here"
                inputKind="password" inputKindLabel="" labelPosition="end" size="sm"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here"
                placeholder="Enter password here" inputKind="password" inputKindLabel="" labelPosition="end"
                size="sm"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter password here" inputKind="password"
                inputKindLabel="" labelPosition="end" size="sm" [disabled]="true"></ava-textbox>
            <p>Extra Small</p>

            <ava-textbox label="Label Name" inputKind="password" inputKindLabel="" labelPosition="end" variant="default"
                placeholder="Enter password here" size="xs"></ava-textbox>
            <ava-textbox label="Label Name" value="123456789" variant="success" placeholder="Enter password here"
                inputKind="password" inputKindLabel="" labelPosition="end" size="xs"></ava-textbox>
            <ava-textbox label="Label Name" variant="error" error="Error message comes here"
                placeholder="Enter password here" inputKind="password" inputKindLabel="" labelPosition="end"
                size="xs"></ava-textbox>
            <ava-textbox label="Label Name" variant="default" placeholder="Enter password here" inputKind="password"
                inputKindLabel="" labelPosition="end" size="xs" [disabled]="true"></ava-textbox>
        </div>







    </div>
</div>