.rating-demo {
    padding: 20px;
    background-color: var(--global-color-gray-50);
    min-height: 100vh;
    margin: 0 auto;

    display: flex;
    flex-direction: column;
    /* stack vertically */
    gap: 1.5rem;
    /* space between rating items */
}

.rating-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    /* space between label and stars */
    background: white;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
    transition: transform 0.2s ease, box-shadow 0.2s ease;

    label {
        font-size: 0.9rem;
        font-weight: 500;
        color: #555;
    }
}