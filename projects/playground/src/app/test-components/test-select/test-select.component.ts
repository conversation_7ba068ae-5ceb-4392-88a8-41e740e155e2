import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AvatarsComponent, ButtonComponent, CheckboxComponent, IconComponent, SelectComponent, SelectOptionComponent } from '../../../../../play-comp-library/src/public-api';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
@Component({
  selector: 'app-test-select',
  imports: [SelectComponent, SelectOptionComponent, CheckboxComponent, AvatarsComponent, IconComponent, CommonModule, ReactiveFormsModule, ButtonComponent],
  templateUrl: './test-select.component.html',
  styleUrl: './test-select.component.scss'
})
export class TestSelectComponent {
  form: FormGroup;
  statusOptions = [
    { value: 'active', label: 'Active', icon: 'circle-check', color: 'green' },
    { value: 'inactive', label: 'Inactive', icon: 'x-circle', color: 'red' },
    { value: 'pending', label: 'Pending', icon: 'clock', color: 'orange' },
    { value: 'suspended', label: 'Suspended', icon: 'pause', color: 'gray' }
  ];

  constructor(private fb: FormBuilder) {
    this.form = this.fb.group({
      status: ['', Validators.required]
    });
  }
  getFieldError(fieldName: string): string {
    const field = this.form.get(fieldName);
    if (field && field.invalid && field.touched) {
      if (field.errors?.['required']) {
        return `${fieldName} is required`;
      }
    }
    return '';
  }
  triggerError() {
    const control = this.form.get('status');
    if (control) {
      control.markAsTouched();
      control.updateValueAndValidity();
    }
  }
  clearError() {
    const control = this.form.get('status');
    if (control) {
      control.reset();
    }
  }
  acceptTerms = false;
  onCheckboxChange(checked: boolean) {
    console.log('Checkbox changed:', checked);
  }
  sampleImageUrl = 'assets/1.svg';
  basicIcons = [
    { name: 'home', label: 'Home' },
    { name: 'user', label: 'User' },
    { name: 'settings', label: 'Settings' },
    { name: 'heart', label: 'Heart' },
    { name: 'star', label: 'Star' },
  ];
  onIconClick(event: Event): void {
    console.log('Icon clicked:', event);
  }
  users = [
    { value: 1, label: 'John Doe' },
    { value: 2, label: 'Jane Smith' },
    { value: 3, label: 'Jack Wilson' },
    { value: 4, label: 'James Brown' }
  ];
  selectedUsers: number[] = [];
}
