.t-container {
    margin-left: 15px;
    margin-top: 10px;
}

.each {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 20px;

    .b {
        p {
            border-bottom: 2px solid #ccc;
            font-weight: bold;
            color: blue;
        }

    }



}

/* First row: All other dropdowns */
.each:not(.error-row)>div {
    width: 280px;
    height: auto;
    margin-right: 1%;
    margin-bottom: 20px;
}

/* Second row: Error and success states (horizontal) */
.error-row {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
}

.error-row>div {
    width: 280px;
    margin-right: 20px;
    margin-bottom: 0;
}



ava-select {
    display: block;
    margin-bottom: 15px;
}

.option-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
}

.option-left {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-right: 20px;
}

ava-icon {
    flex-shrink: 0;
}

.mt-3 {
    margin-top: 1rem;
    display: flex;
    gap: 10px;
}