                <div class="stepper-container">
                <div class="stepper-horizontal">
                <ava-stepper
                  [steps]="largeLabels"
                  [currentStep]="largeStep"
                  [iconColor]="'#ffff'"
                  [iconSize]="'24'"
                  orientation="horizontal"
                  size="large"
                  [showNavigation]="true"
                  (stepChange)="onLargeStepChange($event)">
                </ava-stepper>

                 <ava-stepper
                  [steps]="mediumLabels"
                  [currentStep]="mediumStep"
                  [iconColor]="'#ffff'"
                  [iconSize]="'20'"
                  orientation="horizontal"
                  size="medium"
                  [showNavigation]="true"
                  (stepChange)="onMediumStepChange($event)">
                </ava-stepper>

                <ava-stepper
                  [steps]="smallLabels"
                  [currentStep]="smallStep"
                  [iconColor]="'#ffff'"
                  [iconSize]="'16'"
                  orientation="horizontal"
                  size="small"
                  [showNavigation]="true"
                  (stepChange)="onSmallStepChange($event)">
                </ava-stepper>


                 <ava-stepper
                  [steps]="smallLabels"
                  [currentStep]="smallStep"
                  [iconColor]="'#ffff'"
                  [iconSize]="'12'"
                  orientation="horizontal"
                  size="xsmall"
                  [showNavigation]="true"
                  (stepChange)="onSmallStepChange($event)">
                </ava-stepper>
                </div>


               <div class="stepper-vertical">
                <div class="vertical-large">
                  <ava-stepper
                    [steps]="verticalLabelSet1"
                    [currentStep]="verticalStep1"
                    [iconColor]="'#ffff'"
                    [iconSize]="'24'"
                    orientation="vertical"
                    size="large"
                    [showNavigation]="true"
                    (stepChange)="onVerticalStep1Change($event)">
                  </ava-stepper>
                  </div>


                   <ava-stepper
                    [steps]="verticalLabelSet2"
                    [currentStep]="verticalStep2"
                    [iconColor]="'#ffff'"
                    [iconSize]="'20'"
                    orientation="vertical"
                    size="medium"
                    [showNavigation]="true"
                    (stepChange)="onVerticalStep2Change($event)">
                  </ava-stepper>


                   <ava-stepper
                    [steps]="verticalLabelSet3"
                    [currentStep]="verticalStep3"
                    [iconColor]="'#ffff'"
                    [iconSize]="'16'"
                    orientation="vertical"
                    size="small"
                    [showNavigation]="true"
                    (stepChange)="onVerticalStep3Change($event)">
                  </ava-stepper>


                  <ava-stepper
                    [steps]="verticalLabelSet3"
                    [currentStep]="verticalStep3"
                    [iconColor]="'#ffff'"
                    [iconSize]="'12'"
                    orientation="vertical"
                    size="xsmall"
                    [showNavigation]="true"
                    (stepChange)="onVerticalStep3Change($event)">
                  </ava-stepper>
                  </div>
                </div>