// Following the same pattern as other test components
.t-container {
  padding: 20px;

  .con {
    margin-bottom: 40px;

    .col-12 {
      display: flex;
      flex-direction: column;
      gap: 20px;

      &.col-sm-auto {
        @media (min-width: 576px) {
          flex-direction: row;
          flex-wrap: wrap;
          align-items: flex-start;
          gap: 20px;
        }
      }

      ava-otp {
        flex: 0 0 auto;
        min-width: 200px;
      }
    }
  }
}

// Responsive spacing
@media (max-width: 768px) {
  .t-container {
    padding: 10px;

    .con {
      margin-bottom: 20px;

      .col-12 {
        gap: 15px;

        ava-otp {
          min-width: 180px;
        }
      }
    }
  }
}
