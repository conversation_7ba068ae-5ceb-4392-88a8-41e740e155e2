<div class="tags-container">
    <!-- ========== XL SIZE ========== -->
    <h3>XL Size</h3>

    <!-- XL: Pill Variants -->
    <div class="size-group">
        <h4>XL - Pill</h4>
        <ava-tag type='tag' color="default" size="xl" label="Label" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="xl" label="Label" icon="map-pin" iconPosition="start" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="xl" label="Label" avatar="https://randomuser.me/api/portraits/men/32.jpg" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="xl" label="Label" avatar="https://randomuser.me/api/portraits/men/32.jpg" [disabled]="true"  [removable]="true" [pill]="true"></ava-tag>
    </div>

    <!-- XL: Without Pill -->
    <div class="size-group">
        <h4>XL - Without Pill</h4>
        <ava-tag type='tag' color="default" size="xl" label="Label" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="xl" label="Label" icon="map-pin" iconPosition="start" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="xl" label="Label" avatar="https://randomuser.me/api/portraits/men/32.jpg" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="xl" label="Label" [disabled]="true" [removable]="true"></ava-tag>
    </div>

    <!-- XL: Outlined Pill -->
    <div class="size-group">
        <h4>XL - Outlined Pill</h4>
        <ava-tag type='tag' color="default" variant="outlined" size="xl" label="Label" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="xl" label="Label" icon="map-pin" iconPosition="start" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="xl" label="Label" avatar="https://randomuser.me/api/portraits/men/32.jpg" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="xl" label="Label" [disabled]="true" [removable]="true" [pill]="true"></ava-tag>
    </div>

    <!-- XL: Outlined Without Pill -->
    <div class="size-group">
        <h4>XL - Outlined Without Pill</h4>
        <ava-tag type='tag' color="default" variant="outlined" size="xl" label="Label" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="xl" label="Label" icon="map-pin" iconPosition="start" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="xl" label="Label" avatar="https://randomuser.me/api/portraits/men/32.jpg" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="xl" label="Label" [disabled]="true" [removable]="true"></ava-tag>
    </div>

    <!-- ========== LG SIZE ========== -->
    <h3>LG Size</h3>

    <!-- LG: Pill Variants -->
    <div class="size-group">
        <h4>LG - Pill</h4>
        <ava-tag type='tag' color="default" size="lg" label="Label" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="lg" label="Label" icon="map-pin" iconPosition="start" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="lg" label="Label" avatar="https://randomuser.me/api/portraits/women/44.jpg" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="lg" label="Label" [disabled]="true" [removable]="true" [pill]="true"></ava-tag>
    </div>

    <!-- LG: Without Pill -->
    <div class="size-group">
        <h4>LG - Without Pill</h4>
        <ava-tag type='tag' color="default" size="lg" label="Label" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="lg" label="Label" icon="map-pin" iconPosition="start" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="lg" label="Label" avatar="https://randomuser.me/api/portraits/women/44.jpg" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="lg" label="Label" [disabled]="true" [removable]="true"></ava-tag>
    </div>

    <!-- LG: Outlined Pill -->
    <div class="size-group">
        <h4>LG - Outlined Pill</h4>
        <ava-tag type='tag' color="default" variant="outlined" size="lg" label="Label" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="lg" label="Label" icon="map-pin" iconPosition="start" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="lg" label="Label" avatar="https://randomuser.me/api/portraits/women/44.jpg" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="lg" label="Label" [disabled]="true" [removable]="true" [pill]="true"></ava-tag>
    </div>

    <!-- LG: Outlined Without Pill -->
    <div class="size-group">
        <h4>LG - Outlined Without Pill</h4>
        <ava-tag type='tag' color="default" variant="outlined" size="lg" label="Label" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="lg" label="Label" icon="map-pin" iconPosition="start" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="lg" label="Label" avatar="https://randomuser.me/api/portraits/women/44.jpg" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="lg" label="Label" [disabled]="true" [removable]="true"></ava-tag>
    </div>

    <!-- ========== MD SIZE ========== -->
    <h3>MD Size</h3>

    <!-- MD: Pill Variants -->
    <div class="size-group">
        <h4>MD - Pill</h4>
        <ava-tag type='tag' color="default" size="md" label="Label" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="md" label="Label" icon="map-pin" iconPosition="start" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="md" label="Label" [disabled]="true" [removable]="true" [pill]="true"></ava-tag>
    </div>

    <!-- MD: Without Pill -->
    <div class="size-group">
        <h4>MD - Without Pill</h4>
        <ava-tag type='tag' color="default" size="md" label="Label" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="md" label="Label" icon="map-pin" iconPosition="start" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="md" label="Label" [disabled]="true" [removable]="true"></ava-tag>
    </div>

    <!-- MD: Outlined Pill -->
    <div class="size-group">
        <h4>MD - Outlined Pill</h4>
        <ava-tag type='tag' color="default" variant="outlined" size="md" label="Label" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="md" label="Label" icon="map-pin" iconPosition="start" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="md" label="Label" [disabled]="true" [removable]="true" [pill]="true"></ava-tag>
    </div>

    <!-- MD: Outlined Without Pill -->
    <div class="size-group">
        <h4>MD - Outlined Without Pill</h4>
        <ava-tag type='tag' color="default" variant="outlined" size="md" label="Label" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="md" label="Label" icon="map-pin" iconPosition="start" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="md" label="Label" [disabled]="true" [removable]="true"></ava-tag>
    </div>

    <!-- ========== SM SIZE ========== -->
    <h3>SM Size</h3>

    <!-- SM: Pill Variants -->
    <div class="size-group">
        <h4>SM - Pill</h4>
        <ava-tag type='tag' color="default" size="sm" label="Label" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="sm" label="Label" icon="map-pin" iconPosition="start" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="sm" label="Label" [disabled]="true" [removable]="true" [pill]="true"></ava-tag>
    </div>

    <!-- SM: Without Pill -->
    <div class="size-group">
        <h4>SM - Without Pill</h4>
        <ava-tag type='tag' color="default" size="sm" label="Label" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="sm" label="Label" icon="map-pin" iconPosition="start" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="sm" label="Label" [disabled]="true" [removable]="true"></ava-tag>
    </div>

    <!-- SM: Outlined Pill -->
    <div class="size-group">
        <h4>SM - Outlined Pill</h4>
        <ava-tag type='tag' color="default" variant="outlined" size="sm" label="Label" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="sm" label="Label" icon="map-pin" iconPosition="start" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="sm" label="Label" [disabled]="true" [removable]="true" [pill]="true"></ava-tag>
    </div>

    <!-- SM: Outlined Without Pill -->
    <div class="size-group">
        <h4>SM - Outlined Without Pill</h4>
        <ava-tag type='tag' color="default" variant="outlined" size="sm" label="Label" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="sm" label="Label" icon="map-pin" iconPosition="start" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="sm" label="Label" [disabled]="true" [removable]="true"></ava-tag>
    </div>

    <!-- ========== XS SIZE ========== -->
    <h3>XS Size</h3>

    <!-- XS: Pill Variants -->
    <div class="size-group">
        <h4>XS - Pill</h4>
        <ava-tag type='tag' color="default" size="xs" label="Label" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="xs" label="Label" icon="map-pin" iconPosition="start" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="xs" label="Label" [disabled]="true" [removable]="true" [pill]="true"></ava-tag>
    </div>

    <!-- XS: Without Pill -->
    <div class="size-group">
        <h4>XS - Without Pill</h4>
        <ava-tag type='tag' color="default" size="xs" label="Label" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="xs" label="Label" icon="map-pin" iconPosition="start" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" size="xs" label="Label" [disabled]="true" [removable]="true"></ava-tag>
    </div>

    <!-- XS: Outlined Pill -->
    <div class="size-group">
        <h4>XS - Outlined Pill</h4>
        <ava-tag type='tag' color="default" variant="outlined" size="xs" label="Label" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="xs" label="Label" icon="map-pin" iconPosition="start" [removable]="true" [pill]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="xs" label="Label" [disabled]="true" [removable]="true" [pill]="true"></ava-tag>
    </div>

    <!-- XS: Outlined Without Pill -->
    <div class="size-group">
        <h4>XS - Outlined Without Pill</h4>
        <ava-tag type='tag' color="default" variant="outlined" size="xs" label="Label" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="xs" label="Label" icon="map-pin" iconPosition="start" [removable]="true" (clicked)="onTagClick()"></ava-tag>
        <ava-tag type='tag' color="default" variant="outlined" size="xs" label="Label" [disabled]="true" [removable]="true"></ava-tag>
    </div>

</div>
