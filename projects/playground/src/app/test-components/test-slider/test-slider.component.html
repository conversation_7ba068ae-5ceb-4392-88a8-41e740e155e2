<div class="t-container">
  <!-- Original sliders -->
  <div class="con">
    <h3>Original Sliders (Default Type)</h3>
    <div class="col-12 col-sm-auto">
      <p>Value: {{ basicValue }}</p>
      <ava-slider [value]="0" [min]="0" [max]="100" [step]="1" size="small" type="default" [showTooltip]="true" (valueChange)="onBasicValueChange($event)">
      </ava-slider>
      <br>
      <ava-slider [value]="25" [min]="0" [max]="100" [step]="1" size="small" type="default" [showTooltip]="true" (valueChange)="onBasicValueChange($event)">
      </ava-slider>
      <br>
      <ava-slider [value]="50" [min]="0" [max]="100" [step]="1" size="small" type="default" [showTooltip]="true" (valueChange)="onBasicValueChange($event)">
      </ava-slider>
      <br>
      <ava-slider [value]="100" [min]="0" [max]="100" [step]="1" size="small" type="default" [showTooltip]="true"
        (valueChange)="onBasicValueChange($event)">
      </ava-slider>
    </div>
  </div>

  <div class="con">
    <ava-slider [value]="0" [min]="0" [max]="100" [step]="1" size="medium" type="default" [showTooltip]="true" (valueChange)="onBasicValueChange($event)">
    </ava-slider>
    <br>
    <ava-slider [value]="25" [min]="0" [max]="100" [step]="1" size="medium" type="default" [showTooltip]="true" (valueChange)="onBasicValueChange($event)">
    </ava-slider>
    <br>
    <ava-slider [value]="50" [min]="0" [max]="100" [step]="1" size="medium" type="default" [showTooltip]="true" (valueChange)="onBasicValueChange($event)">
    </ava-slider>
    <br>
    <ava-slider [value]="100" [min]="0" [max]="100" [step]="1" size="medium" type="default" [showTooltip]="true" (valueChange)="onBasicValueChange($event)">
    </ava-slider>
  </div>

  <!-- New Input Type Sliders -->
  <div class="con">
    <h3>New Input Type Sliders</h3>

    <div class="demo-section">
      <h4>Default Type vs Input Type Comparison</h4>
      <div class="comparison-row">
        <div class="comparison-item">
          <p>Default Type (tooltip below): {{ defaultTypeValue }}</p>
          <ava-slider
            [value]="defaultTypeValue"
            [min]="0"
            [max]="100"
            [step]="1"
            type="default"
            [showTooltip]="true"
            size="medium"
            (valueChange)="onDefaultTypeChange($event)">
          </ava-slider>
        </div>

        <div class="comparison-item">
          <p>Input Type (textbox beside): {{ inputTypeValue }}</p>
          <ava-slider
            [value]="inputTypeValue"
            [min]="0"
            [max]="100"
            [step]="1"
            type="input"
            size="medium"
            (valueChange)="onInputTypeChange($event)">
          </ava-slider>
        </div>
      </div>
    </div>

    <div class="demo-section">
      <h4>Input Type with Different Sizes</h4>
      <div class="size-row">
        <div class="size-item">
          <p>Small + Input Type: {{ inputTypeSmallValue }}</p>
          <ava-slider
            [value]="inputTypeSmallValue"
            [min]="0"
            [max]="100"
            [step]="1"
            type="input"
            size="small"
            (valueChange)="onInputTypeSmallChange($event)">
          </ava-slider>
        </div>

        <div class="size-item">
          <p>Medium + Input Type: {{ inputTypeMediumValue }}</p>
          <ava-slider
            [value]="inputTypeMediumValue"
            [min]="0"
            [max]="100"
            [step]="1"
            type="input"
            size="medium"
            (valueChange)="onInputTypeMediumChange($event)">
          </ava-slider>
        </div>
      </div>
    </div>
  </div>
</div>
