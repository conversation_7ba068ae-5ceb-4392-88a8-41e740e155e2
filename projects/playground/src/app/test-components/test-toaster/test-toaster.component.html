<div class="t-container">
  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>1. All Success Toast Variants</h2>
        <div class="demo-container">
          <div class="control-group">
            <div class="control-row">
              <ava-button
                label="Success Large"
                variant="success"
                (userClick)="showBasicSuccesslarge()"
              ></ava-button>
              <ava-button
                label="Success Medium"
                variant="success"
                (userClick)="showBasicSuccessMedium()"
              ></ava-button>
              <ava-button
                label="Success Small"
                variant="success"
                (userClick)="showBasicSuccessSmall()"
              ></ava-button>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>1. All warning Toast Variants</h2>
        <div class="demo-container">
          <div class="control-group">
            <div class="control-row">
              <ava-button
                label="warning Large"
                variant="warning"
                (userClick)="showBasicWarninglarge()"
              ></ava-button>
              <ava-button
                label="warning Medium"
                variant="warning"
                (userClick)="showBasicWarningMedium()"
              ></ava-button>
              <ava-button
                label="warning Small"
                variant="warning"
                (userClick)="showBasicWarningSmall()"
              ></ava-button>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>1. All Error Toast Variants</h2>
        <div class="demo-container">
          <div class="control-group">
            <div class="control-row">
              <ava-button
                label="Error Large"
                variant="danger"
                (userClick)="showBasicErrorlarge()"
              ></ava-button>
              <ava-button
                label="Error Medium"
                variant="danger"
                (userClick)="showBasicErrorMedium()"
              ></ava-button>
              <ava-button
                label="Error Small"
                variant="danger"
                (userClick)="showBasicErrorSmall()"
              ></ava-button>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>

  <div class="row">
    <div class="col-12">
      <section class="doc-section">
        <h2>1. All Info Toast Variants</h2>
        <div class="demo-container">
          <div class="control-group">
            <div class="control-row">
              <ava-button
                label="Info Large"
                variant="info"
                (userClick)="showBasicInfoLarge()"
              ></ava-button>
              <ava-button
                label="Info Medium"
                variant="info"
                (userClick)="showBasicInfoMedium()"
              ></ava-button>
              <ava-button
                label="Info Small"
                variant="info"
                (userClick)="showBasicInfoSmall()"
              ></ava-button>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</div>
