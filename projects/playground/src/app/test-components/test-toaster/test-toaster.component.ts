import { Component } from '@angular/core';
import {
  ButtonComponent,
  IconComponent,
  ToastService,
} from '../../../../../play-comp-library/src/public-api';

@Component({
  selector: 'app-test-toaster',
  imports: [ButtonComponent],
  templateUrl: './test-toaster.component.html',
  styleUrl: './test-toaster.component.scss',
})
export class TestToasterComponent {
  constructor(private toastService: ToastService) {}

  //success
  showBasicSuccesslarge() {
    this.toastService.success({
      title: 'Successfully created!',
      message: 'Your changes have been saved successfully',
      duration: 4000,
      customWidth: '400px',
      design: 'modern',
      size: 'large',
    });
  }
  showBasicSuccessMedium() {
    this.toastService.success({
      title: 'Successfully created!',
      message: 'Your changes have been saved successfully',
      duration: 4000,
      customWidth: '350px',
      design: 'modern',
      size: 'medium',
    });
  }
  showBasicSuccessSmall() {
    this.toastService.success({
      title: 'Successfully created!',
      message: 'Your changes have been saved successfully',
      duration: 4000,
      customWidth: '300px',
      design: 'modern',
      size: 'small',
    });
  }

  //warning

  showBasicWarninglarge() {
    this.toastService.warning({
      title: 'Action Required',
      message: 'Incomplete fields.Please fill in all required information.',
      duration: 4000,
      customWidth: '400px',
      design: 'modern',
      size: 'large',
    });
  }
  showBasicWarningMedium() {
    this.toastService.warning({
      title: 'Action Required',
      message: 'Incomplete fields.Please fill in all required information.',
      duration: 4000,
      customWidth: '350px',
      design: 'modern',
      size: 'medium',
    });
  }
  showBasicWarningSmall() {
    this.toastService.warning({
      title: 'Action Required',
      message: 'Incomplete fields.Please fill in all required information.',
      duration: 4000,
      customWidth: '300px',
      design: 'modern',
      size: 'small',
    });
  }

  //error

  showBasicErrorlarge() {
    this.toastService.error({
      title: 'Error Occurred',
      message: 'Connection error. Unable to connect to the server at present',
      duration: 4000,
      customWidth: '400px',
      design: 'modern',
      size: 'large',
    });
  }
  showBasicErrorMedium() {
    this.toastService.error({
      title: 'Error Occurred',
      message: 'Connection error. Unable to connect to the server at present',
      duration: 4000,
      customWidth: '350px',
      design: 'modern',
      size: 'medium',
    });
  }
  showBasicErrorSmall() {
    this.toastService.error({
      title: 'Error Occurred',
      message: 'Connection error. Unable to connect to the server at present',
      duration: 4000,
      customWidth: '300px',
      design: 'modern',
      size: 'small',
    });
  }

  //info

  showBasicInfoLarge() {
    this.toastService.info({
      title: 'Action Required',
      message: 'Incomplete fields. Please fill in all required information now',
      duration: 4000,
      customWidth: '400px',
      design: 'modern',
      size: 'large',
    });
  }
  showBasicInfoMedium() {
    this.toastService.info({
      title: 'Action Required',
      message: 'Incomplete fields. Please fill in all required information now',
      duration: 4000,
      customWidth: '350px',
      design: 'modern',
      size: 'medium',
    });
  }
  showBasicInfoSmall() {
    this.toastService.info({
      title: 'Action Required',
      message: 'Incomplete fields. Please fill in all required information now',
      duration: 4000,
      customWidth: '300px',
      design: 'modern',
      size: 'small',
    });
  }
}
